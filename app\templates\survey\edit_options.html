{% extends "base.html" %}

{% block title %}编辑选项 - 问题{% endblock %}

{% block extra_css %}
<style>
    .card {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border-radius: 0.5rem;
    }
    .option-item {
        margin-bottom: 1rem;
        padding: 1rem;
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        background-color: #f8f9fa;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .option-item:hover {
        background-color: #e9ecef;
    }
    .option-content {
        flex: 1;
        margin-right: 1rem;
    }
    .option-actions {
        white-space: nowrap;
    }
    .option-index {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #0d6efd;
        color: white;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 1rem;
    }
    .option-list-container {
        margin-top: 1.5rem;
        margin-bottom: 1.5rem;
    }
    .question-type-badge {
        font-size: 0.8rem;
        padding: 0.3rem 0.6rem;
        border-radius: 1rem;
    }
    .empty-option-state {
        text-align: center;
        padding: 2rem 1rem;
        border: 2px dashed #dee2e6;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
    #newOptionForm {
        border-top: 1px solid #dee2e6;
        margin-top: 1.5rem;
        padding-top: 1.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <!-- 页面标题和操作 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h2 mb-0"><i class="fas fa-list-ul me-2"></i>编辑选项</h1>
                <a href="{{ url_for('survey.edit_survey', survey_id=question.survey_id) }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i> 返回问卷编辑
                </a>
            </div>
            
            <!-- 问题信息 -->
            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="card-title">
                        问题内容
                        <span class="badge bg-info text-white question-type-badge ms-2">
                            {% if question.type == 'single_choice' %}
                            单选题
                            {% elif question.type == 'multiple_choice' %}
                            多选题
                            {% endif %}
                        </span>
                    </h5>
                    <p class="card-text">{{ question.content }}</p>
                </div>
            </div>
            
            <!-- 选项列表 -->
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title mb-3">选项列表</h5>
                    
                    {% if options %}
                    <div class="option-list-container" id="optionList">
                        {% for option in options %}
                        <div class="option-item" id="option-{{ option.id }}">
                            <div class="d-flex align-items-center option-content">
                                <div class="option-index">{{ loop.index }}</div>
                                <div>{{ option.content }}</div>
                            </div>
                            <div class="option-actions">
                                <button type="button" class="btn btn-outline-danger btn-sm" 
                                    onclick="deleteOption({{ option.id }})">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="empty-option-state" id="emptyOptionState">
                        <i class="fas fa-list fa-3x mb-3 text-muted"></i>
                        <h4>暂无选项</h4>
                        <p class="text-muted">使用下方表单添加选项</p>
                    </div>
                    {% endif %}
                    
                    <!-- 添加新选项表单 -->
                    <form id="newOptionForm" method="POST" action="{{ url_for('survey.edit_options', question_id=question.id) }}">
                        <h5 class="mb-3">添加新选项</h5>
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" id="optionContent" name="option_content" 
                                placeholder="请输入选项内容" required>
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-plus-circle me-1"></i> 添加
                            </button>
                        </div>
                        <div class="form-text">单击添加按钮保存选项，或按回车键快速添加</div>
                    </form>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="text-muted">
                            <i class="fas fa-info-circle me-1"></i> 
                            已添加 <span id="optionCount">{{ options|length }}</span> 个选项
                        </div>
                        <a href="{{ url_for('survey.view_survey', survey_id=question.survey_id) }}" class="btn btn-primary">
                            <i class="fas fa-eye me-1"></i> 预览问卷
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- 提示信息 -->
            <div class="alert alert-light mt-4">
                <h5><i class="fas fa-lightbulb me-2"></i>提示</h5>
                <ul>
                    <li>对于单选题，请确保至少添加两个选项</li>
                    <li>对于多选题，建议提供足够的选项供用户选择</li>
                    <li>选项的顺序将保持添加的顺序</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 删除选项
    function deleteOption(optionId) {
        if (!confirm('确定要删除此选项吗？')) {
            return;
        }
        
        fetch("{{ url_for('survey.edit_options', question_id=question.id) }}", {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `delete_option=${optionId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 移除选项元素
                document.getElementById(`option-${optionId}`).remove();
                
                // 更新选项计数
                const optionCount = document.getElementById('optionList').children.length;
                document.getElementById('optionCount').textContent = optionCount;
                
                // 如果没有选项了，显示空状态
                if (optionCount === 0) {
                    document.getElementById('optionList').style.display = 'none';
                    document.getElementById('emptyOptionState').style.display = 'block';
                } else {
                    // 重新编号
                    const optionItems = document.querySelectorAll('.option-item');
                    optionItems.forEach((item, index) => {
                        item.querySelector('.option-index').textContent = index + 1;
                    });
                }
                
                // 显示成功消息
                alert('选项已删除');
            } else {
                alert('删除失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('删除选项出错:', error);
            alert('操作失败，请重试');
        });
    }
    
    // 添加选项后重新加载页面
    document.getElementById('newOptionForm').addEventListener('submit', function(event) {
        // 标准的表单提交，页面会重新加载
    });
</script>
{% endblock %} 