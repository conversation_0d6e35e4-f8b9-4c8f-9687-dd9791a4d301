# Java FabCar contract sample

The directions for using this sample are documented in the Hyperledger Fabric
[Writing Your First Application](https://hyperledger-fabric.readthedocs.io/en/latest/write_first_app.html) tutorial.

The tutorial is based on JavaScript, however the same concepts are applicable when using Java.

To install and instantiate the Java version of `FabCar`, use the following command instead of the command shown in the [Launch the network](https://hyperledger-fabric.readthedocs.io/en/release-1.4/write_first_app.html#launch-the-network) section of the tutorial:

```
./startFabric.sh javascript
```

*NOTE:* After navigating to the documentation, choose the documentation version that matches your version of Fabric
