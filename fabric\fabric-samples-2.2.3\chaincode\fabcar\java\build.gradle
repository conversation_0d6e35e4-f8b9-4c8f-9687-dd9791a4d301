/*
 * SPDX-License-Identifier: Apache-2.0
 */

plugins {
    id 'application'
    id 'checkstyle'
    id 'jacoco'
}

group 'org.hyperledger.fabric.samples'
version '1.0-SNAPSHOT'

dependencies {
    compileOnly 'org.hyperledger.fabric-chaincode-java:fabric-chaincode-shim:2.+'
    implementation 'com.owlike:genson:1.5'
    testImplementation 'org.hyperledger.fabric-chaincode-java:fabric-chaincode-shim:2.+'
    testImplementation 'org.junit.jupiter:junit-jupiter:5.4.2'
    testImplementation 'org.assertj:assertj-core:3.11.1'
    testImplementation 'org.mockito:mockito-core:2.+'
}

repositories {
    maven {
        url "https://hyperledger.jfrog.io/hyperledger/fabric-maven"
    }
    jcenter()
    maven {
        url 'https://jitpack.io'
    }
}

application {
    mainClass = 'org.hyperledger.fabric.contract.ContractRouter'
}

checkstyle {
    toolVersion '8.21'
    configFile file("config/checkstyle/checkstyle.xml")
}

checkstyleMain {
    source ='src/main/java'
}

checkstyleTest {
    source ='src/test/java'
}

jacocoTestReport {
    dependsOn test
}

jacocoTestCoverageVerification {
    violationRules {
        rule {
            limit {
                minimum = 1.0
            }
        }
    }

    finalizedBy jacocoTestReport
}

test {
    useJUnitPlatform()
    testLogging {
        events "passed", "skipped", "failed"
    }
}

check.dependsOn jacocoTestCoverageVerification
installDist.dependsOn check
