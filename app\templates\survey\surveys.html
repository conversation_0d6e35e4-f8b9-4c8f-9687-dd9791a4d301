{% extends "base.html" %}

{% block title %}问卷列表 - 基于区块链的应急管理问卷调查系统{% endblock %}

{% block extra_css %}
<style>
    .survey-card {
        transition: transform 0.3s ease;
        height: 100%;
    }
    .survey-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .survey-date {
        font-size: 0.85rem;
        color: #6c757d;
    }
    .survey-description {
        color: #495057;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        height: 4.5rem;
    }
    .card-footer {
        background-color: transparent;
        border-top: none;
    }
    .no-surveys {
        padding: 60px 20px;
        text-align: center;
    }
    .survey-status {
        position: absolute;
        top: 10px;
        right: 10px;
    }
    .btn-primary {
        background-color: #0d6efd;
        border-color: #0d6efd;
    }
    .btn-outline-primary {
        color: #0d6efd;
        border-color: #0d6efd;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <header class="mb-4">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="display-5 fw-bold mb-2">问卷列表</h1>
                <p class="lead text-muted">探索可用的应急管理问卷，参与并提供您的宝贵意见</p>
            </div>
            <div class="col-md-4 text-md-end">
                {% if current_user.is_authenticated %}
                <a href="{{ url_for('survey.create_survey') }}" class="btn btn-primary">
                    <i class="fas fa-plus-circle me-1"></i> 创建新问卷
                </a>
                {% endif %}
            </div>
        </div>
        <hr>
    </header>

    {% if surveys %}
    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
        {% for survey in surveys %}
        <div class="col">
            <div class="card survey-card h-100">
                {% if survey.is_anonymous %}
                <div class="survey-status">
                    <span class="badge bg-success">匿名问卷</span>
                </div>
                {% endif %}
                <div class="card-body">
                    <h5 class="card-title fw-bold mb-2">{{ survey.title }}</h5>
                    <div class="survey-date mb-2">
                        <i class="far fa-calendar-alt me-1"></i> 创建于 {{ survey.created_at.strftime('%Y-%m-%d') }}
                        {% if survey.expiry_date %}
                        <span class="ms-2 badge bg-warning">截止日期: {{ survey.expiry_date.strftime('%Y-%m-%d') }}</span>
                        {% endif %}
                    </div>
                    <p class="card-text survey-description mb-3">{{ survey.description }}</p>
                    <div class="d-flex justify-content-between align-items-center mt-auto">
                        <div>
                            <small class="text-muted">
                                <i class="fas fa-question-circle me-1"></i> {{ survey.questions.count() }} 个问题
                            </small>
                        </div>
                        <div>
                            <small class="text-muted">
                                <i class="fas fa-users me-1"></i> {{ survey.responses.count() }} 人已参与
                            </small>
                        </div>
                    </div>
                </div>
                <div class="card-footer d-flex justify-content-between pt-0">
                    <a href="{{ url_for('survey.survey_results', survey_id=survey.id) }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-chart-bar me-1"></i> 查看结果
                    </a>
                    <a href="{{ url_for('survey.view_survey', survey_id=survey.id) }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-clipboard-check me-1"></i> 参与调查
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="no-surveys">
        <div class="text-center">
            <i class="fas fa-clipboard-list fa-4x mb-3 text-muted"></i>
            <h3>暂无问卷</h3>
            <p class="lead text-muted">目前没有活跃的问卷。{% if current_user.is_authenticated %}请点击"创建新问卷"按钮创建一个问卷。{% else %}登录后可创建问卷。{% endif %}</p>
            {% if not current_user.is_authenticated %}
            <a href="{{ url_for('auth.login') }}" class="btn btn-primary mt-3">
                <i class="fas fa-sign-in-alt me-1"></i> 登录
            </a>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <div class="mt-5">
        <div class="card bg-light">
            <div class="card-body">
                <h5 class="card-title"><i class="fas fa-shield-alt me-2"></i>区块链保障</h5>
                <p class="card-text">
                    我们的问卷系统采用区块链技术，确保您的回答在传输和存储过程中都经过加密和保护。
                    每份问卷提交后将生成唯一的数据指纹，并被安全地记录在区块链上，保证数据不可篡改。
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // 页面加载完成后的动画
        $('.survey-card').each(function(index) {
            $(this).delay(index * 100).animate({opacity: 1}, 500);
        });
    });
</script>
{% endblock %} 