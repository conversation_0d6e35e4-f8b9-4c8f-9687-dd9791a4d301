// 区块链图表初始化函数
// 这个文件包含了区块链信息页面所需的所有图表绘制函数
console.log("1111")
// 区块链结构可视化 - 2D图
window.initBlockchainChart = function() {
    try {
        var chartDom = document.getElementById('blockchainChart');
        if (!chartDom) {
            console.error('区块链结构图表容器不存在');
            return;
        }
        
        var myChart = echarts.init(chartDom);
        
        // 检查数据是否可用
        if (!window.blockchainData || !window.blockchainData.length) {
            chartDom.innerHTML = '<div class="alert alert-warning my-3 mx-4">' +
                '<i class="fas fa-exclamation-triangle me-2"></i>' +
                '无法加载区块链数据，图表无法显示' +
                '</div>';
            return;
        }
        
        // 准备图表数据
        var nodes = [];
        var links = [];
        
        // 添加节点
        for (var i = 0; i < window.blockchainData.length; i++) {
            var block = window.blockchainData[i];
            nodes.push({
                id: i.toString(),
                name: '区块 #' + block.index,
                value: block.hash ? block.hash.substring(0, 8) : 'N/A',
                symbolSize: 50,
                itemStyle: {
                    color: i === 0 ? '#91cc75' : '#5470c6'  // 创世区块为绿色
                }
            });
            
            // 添加连接
            if (i > 0) {
                links.push({
                    source: (i-1).toString(),
                    target: i.toString()
                });
            }
        }
        
        // 图表配置
        var option = {
            tooltip: {
                trigger: 'item',
                formatter: function(params) {
                    if (params.dataType === 'node') {
                        var block = window.blockchainData[parseInt(params.data.id)];
                        return '区块 #' + block.index + '<br/>' +
                               '哈希: ' + (block.hash ? block.hash.substring(0, 16) + '...' : 'N/A') + '<br/>' +
                               '时间: ' + (block.timestamp ? block.timestamp.replace('T', ' ').substring(0, 19) : 'N/A');
                    }
                    return '';
                }
            },
            legend: {
                data: ['区块']
            },
            series: [
                {
                    name: '区块链',
                    type: 'graph',
                    layout: 'circular',
                    data: nodes,
                    links: links,
                    roam: true,
                    label: {
                        show: true,
                        position: 'right',
                        formatter: '{b}'
                    },
                    lineStyle: {
                        color: '#5470c6',
                        width: 2,
                        curveness: 0.3
                    },
                    emphasis: {
                        focus: 'adjacency',
                        lineStyle: {
                            width: 4
                        }
                    }
                }
            ]
        };
        
        myChart.setOption(option);
        
        // 窗口大小改变时重绘图表
        window.addEventListener('resize', function() {
            myChart.resize();
        });
        
        console.log('区块链结构图表初始化成功');
    } catch (e) {
        console.error('初始化区块链结构图表失败:', e);
        if (chartDom) {
            chartDom.innerHTML = '<div class="alert alert-danger">' +
                '<i class="fas fa-exclamation-circle me-2"></i>' +
                '图表初始化失败: ' + e.message +
                '</div>';
        }
    }
};

// 区块链3D可视化
window.init3DBlockchainChart = function() {
    try {
        var chartDom = document.getElementById('blockchainChart3D');
        if (!chartDom) {
            console.error('3D图表容器不存在');
            return;
        }
        
        // 检查WebGL支持
        var canvas = document.createElement('canvas');
        var gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
        if (!gl) {
            console.error('浏览器不支持WebGL');
            throw new Error('浏览器不支持WebGL，无法显示3D图表');
        }
        
        var myChart = echarts.init(chartDom);
        
        // 检查数据是否可用
        if (!window.blockchainData || !window.blockchainData.length) {
            chartDom.innerHTML = '<div class="alert alert-warning my-3 mx-4">' +
                '<i class="fas fa-exclamation-triangle me-2"></i>' +
                '无法加载区块链数据，3D图表无法显示' +
                '</div>';
            return;
        }
        
        // 准备3D图表数据
        var data = [];
        var links = [];
        
        for (var i = 0; i < window.blockchainData.length; i++) {
            var block = window.blockchainData[i];
            
            // 计算节点在3D空间中的位置
            var x = i * 5;
            var y = Math.sin(i * 0.5) * 3;
            var z = Math.cos(i * 0.5) * 3;
            
            // 添加节点
            data.push({
                name: '区块 #' + block.index,
                value: [x, y, z],
                blockData: block
            });
            
            // 添加连接
            if (i > 0) {
                links.push({
                    source: i - 1,
                    target: i
                });
            }
        }
        
        option = {
            backgroundColor: '#0e1621',
            tooltip: {
                formatter: function(params) {
                    if (params.data.blockData) {
                        var block = params.data.blockData;
                        return '区块 #' + block.index + '<br/>' +
                               '哈希: ' + (block.hash ? block.hash.substring(0, 16) + '...' : 'N/A') + '<br/>' +
                               '时间: ' + (block.timestamp ? block.timestamp.replace('T', ' ').substring(0, 19) : 'N/A');
                    }
                    return '';
                }
            },
            xAxis3D: {
                type: 'value'
            },
            yAxis3D: {
                type: 'value'
            },
            zAxis3D: {
                type: 'value'
            },
            grid3D: {
                viewControl: {
                    projection: 'perspective',
                    autoRotate: true,
                    autoRotateSpeed: 10
                },
                environment: '#0e1621'
            },
            series: [{
                type: 'scatter3D',
                name: '区块',
                data: data,
                symbolSize: 15,
                itemStyle: {
                    color: function(params) {
                        // 创世区块为绿色，其他为蓝色
                        return params.dataIndex === 0 ? '#91cc75' : '#5470c6';
                    },
                    opacity: 0.8
                },
                emphasis: {
                    itemStyle: {
                        color: '#ff9800',
                        opacity: 1
                    }
                }
            }, {
                type: 'graph3D',
                data: data,
                links: links,
                lineStyle: {
                    color: '#4fc3f7',
                    width: 3,
                    opacity: 0.7
                }
            }]
        };
        
        myChart.setOption(option);
        
        // 窗口大小改变时重绘图表
        window.addEventListener('resize', function() {
            myChart.resize();
        });
        
        console.log('3D区块链图表初始化成功');
    } catch (e) {
        console.error('初始化3D区块链图表失败:', e);
        
        // 显示错误信息
        if (chartDom) {
            chartDom.innerHTML = '<div class="alert alert-warning">' +
                '<i class="fas fa-exclamation-triangle me-2"></i>' +
                '3D可视化无法加载: ' + e.message +
                '</div>';
        }
        
        // 显示备用2D可视化
        var fallback = document.getElementById('fallback2DVisualization');
        if (fallback) {
            fallback.style.display = 'block';
            
            // 生成静态区块链可视化
            var fallbackContent = document.getElementById('fallback2DContent');
            if (fallbackContent && window.blockchainData && window.blockchainData.length > 0) {
                var html = '';
                for (var i = 0; i < Math.min(window.blockchainData.length, 5); i++) {
                    var block = window.blockchainData[i];
                    html += '<div class="static-block">';
                    html += '<div class="static-block-index">' + block.index + '</div>';
                    html += '<div class="small">哈希: <span class="hash-text">' + 
                            (block.hash ? block.hash.substring(0, 8) + '...' : 'N/A') + '</span></div>';
                    html += '</div>';
                    
                    if (i < Math.min(window.blockchainData.length, 5) - 1) {
                        html += '<div class="static-arrow"><i class="fas fa-long-arrow-alt-right"></i></div>';
                    }
                }
                fallbackContent.innerHTML = html;
            }
        }
    }
};

// 区块时间分布图表
window.initTimeDistributionChart = function() {
    try {
        var chartDom = document.getElementById('timeDistributionChart');
        if (!chartDom) return;
        
        var myChart = echarts.init(chartDom);
        
        if (!window.blockchainData || window.blockchainData.length < 2) {
            chartDom.innerHTML = '<div class="alert alert-info p-2">数据不足，无法生成图表</div>';
            return;
        }
        
        // 准备数据
        var hours = {};
        for (var i = 0; i < 24; i++) {
            hours[i] = 0;
        }
        
        for (var i = 0; i < window.blockchainData.length; i++) {
            var block = window.blockchainData[i];
            if (block.timestamp) {
                var date = new Date(block.timestamp);
                var hour = date.getHours();
                hours[hour]++;
            }
        }
        
        var xData = [];
        var yData = [];
        for (var i = 0; i < 24; i++) {
            xData.push(i + '时');
            yData.push(hours[i]);
        }
        
        var option = {
            tooltip: {
                trigger: 'axis',
                formatter: '{b}: {c} 个区块'
            },
            xAxis: {
                type: 'category',
                data: xData
            },
            yAxis: {
                type: 'value'
            },
            series: [{
                data: yData,
                type: 'bar',
                showBackground: true,
                backgroundStyle: {
                    color: 'rgba(180, 180, 180, 0.2)'
                },
                itemStyle: {
                    color: '#5470c6'
                }
            }]
        };
        
        myChart.setOption(option);
        
        window.addEventListener('resize', function() {
            myChart.resize();
        });
    } catch (e) {
        console.error('初始化时间分布图表失败:', e);
    }
};

// 区块大小对比图表
window.initBlockSizeChart = function() {
    try {
        var chartDom = document.getElementById('blockSizeChart');
        if (!chartDom) return;
        
        var myChart = echarts.init(chartDom);
        
        if (!window.blockchainData || window.blockchainData.length < 2) {
            chartDom.innerHTML = '<div class="alert alert-info p-2">数据不足，无法生成图表</div>';
            return;
        }
        
        // 准备数据
        var xData = [];
        var yData = [];
        
        for (var i = 0; i < Math.min(window.blockchainData.length, 10); i++) {
            var block = window.blockchainData[i];
            var blockSize = 0;
            
            // 计算区块数据大小
            if (typeof block.data === 'string') {
                blockSize = block.data.length / 1024; // KB
            } else if (typeof block.data === 'object') {
                blockSize = JSON.stringify(block.data).length / 1024; // KB
            }
            
            xData.push('区块 #' + block.index);
            yData.push(blockSize.toFixed(2));
        }
        
        var option = {
            tooltip: {
                trigger: 'axis',
                formatter: '{b}<br/>{c} KB'
            },
            xAxis: {
                type: 'category',
                data: xData,
                axisLabel: {
                    interval: 0,
                    rotate: 30
                }
            },
            yAxis: {
                type: 'value',
                name: '大小 (KB)'
            },
            series: [{
                data: yData,
                type: 'bar',
                itemStyle: {
                    color: '#91cc75'
                }
            }]
        };
        
        myChart.setOption(option);
        
        window.addEventListener('resize', function() {
            myChart.resize();
        });
    } catch (e) {
        console.error('初始化区块大小图表失败:', e);
    }
};

// Nonce分布图表
window.initNonceDistributionChart = function() {
    try {
        var chartDom = document.getElementById('nonceDistributionChart');
        if (!chartDom) return;
        
        var myChart = echarts.init(chartDom);
        
        if (!window.blockchainData || window.blockchainData.length < 2) {
            chartDom.innerHTML = '<div class="alert alert-info p-2">数据不足，无法生成图表</div>';
            return;
        }
        
        // 准备数据
        var data = [];
        for (var i = 0; i < window.blockchainData.length; i++) {
            var block = window.blockchainData[i];
            if (block.nonce !== undefined) {
                data.push([block.index, block.nonce]);
            }
        }
        
        var option = {
            tooltip: {
                formatter: function(params) {
                    return '区块 #' + params.value[0] + '<br/>Nonce: ' + params.value[1];
                }
            },
            xAxis: {
                type: 'value',
                name: '区块编号'
            },
            yAxis: {
                type: 'value',
                name: 'Nonce值'
            },
            series: [{
                type: 'scatter',
                data: data,
                symbolSize: 10,
                itemStyle: {
                    color: '#ee6666'
                }
            }]
        };
        
        myChart.setOption(option);
        
        window.addEventListener('resize', function() {
            myChart.resize();
        });
    } catch (e) {
        console.error('初始化Nonce分布图表失败:', e);
    }
};

// 交易数量趋势图表
window.initTransactionTrendChart = function() {
    try {
        var chartDom = document.getElementById('transactionTrendChart');
        if (!chartDom) return;
        
        var myChart = echarts.init(chartDom);
        
        if (!window.blockchainData || window.blockchainData.length < 2) {
            chartDom.innerHTML = '<div class="alert alert-info p-2">数据不足，无法生成图表</div>';
            return;
        }
        
        // 准备数据
        var xData = [];
        var yData = [];
        
        for (var i = 0; i < window.blockchainData.length; i++) {
            var block = window.blockchainData[i];
            var transactionCount = 0;
            
            // 计算交易数量
            if (block.data) {
                if (typeof block.data === 'string') {
                    try {
                        var dataObj = JSON.parse(block.data);
                        if (Array.isArray(dataObj)) {
                            transactionCount = dataObj.length;
                        }
                    } catch (e) {
                        // 如果不是有效的JSON，尝试计算逗号数量来估计
                        var matches = block.data.match(/,/g);
                        transactionCount = matches ? matches.length + 1 : 1;
                    }
                } else if (Array.isArray(block.data)) {
                    transactionCount = block.data.length;
                }
            }
            
            xData.push('区块 #' + block.index);
            yData.push(transactionCount);
        }
        
        var option = {
            tooltip: {
                trigger: 'axis',
                formatter: '{b}<br/>交易数: {c}'
            },
            xAxis: {
                type: 'category',
                data: xData,
                axisLabel: {
                    interval: 0,
                    rotate: 30
                }
            },
            yAxis: {
                type: 'value',
                name: '交易数量'
            },
            series: [{
                data: yData,
                type: 'line',
                smooth: true,
                itemStyle: {
                    color: '#73c0de'
                },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                            offset: 0,
                            color: 'rgba(115, 192, 222, 0.5)'
                        },
                        {
                            offset: 1,
                            color: 'rgba(115, 192, 222, 0.1)'
                        }
                    ])
                }
            }]
        };
        
        myChart.setOption(option);
        
        window.addEventListener('resize', function() {
            myChart.resize();
        });
    } catch (e) {
        console.error('初始化交易趋势图表失败:', e);
    }
}; 