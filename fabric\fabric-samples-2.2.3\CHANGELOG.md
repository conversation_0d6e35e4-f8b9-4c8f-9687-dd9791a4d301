## "v2.1.0", "v2.0.1", "v2.0.0"

* [11e4c23](https://github.com/hyperledger/fabric-samples/commit/11e4c23) Update samples to use v2.0 or later dependencies (#161)
* [94beab7](https://github.com/hyperledger/fabric-samples/commit/94beab7) FABN-1531 Use v2.1.0 sdk-node modules
* [8820d2f](https://github.com/hyperledger/fabric-samples/commit/8820d2f) Fix commercial-paper README
* [aa9b577](https://github.com/hyperledger/fabric-samples/commit/aa9b577) Remove TLS enabled switch (#155)
* [381fb46](https://github.com/hyperledger/fabric-samples/commit/381fb46) upgraded abstore golang chaincode to use contract-api package (#154)
* [5e5d2c8](https://github.com/hyperledger/fabric-samples/commit/5e5d2c8) Update java chaincode to be compatible with doc and other implementations (#149)
* [c572c51](https://github.com/hyperledger/fabric-samples/commit/c572c51) Organize and Standardize `ci` Directory Content (#152)
* [aa40963](https://github.com/hyperledger/fabric-samples/commit/aa40963) Perform General Cleanup (#151)
* [da41afa](https://github.com/hyperledger/fabric-samples/commit/da41afa) Remove left over rm -rf command from BYFN (#148)
* [4bb48a9](https://github.com/hyperledger/fabric-samples/commit/4bb48a9) Jenkins no longer used (#145)
* [6f984e1](https://github.com/hyperledger/fabric-samples/commit/6f984e1) Bump acorn from 6.4.0 to 6.4.1 in /fabcar/javascript (#144)
* [b155620](https://github.com/hyperledger/fabric-samples/commit/b155620) Remove redundant invoke command from test network (#142)
* [851933b](https://github.com/hyperledger/fabric-samples/commit/851933b) Add enrollUser files to commercial paper (#140)
* [87600bd](https://github.com/hyperledger/fabric-samples/commit/87600bd) [FAB-17268](https://jira.hyperledger.org/browse/FAB-17268) Move fabcar sample to test network (#103)
* [9397788](https://github.com/hyperledger/fabric-samples/commit/9397788) Wrong groupId on hyperledger fabric dependencies for java-application (#134)
* [92555fb](https://github.com/hyperledger/fabric-samples/commit/92555fb) Update README.md (#133)
* [59c6641](https://github.com/hyperledger/fabric-samples/commit/59c6641) Change Download Location of Fabric Binaries (#143)
* [1f283fc](https://github.com/hyperledger/fabric-samples/commit/1f283fc) init function does not exist on fabcar (#141)
* [defb6bb](https://github.com/hyperledger/fabric-samples/commit/defb6bb) [FAB-17656](https://jira.hyperledger.org/browse/FAB-17656) echo Generating channel.tx (#139)
* [4c7bab0](https://github.com/hyperledger/fabric-samples/commit/4c7bab0) fix: package seletor REGEX (#135)
* [db69c6f](https://github.com/hyperledger/fabric-samples/commit/db69c6f) Add fabcar external service sample (#136)
* [7f5f5e6](https://github.com/hyperledger/fabric-samples/commit/7f5f5e6) [FAB-17504](https://jira.hyperledger.org/browse/FAB-17504) add Organizations.<Org>.OrdererEndpoints and remove Orderer.Addresses (#125)
* [f3fc08d](https://github.com/hyperledger/fabric-samples/commit/f3fc08d) Remove solo and kafka from test net configtx.yaml (#137)
* [e17574d](https://github.com/hyperledger/fabric-samples/commit/e17574d) Add CA's to docker test network (#124)
* [faac18e](https://github.com/hyperledger/fabric-samples/commit/faac18e) [FAB-17461](https://jira.hyperledger.org/browse/FAB-17461) Move off_chain_data sample to test network (#122)
* [121a44a](https://github.com/hyperledger/fabric-samples/commit/121a44a) [FAB-17460](https://jira.hyperledger.org/browse/FAB-17460) Move High Throughput sample to test network (#112)
* [a2f3a66](https://github.com/hyperledger/fabric-samples/commit/a2f3a66) Update docker image version
* [e5b898c](https://github.com/hyperledger/fabric-samples/commit/e5b898c) Revert "first-network/scripts/*: Make Chaincode name configurable (#118)" (#131)
* [9ef61e2](https://github.com/hyperledger/fabric-samples/commit/9ef61e2) first-network/scripts/*: Make Chaincode name configurable (#118)
* [e204ebb](https://github.com/hyperledger/fabric-samples/commit/e204ebb) Remove reference to 2.0 beta (#111)
* [3dbe116](https://github.com/hyperledger/fabric-samples/commit/3dbe116) [FAB-17456](https://jira.hyperledger.org/browse/FAB-17456) fabric-samples read ccp (#117)
* [965ed1f](https://github.com/hyperledger/fabric-samples/commit/965ed1f) [FAB-17498](https://jira.hyperledger.org/browse/FAB-17498) Beta Images removal, test test-network (#121)
* [403019e](https://github.com/hyperledger/fabric-samples/commit/403019e) [FAB-17495](https://jira.hyperledger.org/browse/FAB-17495) Remove Basic Network sample (#120)
* [883ef99](https://github.com/hyperledger/fabric-samples/commit/883ef99) [FAB-17457](https://jira.hyperledger.org/browse/FAB-17457) Script correction (#119)
* [b89ee34](https://github.com/hyperledger/fabric-samples/commit/b89ee34) Update Commercial Paper to v2.0 Lifecycle (#109)
* [4208644](https://github.com/hyperledger/fabric-samples/commit/4208644) [FAB-17478](https://jira.hyperledger.org/browse/FAB-17478) Update commercial paper to use go api v1.0.0 (#115)
* [0df5ed9](https://github.com/hyperledger/fabric-samples/commit/0df5ed9) [FAB-17477](https://jira.hyperledger.org/browse/FAB-17477) Update fabcar to use go api v1.0.0 (#116)
* [571733f](https://github.com/hyperledger/fabric-samples/commit/571733f) [FAB-17447](https://jira.hyperledger.org/browse/FAB-17447) Update to 2.0.0 Libraries
* [67b4ee7](https://github.com/hyperledger/fabric-samples/commit/67b4ee7) Add Org3 bugs in test network (#108)
* [5b93dd0](https://github.com/hyperledger/fabric-samples/commit/5b93dd0) [FAB-17140](https://jira.hyperledger.org/browse/FAB-17140) Add go commercial paper contract (#102)
* [4fe6a25](https://github.com/hyperledger/fabric-samples/commit/4fe6a25) [FABCI-482] Update Nexus URL's to Artifactory (#92)
* [1488fbb](https://github.com/hyperledger/fabric-samples/commit/1488fbb) Add 1.x versions of fabric to blacklisted versions
* [8ca279d](https://github.com/hyperledger/fabric-samples/commit/8ca279d) Add Support for Versioning NodeJS (#106)
* [b3b5267](https://github.com/hyperledger/fabric-samples/commit/b3b5267) [FAB-17243](https://jira.hyperledger.org/browse/FAB-17243) Add support for Fabric CA for Org3 on the (#91)
* [ce41ff7](https://github.com/hyperledger/fabric-samples/commit/ce41ff7) Remove references to vendoring chaincode from your gopath (#96)
* [4235d30](https://github.com/hyperledger/fabric-samples/commit/4235d30) [FAB-17306](https://jira.hyperledger.org/browse/FAB-17306) Fix artifact names in test-network (#97)
* [4c2a0a4](https://github.com/hyperledger/fabric-samples/commit/4c2a0a4) [FAB-16147](https://jira.hyperledger.org/browse/FAB-16147) Update Commercial Paper to work with v2 (#98)
* [6d9fd6f](https://github.com/hyperledger/fabric-samples/commit/6d9fd6f) Remove Gerrit reference
* [a026a4f](https://github.com/hyperledger/fabric-samples/commit/a026a4f) Fixed typo (#90)
* [cdb0e8b](https://github.com/hyperledger/fabric-samples/commit/cdb0e8b) TYPO (#89)
* [94ac8b6](https://github.com/hyperledger/fabric-samples/commit/94ac8b6) Update to use beta levels of modules (#88)
* [d848633](https://github.com/hyperledger/fabric-samples/commit/d848633) [FAB-16844](https://jira.hyperledger.org/browse/FAB-16844) Correct BYFN CC name
* [73267e1](https://github.com/hyperledger/fabric-samples/commit/73267e1) Fix test network bugs for adding org3
* [5d58254](https://github.com/hyperledger/fabric-samples/commit/5d58254) [FAB-17145](https://jira.hyperledger.org/browse/FAB-17145) Add test network to Fabric Samples
* [e9f2957](https://github.com/hyperledger/fabric-samples/commit/e9f2957) [FAB-17062](https://jira.hyperledger.org/browse/FAB-17062) Fix typos in Commercial Paper readme
* [36694d0](https://github.com/hyperledger/fabric-samples/commit/36694d0) [FAB-17121](https://jira.hyperledger.org/browse/FAB-17121) Use new bootstrap config in orderer
* [429f087](https://github.com/hyperledger/fabric-samples/commit/429f087) update fabcar go to new programming model
* [1467086](https://github.com/hyperledger/fabric-samples/commit/1467086) Bump eslint-utils
* [33f349a](https://github.com/hyperledger/fabric-samples/commit/33f349a) Remove Stalebot
* [6af43bf](https://github.com/hyperledger/fabric-samples/commit/6af43bf) Change stalebot settings
* [4880401](https://github.com/hyperledger/fabric-samples/commit/4880401) [FAB-16284](https://jira.hyperledger.org/browse/FAB-16284) Remove E2E file and -f option from BYFN
* [fe96f60](https://github.com/hyperledger/fabric-samples/commit/fe96f60) [FAB-16850](https://jira.hyperledger.org/browse/FAB-16850) Set up CI with Azure Pipelines
* [81aabf4](https://github.com/hyperledger/fabric-samples/commit/81aabf4) [FAB-16849](https://jira.hyperledger.org/browse/FAB-16849) Various updates for Java version of FabCar
* [a42b858](https://github.com/hyperledger/fabric-samples/commit/a42b858) Update FabCar to reflect wallet API changes
* [890f9ea](https://github.com/hyperledger/fabric-samples/commit/890f9ea) [FAB-16713](https://jira.hyperledger.org/browse/FAB-16713) Fix npm audit warnings
* [e48e804](https://github.com/hyperledger/fabric-samples/commit/e48e804) [FAB-16776](https://jira.hyperledger.org/browse/FAB-16776) Move BYFN up to V2_0 capabilities
* [7b65a25](https://github.com/hyperledger/fabric-samples/commit/7b65a25) [IN-68] Add default GitHub SECURITY policy
* [408e0e8](https://github.com/hyperledger/fabric-samples/commit/408e0e8) [FAB-16619](https://jira.hyperledger.org/browse/FAB-16619) Fix the policy warning
* [670d446](https://github.com/hyperledger/fabric-samples/commit/670d446) [FAB-16668](https://jira.hyperledger.org/browse/FAB-16668) fabcar chaincode modify console output
* [f2939e2](https://github.com/hyperledger/fabric-samples/commit/f2939e2) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for Commercial Paper sample
* [3d19014](https://github.com/hyperledger/fabric-samples/commit/3d19014) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for FabCar sample
* [e2b7cb7](https://github.com/hyperledger/fabric-samples/commit/e2b7cb7) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for abstore sample
* [db48612](https://github.com/hyperledger/fabric-samples/commit/db48612) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Increase chaincode execute timeout
* [521a7ff](https://github.com/hyperledger/fabric-samples/commit/521a7ff) [FAB-16607](https://jira.hyperledger.org/browse/FAB-16607) Update FabCar to reflect CC updates
* [c13a5ec](https://github.com/hyperledger/fabric-samples/commit/c13a5ec) [FAB-16528](https://jira.hyperledger.org/browse/FAB-16528) marbles private chaincode sync up
* [3fad853](https://github.com/hyperledger/fabric-samples/commit/3fad853) [FAB-16528](https://jira.hyperledger.org/browse/FAB-16528) marbles private chaincode sync up
* [8b9b82f](https://github.com/hyperledger/fabric-samples/commit/8b9b82f) [FAB-16489](https://jira.hyperledger.org/browse/FAB-16489) Add CODEOWNERS
* [a6ce915](https://github.com/hyperledger/fabric-samples/commit/a6ce915) [FAB-16487](https://jira.hyperledger.org/browse/FAB-16487) Update eslint
* [48082cf](https://github.com/hyperledger/fabric-samples/commit/48082cf) [FAB-16362](https://jira.hyperledger.org/browse/FAB-16362) adding chaincode excution comments
* [1d379f3](https://github.com/hyperledger/fabric-samples/commit/1d379f3) [FAB-16474](https://jira.hyperledger.org/browse/FAB-16474) marbles02 chaincode error
* [18712ca](https://github.com/hyperledger/fabric-samples/commit/18712ca) [FAB-16133](https://jira.hyperledger.org/browse/FAB-16133) Remove Solo consensus from BYFN
* [91c720a](https://github.com/hyperledger/fabric-samples/commit/91c720a) [FAB-16390](https://jira.hyperledger.org/browse/FAB-16390) Added filter for invalid transactions
* [1d3e267](https://github.com/hyperledger/fabric-samples/commit/1d3e267) Redirect samples to fabric-{chaincode,protos}-go
* [398a5b1](https://github.com/hyperledger/fabric-samples/commit/398a5b1) [FABCI-394] Remove AnsiColor Wrapper
* [ce154e0](https://github.com/hyperledger/fabric-samples/commit/ce154e0) [FAB-16310](https://jira.hyperledger.org/browse/FAB-16310) Vendor Go dependencies in all samples
* [6ea7c71](https://github.com/hyperledger/fabric-samples/commit/6ea7c71) [FAB-16285](https://jira.hyperledger.org/browse/FAB-16285) Update blacklisted versions in BYFN
* [86cd831](https://github.com/hyperledger/fabric-samples/commit/86cd831) [FAB-16284](https://jira.hyperledger.org/browse/FAB-16284) Remove E2E file and -f option from BYFN
* [0063abe](https://github.com/hyperledger/fabric-samples/commit/0063abe) Update stale script name in interest rate swaps
* [3907507](https://github.com/hyperledger/fabric-samples/commit/3907507) [FAB-16277](https://jira.hyperledger.org/browse/FAB-16277) Update BYFN w/ Raft ports in Docker network
* [33b0065](https://github.com/hyperledger/fabric-samples/commit/33b0065) [FAB-14813](https://jira.hyperledger.org/browse/FAB-14813) Channel event sample in fabric-samples
* [b62d5bd](https://github.com/hyperledger/fabric-samples/commit/b62d5bd) [FAB-16132](https://jira.hyperledger.org/browse/FAB-16132) Remove Kafka consensus from BYFN
* [9b14525](https://github.com/hyperledger/fabric-samples/commit/9b14525) [FAB-15213](https://jira.hyperledger.org/browse/FAB-15213) Update Commercial Paper for Java
* [4158877](https://github.com/hyperledger/fabric-samples/commit/4158877) [FAB-16232](https://jira.hyperledger.org/browse/FAB-16232) Remove FabToken sample
* [b6380cc](https://github.com/hyperledger/fabric-samples/commit/b6380cc) [FAB-16198](https://jira.hyperledger.org/browse/FAB-16198) Run "go mod vendor" for FabCar Go contract
* [639848a](https://github.com/hyperledger/fabric-samples/commit/639848a) [FAB-16197](https://jira.hyperledger.org/browse/FAB-16197) Add connection profiles to .gitignore
* [3996db5](https://github.com/hyperledger/fabric-samples/commit/3996db5) [FAB-12219](https://jira.hyperledger.org/browse/FAB-12219) abstore node -> javascript
* [14ac271](https://github.com/hyperledger/fabric-samples/commit/14ac271) [FAB-12219](https://jira.hyperledger.org/browse/FAB-12219) marbles02 node -> javascript
* [13f16e5](https://github.com/hyperledger/fabric-samples/commit/13f16e5) [FGJ-4] CI tests for FabCar Java sample
* [171a7d2](https://github.com/hyperledger/fabric-samples/commit/171a7d2) FGJ-4 Fabcar sample
* [868f9d0](https://github.com/hyperledger/fabric-samples/commit/868f9d0) [FAB-15625](https://jira.hyperledger.org/browse/FAB-15625) Add UT for Simple Asset Chaincode
* [597d150](https://github.com/hyperledger/fabric-samples/commit/597d150) [FAB-14532](https://jira.hyperledger.org/browse/FAB-14532) Remove LL FabCar sample
* [583ff8f](https://github.com/hyperledger/fabric-samples/commit/583ff8f) Use renamed CheckCommitReadiness function
* [750f937](https://github.com/hyperledger/fabric-samples/commit/750f937) [FAB-15213](https://jira.hyperledger.org/browse/FAB-15213) Add Java FabCar sample contract
* [abbda95](https://github.com/hyperledger/fabric-samples/commit/abbda95) [FAB-15897](https://jira.hyperledger.org/browse/FAB-15897) Improve FabCar test logging
* [dd8150a](https://github.com/hyperledger/fabric-samples/commit/dd8150a) [FAB-15104](https://jira.hyperledger.org/browse/FAB-15104) Remove versions from fabric-samples readme
* [1387aa8](https://github.com/hyperledger/fabric-samples/commit/1387aa8) [FAB-15927](https://jira.hyperledger.org/browse/FAB-15927)  Better expression for golang
* [61c33d3](https://github.com/hyperledger/fabric-samples/commit/61c33d3) [FAB-15973](https://jira.hyperledger.org/browse/FAB-15973) use --output json on simulatecommit
* [8bbdd0f](https://github.com/hyperledger/fabric-samples/commit/8bbdd0f) [FAB-15716](https://jira.hyperledger.org/browse/FAB-15716) Fix instructions for dev-mode
* [0254d67](https://github.com/hyperledger/fabric-samples/commit/0254d67) QueryApprovalStatus -> SimulateCommitChaincodeDef
* [c57d67c](https://github.com/hyperledger/fabric-samples/commit/c57d67c) [FAB-15782](https://jira.hyperledger.org/browse/FAB-15782) Sample Go CC should include deps
* [6ba5a19](https://github.com/hyperledger/fabric-samples/commit/6ba5a19) Update to Go 1.12.5 in ci.properties
* [1774a25](https://github.com/hyperledger/fabric-samples/commit/1774a25) [FAB-15723](https://jira.hyperledger.org/browse/FAB-15723) Fix script and instruction with ccenv
* [6ae711c](https://github.com/hyperledger/fabric-samples/commit/6ae711c) [FAB-15717](https://jira.hyperledger.org/browse/FAB-15717) fix Error Unexpected end of JSON input
* [5be56d3](https://github.com/hyperledger/fabric-samples/commit/5be56d3) [FAB-15104](https://jira.hyperledger.org/browse/FAB-15104) Remove scripts/bootstrap.sh
* [779f8f3](https://github.com/hyperledger/fabric-samples/commit/779f8f3) [FAB-15649](https://jira.hyperledger.org/browse/FAB-15649)Fix Fabcar to install Chaincode on all peers
* [7c5f5d3](https://github.com/hyperledger/fabric-samples/commit/7c5f5d3) [FAB-15199](https://jira.hyperledger.org/browse/FAB-15199) Update interest rate sample
* [f0dca20](https://github.com/hyperledger/fabric-samples/commit/f0dca20) [FAB-14532](https://jira.hyperledger.org/browse/FAB-14532) Remove LL FabCar sample
* [1ed1a10](https://github.com/hyperledger/fabric-samples/commit/1ed1a10) [FAB-15573](https://jira.hyperledger.org/browse/FAB-15573) Fix typo in fabric-samples-ci.md
* [2e7fec9](https://github.com/hyperledger/fabric-samples/commit/2e7fec9) [FAB-15601](https://jira.hyperledger.org/browse/FAB-15601) BYFN: Fix MAX_RETRY for couchdb
* [1e9e4c4](https://github.com/hyperledger/fabric-samples/commit/1e9e4c4) [FAB-9329](https://jira.hyperledger.org/browse/FAB-9329) Remove the unused variable in BYFN/EYFN
* [964c09f](https://github.com/hyperledger/fabric-samples/commit/964c09f) [FAB-15601](https://jira.hyperledger.org/browse/FAB-15601) BYFN: Fix MAX_RETRY for couchdb
* [41dca99](https://github.com/hyperledger/fabric-samples/commit/41dca99) [FAB-15127](https://jira.hyperledger.org/browse/FAB-15127) Update high throughput sample
* [3fe014a](https://github.com/hyperledger/fabric-samples/commit/3fe014a) Use official CouchDB image
* [f2d0fa0](https://github.com/hyperledger/fabric-samples/commit/f2d0fa0) [FAB-14487](https://jira.hyperledger.org/browse/FAB-14487) Make FabCar use BYFN, not basic-network
* [e9c3649](https://github.com/hyperledger/fabric-samples/commit/e9c3649) [FAB-15276](https://jira.hyperledger.org/browse/FAB-15276) Fix license statements
* [fbe4036](https://github.com/hyperledger/fabric-samples/commit/fbe4036) [FAB-14486](https://jira.hyperledger.org/browse/FAB-14486) Extend BYFN to opt skip chaincode deploy
* [0c4141f](https://github.com/hyperledger/fabric-samples/commit/0c4141f) [FAB-14485](https://jira.hyperledger.org/browse/FAB-14485) Extend BYFN to opt inc cert authorities
* [529b83b](https://github.com/hyperledger/fabric-samples/commit/529b83b) [FAB-14330](https://jira.hyperledger.org/browse/FAB-14330) Add connection profiles for BYFN and EYFN
* [2c21c83](https://github.com/hyperledger/fabric-samples/commit/2c21c83) [FABN-1184] Update fabtoken/README.md
* [5056a23](https://github.com/hyperledger/fabric-samples/commit/5056a23) [FABN-1184] Add CI script for fabtoken sample app
* [5d6db95](https://github.com/hyperledger/fabric-samples/commit/5d6db95) Update maintainers for fabric-samples
* [f527815](https://github.com/hyperledger/fabric-samples/commit/f527815) [FAB-15119](https://jira.hyperledger.org/browse/FAB-15119) Fix BYFN with Java chaincode
* [8245252](https://github.com/hyperledger/fabric-samples/commit/8245252) [FABN-1184] Implement fabtoken sample app
* [1bd1c2f](https://github.com/hyperledger/fabric-samples/commit/1bd1c2f) FABCI-284 Update CI Pipeline script
* [c24abf9](https://github.com/hyperledger/fabric-samples/commit/c24abf9) [FAB-15022](https://jira.hyperledger.org/browse/FAB-15022) Basic-network support for new lifecycle
* [b64fd45](https://github.com/hyperledger/fabric-samples/commit/b64fd45) [FAB-15051](https://jira.hyperledger.org/browse/FAB-15051) delStandard() function for high-throughput
* [3e68a7e](https://github.com/hyperledger/fabric-samples/commit/3e68a7e) [FAB-14784](https://jira.hyperledger.org/browse/FAB-14784) Remove balance-transfer
* [eb3fe08](https://github.com/hyperledger/fabric-samples/commit/eb3fe08) [FAB-14779](https://jira.hyperledger.org/browse/FAB-14779) QueryApprovalStatus step in byfn
* [2777429](https://github.com/hyperledger/fabric-samples/commit/2777429) [FAB-14711](https://jira.hyperledger.org/browse/FAB-14711) update byfn with new lifecycle
* [aec3389](https://github.com/hyperledger/fabric-samples/commit/aec3389) [FAB-12215](https://jira.hyperledger.org/browse/FAB-12215)WYFA:Remove chainId in tx proposal request
* [b5d5026](https://github.com/hyperledger/fabric-samples/commit/b5d5026) [FAB-14633](https://jira.hyperledger.org/browse/FAB-14633) Remove apt-get from eyfn.sh
* [efaadd3](https://github.com/hyperledger/fabric-samples/commit/efaadd3) [FAB-14531](https://jira.hyperledger.org/browse/FAB-14531) BYFN Raft with 5 nodes
* [d63047c](https://github.com/hyperledger/fabric-samples/commit/d63047c) [FAB-14444](https://jira.hyperledger.org/browse/FAB-14444)
* [7e3d428](https://github.com/hyperledger/fabric-samples/commit/7e3d428) [FAB-14369](https://jira.hyperledger.org/browse/FAB-14369)Fix dev mode failing to build Chaincode
* [420ba23](https://github.com/hyperledger/fabric-samples/commit/420ba23) [FAB-12762](https://jira.hyperledger.org/browse/FAB-12762) Add etcd/raft consensus option to BYFN
* [2b68c80](https://github.com/hyperledger/fabric-samples/commit/2b68c80) [FAB-14317](https://jira.hyperledger.org/browse/FAB-14317) Add default policies to org3
* [f942010](https://github.com/hyperledger/fabric-samples/commit/f942010) [FAB-14268](https://jira.hyperledger.org/browse/FAB-14268) Make BYFN/EYFN ports match external ports
* [4e2ce23](https://github.com/hyperledger/fabric-samples/commit/4e2ce23) [FAB-14271](https://jira.hyperledger.org/browse/FAB-14271) Add channel policies to channel config
* [f26477c](https://github.com/hyperledger/fabric-samples/commit/f26477c) [FAB-11796](https://jira.hyperledger.org/browse/FAB-11796)high-throughput:Remove unnecessary prunesafe
* [137327a](https://github.com/hyperledger/fabric-samples/commit/137327a) [FAB-14162](https://jira.hyperledger.org/browse/FAB-14162) Pin fabric-samples in master to "unstable"
* [6007c09](https://github.com/hyperledger/fabric-samples/commit/6007c09) [FAB-13862](https://jira.hyperledger.org/browse/FAB-13862) Rename example02 ABstore
* [94cb603](https://github.com/hyperledger/fabric-samples/commit/94cb603) [FAB-13933](https://jira.hyperledger.org/browse/FAB-13933) Fix misspellings
* [a8a5539](https://github.com/hyperledger/fabric-samples/commit/a8a5539) Fix doc link Fix variable error
* [b0cda61](https://github.com/hyperledger/fabric-samples/commit/b0cda61) [FAB-13769](https://jira.hyperledger.org/browse/FAB-13769) Add UT code to ABAC sample Chaincode
* [c7438e1](https://github.com/hyperledger/fabric-samples/commit/c7438e1) [FAB-13668](https://jira.hyperledger.org/browse/FAB-13668) BYFN's container volume mapping is bad
* [e48b2de](https://github.com/hyperledger/fabric-samples/commit/e48b2de) [FAB-13489](https://jira.hyperledger.org/browse/FAB-13489) fabric-samples add error msg
* [6269941](https://github.com/hyperledger/fabric-samples/commit/6269941) Correct broken link

## "v1.4.6"

* [11e4c23](https://github.com/hyperledger/fabric-samples/commit/11e4c23) Update samples to use v2.0 or later dependencies (#161)
* [94beab7](https://github.com/hyperledger/fabric-samples/commit/94beab7) FABN-1531 Use v2.1.0 sdk-node modules
* [8820d2f](https://github.com/hyperledger/fabric-samples/commit/8820d2f) Fix commercial-paper README
* [aa9b577](https://github.com/hyperledger/fabric-samples/commit/aa9b577) Remove TLS enabled switch (#155)
* [381fb46](https://github.com/hyperledger/fabric-samples/commit/381fb46) upgraded abstore golang chaincode to use contract-api package (#154)
* [5e5d2c8](https://github.com/hyperledger/fabric-samples/commit/5e5d2c8) Update java chaincode to be compatible with doc and other implementations (#149)
* [c572c51](https://github.com/hyperledger/fabric-samples/commit/c572c51) Organize and Standardize `ci` Directory Content (#152)
* [aa40963](https://github.com/hyperledger/fabric-samples/commit/aa40963) Perform General Cleanup (#151)
* [da41afa](https://github.com/hyperledger/fabric-samples/commit/da41afa) Remove left over rm -rf command from BYFN (#148)
* [4bb48a9](https://github.com/hyperledger/fabric-samples/commit/4bb48a9) Jenkins no longer used (#145)
* [6f984e1](https://github.com/hyperledger/fabric-samples/commit/6f984e1) Bump acorn from 6.4.0 to 6.4.1 in /fabcar/javascript (#144)
* [b155620](https://github.com/hyperledger/fabric-samples/commit/b155620) Remove redundant invoke command from test network (#142)
* [851933b](https://github.com/hyperledger/fabric-samples/commit/851933b) Add enrollUser files to commercial paper (#140)
* [87600bd](https://github.com/hyperledger/fabric-samples/commit/87600bd) [FAB-17268](https://jira.hyperledger.org/browse/FAB-17268) Move fabcar sample to test network (#103)
* [9397788](https://github.com/hyperledger/fabric-samples/commit/9397788) Wrong groupId on hyperledger fabric dependencies for java-application (#134)
* [92555fb](https://github.com/hyperledger/fabric-samples/commit/92555fb) Update README.md (#133)
* [59c6641](https://github.com/hyperledger/fabric-samples/commit/59c6641) Change Download Location of Fabric Binaries (#143)
* [1f283fc](https://github.com/hyperledger/fabric-samples/commit/1f283fc) init function does not exist on fabcar (#141)
* [defb6bb](https://github.com/hyperledger/fabric-samples/commit/defb6bb) [FAB-17656](https://jira.hyperledger.org/browse/FAB-17656) echo Generating channel.tx (#139)
* [4c7bab0](https://github.com/hyperledger/fabric-samples/commit/4c7bab0) fix: package seletor REGEX (#135)
* [db69c6f](https://github.com/hyperledger/fabric-samples/commit/db69c6f) Add fabcar external service sample (#136)
* [7f5f5e6](https://github.com/hyperledger/fabric-samples/commit/7f5f5e6) [FAB-17504](https://jira.hyperledger.org/browse/FAB-17504) add Organizations.<Org>.OrdererEndpoints and remove Orderer.Addresses (#125)
* [f3fc08d](https://github.com/hyperledger/fabric-samples/commit/f3fc08d) Remove solo and kafka from test net configtx.yaml (#137)
* [e17574d](https://github.com/hyperledger/fabric-samples/commit/e17574d) Add CA's to docker test network (#124)
* [faac18e](https://github.com/hyperledger/fabric-samples/commit/faac18e) [FAB-17461](https://jira.hyperledger.org/browse/FAB-17461) Move off_chain_data sample to test network (#122)
* [121a44a](https://github.com/hyperledger/fabric-samples/commit/121a44a) [FAB-17460](https://jira.hyperledger.org/browse/FAB-17460) Move High Throughput sample to test network (#112)
* [a2f3a66](https://github.com/hyperledger/fabric-samples/commit/a2f3a66) Update docker image version
* [e5b898c](https://github.com/hyperledger/fabric-samples/commit/e5b898c) Revert "first-network/scripts/*: Make Chaincode name configurable (#118)" (#131)
* [9ef61e2](https://github.com/hyperledger/fabric-samples/commit/9ef61e2) first-network/scripts/*: Make Chaincode name configurable (#118)
* [e204ebb](https://github.com/hyperledger/fabric-samples/commit/e204ebb) Remove reference to 2.0 beta (#111)
* [3dbe116](https://github.com/hyperledger/fabric-samples/commit/3dbe116) [FAB-17456](https://jira.hyperledger.org/browse/FAB-17456) fabric-samples read ccp (#117)
* [965ed1f](https://github.com/hyperledger/fabric-samples/commit/965ed1f) [FAB-17498](https://jira.hyperledger.org/browse/FAB-17498) Beta Images removal, test test-network (#121)
* [403019e](https://github.com/hyperledger/fabric-samples/commit/403019e) [FAB-17495](https://jira.hyperledger.org/browse/FAB-17495) Remove Basic Network sample (#120)
* [883ef99](https://github.com/hyperledger/fabric-samples/commit/883ef99) [FAB-17457](https://jira.hyperledger.org/browse/FAB-17457) Script correction (#119)
* [b89ee34](https://github.com/hyperledger/fabric-samples/commit/b89ee34) Update Commercial Paper to v2.0 Lifecycle (#109)
* [4208644](https://github.com/hyperledger/fabric-samples/commit/4208644) [FAB-17478](https://jira.hyperledger.org/browse/FAB-17478) Update commercial paper to use go api v1.0.0 (#115)
* [0df5ed9](https://github.com/hyperledger/fabric-samples/commit/0df5ed9) [FAB-17477](https://jira.hyperledger.org/browse/FAB-17477) Update fabcar to use go api v1.0.0 (#116)
* [571733f](https://github.com/hyperledger/fabric-samples/commit/571733f) [FAB-17447](https://jira.hyperledger.org/browse/FAB-17447) Update to 2.0.0 Libraries
* [67b4ee7](https://github.com/hyperledger/fabric-samples/commit/67b4ee7) Add Org3 bugs in test network (#108)
* [5b93dd0](https://github.com/hyperledger/fabric-samples/commit/5b93dd0) [FAB-17140](https://jira.hyperledger.org/browse/FAB-17140) Add go commercial paper contract (#102)
* [4fe6a25](https://github.com/hyperledger/fabric-samples/commit/4fe6a25) [FABCI-482] Update Nexus URL's to Artifactory (#92)
* [1488fbb](https://github.com/hyperledger/fabric-samples/commit/1488fbb) Add 1.x versions of fabric to blacklisted versions
* [8ca279d](https://github.com/hyperledger/fabric-samples/commit/8ca279d) Add Support for Versioning NodeJS (#106)
* [b3b5267](https://github.com/hyperledger/fabric-samples/commit/b3b5267) [FAB-17243](https://jira.hyperledger.org/browse/FAB-17243) Add support for Fabric CA for Org3 on the (#91)
* [ce41ff7](https://github.com/hyperledger/fabric-samples/commit/ce41ff7) Remove references to vendoring chaincode from your gopath (#96)
* [4235d30](https://github.com/hyperledger/fabric-samples/commit/4235d30) [FAB-17306](https://jira.hyperledger.org/browse/FAB-17306) Fix artifact names in test-network (#97)
* [4c2a0a4](https://github.com/hyperledger/fabric-samples/commit/4c2a0a4) [FAB-16147](https://jira.hyperledger.org/browse/FAB-16147) Update Commercial Paper to work with v2 (#98)
* [6d9fd6f](https://github.com/hyperledger/fabric-samples/commit/6d9fd6f) Remove Gerrit reference
* [a026a4f](https://github.com/hyperledger/fabric-samples/commit/a026a4f) Fixed typo (#90)
* [cdb0e8b](https://github.com/hyperledger/fabric-samples/commit/cdb0e8b) TYPO (#89)
* [94ac8b6](https://github.com/hyperledger/fabric-samples/commit/94ac8b6) Update to use beta levels of modules (#88)
* [d848633](https://github.com/hyperledger/fabric-samples/commit/d848633) [FAB-16844](https://jira.hyperledger.org/browse/FAB-16844) Correct BYFN CC name
* [73267e1](https://github.com/hyperledger/fabric-samples/commit/73267e1) Fix test network bugs for adding org3
* [5d58254](https://github.com/hyperledger/fabric-samples/commit/5d58254) [FAB-17145](https://jira.hyperledger.org/browse/FAB-17145) Add test network to Fabric Samples
* [e9f2957](https://github.com/hyperledger/fabric-samples/commit/e9f2957) [FAB-17062](https://jira.hyperledger.org/browse/FAB-17062) Fix typos in Commercial Paper readme
* [36694d0](https://github.com/hyperledger/fabric-samples/commit/36694d0) [FAB-17121](https://jira.hyperledger.org/browse/FAB-17121) Use new bootstrap config in orderer
* [429f087](https://github.com/hyperledger/fabric-samples/commit/429f087) update fabcar go to new programming model
* [1467086](https://github.com/hyperledger/fabric-samples/commit/1467086) Bump eslint-utils
* [33f349a](https://github.com/hyperledger/fabric-samples/commit/33f349a) Remove Stalebot
* [6af43bf](https://github.com/hyperledger/fabric-samples/commit/6af43bf) Change stalebot settings
* [4880401](https://github.com/hyperledger/fabric-samples/commit/4880401) [FAB-16284](https://jira.hyperledger.org/browse/FAB-16284) Remove E2E file and -f option from BYFN
* [fe96f60](https://github.com/hyperledger/fabric-samples/commit/fe96f60) [FAB-16850](https://jira.hyperledger.org/browse/FAB-16850) Set up CI with Azure Pipelines
* [81aabf4](https://github.com/hyperledger/fabric-samples/commit/81aabf4) [FAB-16849](https://jira.hyperledger.org/browse/FAB-16849) Various updates for Java version of FabCar
* [a42b858](https://github.com/hyperledger/fabric-samples/commit/a42b858) Update FabCar to reflect wallet API changes
* [890f9ea](https://github.com/hyperledger/fabric-samples/commit/890f9ea) [FAB-16713](https://jira.hyperledger.org/browse/FAB-16713) Fix npm audit warnings
* [e48e804](https://github.com/hyperledger/fabric-samples/commit/e48e804) [FAB-16776](https://jira.hyperledger.org/browse/FAB-16776) Move BYFN up to V2_0 capabilities
* [7b65a25](https://github.com/hyperledger/fabric-samples/commit/7b65a25) [IN-68] Add default GitHub SECURITY policy
* [408e0e8](https://github.com/hyperledger/fabric-samples/commit/408e0e8) [FAB-16619](https://jira.hyperledger.org/browse/FAB-16619) Fix the policy warning
* [670d446](https://github.com/hyperledger/fabric-samples/commit/670d446) [FAB-16668](https://jira.hyperledger.org/browse/FAB-16668) fabcar chaincode modify console output
* [f2939e2](https://github.com/hyperledger/fabric-samples/commit/f2939e2) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for Commercial Paper sample
* [3d19014](https://github.com/hyperledger/fabric-samples/commit/3d19014) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for FabCar sample
* [e2b7cb7](https://github.com/hyperledger/fabric-samples/commit/e2b7cb7) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for abstore sample
* [db48612](https://github.com/hyperledger/fabric-samples/commit/db48612) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Increase chaincode execute timeout
* [521a7ff](https://github.com/hyperledger/fabric-samples/commit/521a7ff) [FAB-16607](https://jira.hyperledger.org/browse/FAB-16607) Update FabCar to reflect CC updates
* [c13a5ec](https://github.com/hyperledger/fabric-samples/commit/c13a5ec) [FAB-16528](https://jira.hyperledger.org/browse/FAB-16528) marbles private chaincode sync up
* [3fad853](https://github.com/hyperledger/fabric-samples/commit/3fad853) [FAB-16528](https://jira.hyperledger.org/browse/FAB-16528) marbles private chaincode sync up
* [8b9b82f](https://github.com/hyperledger/fabric-samples/commit/8b9b82f) [FAB-16489](https://jira.hyperledger.org/browse/FAB-16489) Add CODEOWNERS
* [a6ce915](https://github.com/hyperledger/fabric-samples/commit/a6ce915) [FAB-16487](https://jira.hyperledger.org/browse/FAB-16487) Update eslint
* [48082cf](https://github.com/hyperledger/fabric-samples/commit/48082cf) [FAB-16362](https://jira.hyperledger.org/browse/FAB-16362) adding chaincode excution comments
* [1d379f3](https://github.com/hyperledger/fabric-samples/commit/1d379f3) [FAB-16474](https://jira.hyperledger.org/browse/FAB-16474) marbles02 chaincode error
* [18712ca](https://github.com/hyperledger/fabric-samples/commit/18712ca) [FAB-16133](https://jira.hyperledger.org/browse/FAB-16133) Remove Solo consensus from BYFN
* [91c720a](https://github.com/hyperledger/fabric-samples/commit/91c720a) [FAB-16390](https://jira.hyperledger.org/browse/FAB-16390) Added filter for invalid transactions
* [1d3e267](https://github.com/hyperledger/fabric-samples/commit/1d3e267) Redirect samples to fabric-{chaincode,protos}-go
* [398a5b1](https://github.com/hyperledger/fabric-samples/commit/398a5b1) [FABCI-394] Remove AnsiColor Wrapper
* [ce154e0](https://github.com/hyperledger/fabric-samples/commit/ce154e0) [FAB-16310](https://jira.hyperledger.org/browse/FAB-16310) Vendor Go dependencies in all samples
* [6ea7c71](https://github.com/hyperledger/fabric-samples/commit/6ea7c71) [FAB-16285](https://jira.hyperledger.org/browse/FAB-16285) Update blacklisted versions in BYFN
* [86cd831](https://github.com/hyperledger/fabric-samples/commit/86cd831) [FAB-16284](https://jira.hyperledger.org/browse/FAB-16284) Remove E2E file and -f option from BYFN
* [0063abe](https://github.com/hyperledger/fabric-samples/commit/0063abe) Update stale script name in interest rate swaps
* [3907507](https://github.com/hyperledger/fabric-samples/commit/3907507) [FAB-16277](https://jira.hyperledger.org/browse/FAB-16277) Update BYFN w/ Raft ports in Docker network
* [33b0065](https://github.com/hyperledger/fabric-samples/commit/33b0065) [FAB-14813](https://jira.hyperledger.org/browse/FAB-14813) Channel event sample in fabric-samples
* [b62d5bd](https://github.com/hyperledger/fabric-samples/commit/b62d5bd) [FAB-16132](https://jira.hyperledger.org/browse/FAB-16132) Remove Kafka consensus from BYFN
* [9b14525](https://github.com/hyperledger/fabric-samples/commit/9b14525) [FAB-15213](https://jira.hyperledger.org/browse/FAB-15213) Update Commercial Paper for Java
* [4158877](https://github.com/hyperledger/fabric-samples/commit/4158877) [FAB-16232](https://jira.hyperledger.org/browse/FAB-16232) Remove FabToken sample
* [b6380cc](https://github.com/hyperledger/fabric-samples/commit/b6380cc) [FAB-16198](https://jira.hyperledger.org/browse/FAB-16198) Run "go mod vendor" for FabCar Go contract
* [639848a](https://github.com/hyperledger/fabric-samples/commit/639848a) [FAB-16197](https://jira.hyperledger.org/browse/FAB-16197) Add connection profiles to .gitignore
* [3996db5](https://github.com/hyperledger/fabric-samples/commit/3996db5) [FAB-12219](https://jira.hyperledger.org/browse/FAB-12219) abstore node -> javascript
* [14ac271](https://github.com/hyperledger/fabric-samples/commit/14ac271) [FAB-12219](https://jira.hyperledger.org/browse/FAB-12219) marbles02 node -> javascript
* [13f16e5](https://github.com/hyperledger/fabric-samples/commit/13f16e5) [FGJ-4] CI tests for FabCar Java sample
* [171a7d2](https://github.com/hyperledger/fabric-samples/commit/171a7d2) FGJ-4 Fabcar sample
* [868f9d0](https://github.com/hyperledger/fabric-samples/commit/868f9d0) [FAB-15625](https://jira.hyperledger.org/browse/FAB-15625) Add UT for Simple Asset Chaincode
* [597d150](https://github.com/hyperledger/fabric-samples/commit/597d150) [FAB-14532](https://jira.hyperledger.org/browse/FAB-14532) Remove LL FabCar sample
* [583ff8f](https://github.com/hyperledger/fabric-samples/commit/583ff8f) Use renamed CheckCommitReadiness function
* [750f937](https://github.com/hyperledger/fabric-samples/commit/750f937) [FAB-15213](https://jira.hyperledger.org/browse/FAB-15213) Add Java FabCar sample contract
* [abbda95](https://github.com/hyperledger/fabric-samples/commit/abbda95) [FAB-15897](https://jira.hyperledger.org/browse/FAB-15897) Improve FabCar test logging
* [dd8150a](https://github.com/hyperledger/fabric-samples/commit/dd8150a) [FAB-15104](https://jira.hyperledger.org/browse/FAB-15104) Remove versions from fabric-samples readme
* [1387aa8](https://github.com/hyperledger/fabric-samples/commit/1387aa8) [FAB-15927](https://jira.hyperledger.org/browse/FAB-15927)  Better expression for golang
* [61c33d3](https://github.com/hyperledger/fabric-samples/commit/61c33d3) [FAB-15973](https://jira.hyperledger.org/browse/FAB-15973) use --output json on simulatecommit
* [8bbdd0f](https://github.com/hyperledger/fabric-samples/commit/8bbdd0f) [FAB-15716](https://jira.hyperledger.org/browse/FAB-15716) Fix instructions for dev-mode
* [0254d67](https://github.com/hyperledger/fabric-samples/commit/0254d67) QueryApprovalStatus -> SimulateCommitChaincodeDef
* [c57d67c](https://github.com/hyperledger/fabric-samples/commit/c57d67c) [FAB-15782](https://jira.hyperledger.org/browse/FAB-15782) Sample Go CC should include deps
* [6ba5a19](https://github.com/hyperledger/fabric-samples/commit/6ba5a19) Update to Go 1.12.5 in ci.properties
* [1774a25](https://github.com/hyperledger/fabric-samples/commit/1774a25) [FAB-15723](https://jira.hyperledger.org/browse/FAB-15723) Fix script and instruction with ccenv
* [6ae711c](https://github.com/hyperledger/fabric-samples/commit/6ae711c) [FAB-15717](https://jira.hyperledger.org/browse/FAB-15717) fix Error Unexpected end of JSON input
* [5be56d3](https://github.com/hyperledger/fabric-samples/commit/5be56d3) [FAB-15104](https://jira.hyperledger.org/browse/FAB-15104) Remove scripts/bootstrap.sh
* [779f8f3](https://github.com/hyperledger/fabric-samples/commit/779f8f3) [FAB-15649](https://jira.hyperledger.org/browse/FAB-15649)Fix Fabcar to install Chaincode on all peers
* [7c5f5d3](https://github.com/hyperledger/fabric-samples/commit/7c5f5d3) [FAB-15199](https://jira.hyperledger.org/browse/FAB-15199) Update interest rate sample
* [f0dca20](https://github.com/hyperledger/fabric-samples/commit/f0dca20) [FAB-14532](https://jira.hyperledger.org/browse/FAB-14532) Remove LL FabCar sample
* [1ed1a10](https://github.com/hyperledger/fabric-samples/commit/1ed1a10) [FAB-15573](https://jira.hyperledger.org/browse/FAB-15573) Fix typo in fabric-samples-ci.md
* [2e7fec9](https://github.com/hyperledger/fabric-samples/commit/2e7fec9) [FAB-15601](https://jira.hyperledger.org/browse/FAB-15601) BYFN: Fix MAX_RETRY for couchdb
* [1e9e4c4](https://github.com/hyperledger/fabric-samples/commit/1e9e4c4) [FAB-9329](https://jira.hyperledger.org/browse/FAB-9329) Remove the unused variable in BYFN/EYFN
* [964c09f](https://github.com/hyperledger/fabric-samples/commit/964c09f) [FAB-15601](https://jira.hyperledger.org/browse/FAB-15601) BYFN: Fix MAX_RETRY for couchdb
* [41dca99](https://github.com/hyperledger/fabric-samples/commit/41dca99) [FAB-15127](https://jira.hyperledger.org/browse/FAB-15127) Update high throughput sample
* [3fe014a](https://github.com/hyperledger/fabric-samples/commit/3fe014a) Use official CouchDB image
* [f2d0fa0](https://github.com/hyperledger/fabric-samples/commit/f2d0fa0) [FAB-14487](https://jira.hyperledger.org/browse/FAB-14487) Make FabCar use BYFN, not basic-network
* [e9c3649](https://github.com/hyperledger/fabric-samples/commit/e9c3649) [FAB-15276](https://jira.hyperledger.org/browse/FAB-15276) Fix license statements
* [fbe4036](https://github.com/hyperledger/fabric-samples/commit/fbe4036) [FAB-14486](https://jira.hyperledger.org/browse/FAB-14486) Extend BYFN to opt skip chaincode deploy
* [0c4141f](https://github.com/hyperledger/fabric-samples/commit/0c4141f) [FAB-14485](https://jira.hyperledger.org/browse/FAB-14485) Extend BYFN to opt inc cert authorities
* [529b83b](https://github.com/hyperledger/fabric-samples/commit/529b83b) [FAB-14330](https://jira.hyperledger.org/browse/FAB-14330) Add connection profiles for BYFN and EYFN
* [2c21c83](https://github.com/hyperledger/fabric-samples/commit/2c21c83) [FABN-1184] Update fabtoken/README.md
* [5056a23](https://github.com/hyperledger/fabric-samples/commit/5056a23) [FABN-1184] Add CI script for fabtoken sample app
* [5d6db95](https://github.com/hyperledger/fabric-samples/commit/5d6db95) Update maintainers for fabric-samples
* [f527815](https://github.com/hyperledger/fabric-samples/commit/f527815) [FAB-15119](https://jira.hyperledger.org/browse/FAB-15119) Fix BYFN with Java chaincode
* [8245252](https://github.com/hyperledger/fabric-samples/commit/8245252) [FABN-1184] Implement fabtoken sample app
* [1bd1c2f](https://github.com/hyperledger/fabric-samples/commit/1bd1c2f) FABCI-284 Update CI Pipeline script
* [c24abf9](https://github.com/hyperledger/fabric-samples/commit/c24abf9) [FAB-15022](https://jira.hyperledger.org/browse/FAB-15022) Basic-network support for new lifecycle
* [b64fd45](https://github.com/hyperledger/fabric-samples/commit/b64fd45) [FAB-15051](https://jira.hyperledger.org/browse/FAB-15051) delStandard() function for high-throughput
* [3e68a7e](https://github.com/hyperledger/fabric-samples/commit/3e68a7e) [FAB-14784](https://jira.hyperledger.org/browse/FAB-14784) Remove balance-transfer
* [eb3fe08](https://github.com/hyperledger/fabric-samples/commit/eb3fe08) [FAB-14779](https://jira.hyperledger.org/browse/FAB-14779) QueryApprovalStatus step in byfn
* [2777429](https://github.com/hyperledger/fabric-samples/commit/2777429) [FAB-14711](https://jira.hyperledger.org/browse/FAB-14711) update byfn with new lifecycle
* [aec3389](https://github.com/hyperledger/fabric-samples/commit/aec3389) [FAB-12215](https://jira.hyperledger.org/browse/FAB-12215)WYFA:Remove chainId in tx proposal request
* [b5d5026](https://github.com/hyperledger/fabric-samples/commit/b5d5026) [FAB-14633](https://jira.hyperledger.org/browse/FAB-14633) Remove apt-get from eyfn.sh
* [efaadd3](https://github.com/hyperledger/fabric-samples/commit/efaadd3) [FAB-14531](https://jira.hyperledger.org/browse/FAB-14531) BYFN Raft with 5 nodes
* [d63047c](https://github.com/hyperledger/fabric-samples/commit/d63047c) [FAB-14444](https://jira.hyperledger.org/browse/FAB-14444)
* [7e3d428](https://github.com/hyperledger/fabric-samples/commit/7e3d428) [FAB-14369](https://jira.hyperledger.org/browse/FAB-14369)Fix dev mode failing to build Chaincode
* [420ba23](https://github.com/hyperledger/fabric-samples/commit/420ba23) [FAB-12762](https://jira.hyperledger.org/browse/FAB-12762) Add etcd/raft consensus option to BYFN
* [2b68c80](https://github.com/hyperledger/fabric-samples/commit/2b68c80) [FAB-14317](https://jira.hyperledger.org/browse/FAB-14317) Add default policies to org3
* [f942010](https://github.com/hyperledger/fabric-samples/commit/f942010) [FAB-14268](https://jira.hyperledger.org/browse/FAB-14268) Make BYFN/EYFN ports match external ports
* [4e2ce23](https://github.com/hyperledger/fabric-samples/commit/4e2ce23) [FAB-14271](https://jira.hyperledger.org/browse/FAB-14271) Add channel policies to channel config
* [f26477c](https://github.com/hyperledger/fabric-samples/commit/f26477c) [FAB-11796](https://jira.hyperledger.org/browse/FAB-11796)high-throughput:Remove unnecessary prunesafe
* [137327a](https://github.com/hyperledger/fabric-samples/commit/137327a) [FAB-14162](https://jira.hyperledger.org/browse/FAB-14162) Pin fabric-samples in master to "unstable"
* [6007c09](https://github.com/hyperledger/fabric-samples/commit/6007c09) [FAB-13862](https://jira.hyperledger.org/browse/FAB-13862) Rename example02 ABstore
* [94cb603](https://github.com/hyperledger/fabric-samples/commit/94cb603) [FAB-13933](https://jira.hyperledger.org/browse/FAB-13933) Fix misspellings
* [a8a5539](https://github.com/hyperledger/fabric-samples/commit/a8a5539) Fix doc link Fix variable error
* [b0cda61](https://github.com/hyperledger/fabric-samples/commit/b0cda61) [FAB-13769](https://jira.hyperledger.org/browse/FAB-13769) Add UT code to ABAC sample Chaincode
* [c7438e1](https://github.com/hyperledger/fabric-samples/commit/c7438e1) [FAB-13668](https://jira.hyperledger.org/browse/FAB-13668) BYFN's container volume mapping is bad
* [e48b2de](https://github.com/hyperledger/fabric-samples/commit/e48b2de) [FAB-13489](https://jira.hyperledger.org/browse/FAB-13489) fabric-samples add error msg
* [6269941](https://github.com/hyperledger/fabric-samples/commit/6269941) Correct broken link

## "v1.4.5"

* [11e4c23](https://github.com/hyperledger/fabric-samples/commit/11e4c23) Update samples to use v2.0 or later dependencies (#161)
* [94beab7](https://github.com/hyperledger/fabric-samples/commit/94beab7) FABN-1531 Use v2.1.0 sdk-node modules
* [8820d2f](https://github.com/hyperledger/fabric-samples/commit/8820d2f) Fix commercial-paper README
* [aa9b577](https://github.com/hyperledger/fabric-samples/commit/aa9b577) Remove TLS enabled switch (#155)
* [381fb46](https://github.com/hyperledger/fabric-samples/commit/381fb46) upgraded abstore golang chaincode to use contract-api package (#154)
* [5e5d2c8](https://github.com/hyperledger/fabric-samples/commit/5e5d2c8) Update java chaincode to be compatible with doc and other implementations (#149)
* [c572c51](https://github.com/hyperledger/fabric-samples/commit/c572c51) Organize and Standardize `ci` Directory Content (#152)
* [aa40963](https://github.com/hyperledger/fabric-samples/commit/aa40963) Perform General Cleanup (#151)
* [da41afa](https://github.com/hyperledger/fabric-samples/commit/da41afa) Remove left over rm -rf command from BYFN (#148)
* [4bb48a9](https://github.com/hyperledger/fabric-samples/commit/4bb48a9) Jenkins no longer used (#145)
* [6f984e1](https://github.com/hyperledger/fabric-samples/commit/6f984e1) Bump acorn from 6.4.0 to 6.4.1 in /fabcar/javascript (#144)
* [b155620](https://github.com/hyperledger/fabric-samples/commit/b155620) Remove redundant invoke command from test network (#142)
* [851933b](https://github.com/hyperledger/fabric-samples/commit/851933b) Add enrollUser files to commercial paper (#140)
* [87600bd](https://github.com/hyperledger/fabric-samples/commit/87600bd) [FAB-17268](https://jira.hyperledger.org/browse/FAB-17268) Move fabcar sample to test network (#103)
* [9397788](https://github.com/hyperledger/fabric-samples/commit/9397788) Wrong groupId on hyperledger fabric dependencies for java-application (#134)
* [92555fb](https://github.com/hyperledger/fabric-samples/commit/92555fb) Update README.md (#133)
* [59c6641](https://github.com/hyperledger/fabric-samples/commit/59c6641) Change Download Location of Fabric Binaries (#143)
* [1f283fc](https://github.com/hyperledger/fabric-samples/commit/1f283fc) init function does not exist on fabcar (#141)
* [defb6bb](https://github.com/hyperledger/fabric-samples/commit/defb6bb) [FAB-17656](https://jira.hyperledger.org/browse/FAB-17656) echo Generating channel.tx (#139)
* [4c7bab0](https://github.com/hyperledger/fabric-samples/commit/4c7bab0) fix: package seletor REGEX (#135)
* [db69c6f](https://github.com/hyperledger/fabric-samples/commit/db69c6f) Add fabcar external service sample (#136)
* [7f5f5e6](https://github.com/hyperledger/fabric-samples/commit/7f5f5e6) [FAB-17504](https://jira.hyperledger.org/browse/FAB-17504) add Organizations.<Org>.OrdererEndpoints and remove Orderer.Addresses (#125)
* [f3fc08d](https://github.com/hyperledger/fabric-samples/commit/f3fc08d) Remove solo and kafka from test net configtx.yaml (#137)
* [e17574d](https://github.com/hyperledger/fabric-samples/commit/e17574d) Add CA's to docker test network (#124)
* [faac18e](https://github.com/hyperledger/fabric-samples/commit/faac18e) [FAB-17461](https://jira.hyperledger.org/browse/FAB-17461) Move off_chain_data sample to test network (#122)
* [121a44a](https://github.com/hyperledger/fabric-samples/commit/121a44a) [FAB-17460](https://jira.hyperledger.org/browse/FAB-17460) Move High Throughput sample to test network (#112)
* [a2f3a66](https://github.com/hyperledger/fabric-samples/commit/a2f3a66) Update docker image version
* [e5b898c](https://github.com/hyperledger/fabric-samples/commit/e5b898c) Revert "first-network/scripts/*: Make Chaincode name configurable (#118)" (#131)
* [9ef61e2](https://github.com/hyperledger/fabric-samples/commit/9ef61e2) first-network/scripts/*: Make Chaincode name configurable (#118)
* [e204ebb](https://github.com/hyperledger/fabric-samples/commit/e204ebb) Remove reference to 2.0 beta (#111)
* [3dbe116](https://github.com/hyperledger/fabric-samples/commit/3dbe116) [FAB-17456](https://jira.hyperledger.org/browse/FAB-17456) fabric-samples read ccp (#117)
* [965ed1f](https://github.com/hyperledger/fabric-samples/commit/965ed1f) [FAB-17498](https://jira.hyperledger.org/browse/FAB-17498) Beta Images removal, test test-network (#121)
* [403019e](https://github.com/hyperledger/fabric-samples/commit/403019e) [FAB-17495](https://jira.hyperledger.org/browse/FAB-17495) Remove Basic Network sample (#120)
* [883ef99](https://github.com/hyperledger/fabric-samples/commit/883ef99) [FAB-17457](https://jira.hyperledger.org/browse/FAB-17457) Script correction (#119)
* [b89ee34](https://github.com/hyperledger/fabric-samples/commit/b89ee34) Update Commercial Paper to v2.0 Lifecycle (#109)
* [4208644](https://github.com/hyperledger/fabric-samples/commit/4208644) [FAB-17478](https://jira.hyperledger.org/browse/FAB-17478) Update commercial paper to use go api v1.0.0 (#115)
* [0df5ed9](https://github.com/hyperledger/fabric-samples/commit/0df5ed9) [FAB-17477](https://jira.hyperledger.org/browse/FAB-17477) Update fabcar to use go api v1.0.0 (#116)
* [571733f](https://github.com/hyperledger/fabric-samples/commit/571733f) [FAB-17447](https://jira.hyperledger.org/browse/FAB-17447) Update to 2.0.0 Libraries
* [67b4ee7](https://github.com/hyperledger/fabric-samples/commit/67b4ee7) Add Org3 bugs in test network (#108)
* [5b93dd0](https://github.com/hyperledger/fabric-samples/commit/5b93dd0) [FAB-17140](https://jira.hyperledger.org/browse/FAB-17140) Add go commercial paper contract (#102)
* [4fe6a25](https://github.com/hyperledger/fabric-samples/commit/4fe6a25) [FABCI-482] Update Nexus URL's to Artifactory (#92)
* [1488fbb](https://github.com/hyperledger/fabric-samples/commit/1488fbb) Add 1.x versions of fabric to blacklisted versions
* [8ca279d](https://github.com/hyperledger/fabric-samples/commit/8ca279d) Add Support for Versioning NodeJS (#106)
* [b3b5267](https://github.com/hyperledger/fabric-samples/commit/b3b5267) [FAB-17243](https://jira.hyperledger.org/browse/FAB-17243) Add support for Fabric CA for Org3 on the (#91)
* [ce41ff7](https://github.com/hyperledger/fabric-samples/commit/ce41ff7) Remove references to vendoring chaincode from your gopath (#96)
* [4235d30](https://github.com/hyperledger/fabric-samples/commit/4235d30) [FAB-17306](https://jira.hyperledger.org/browse/FAB-17306) Fix artifact names in test-network (#97)
* [4c2a0a4](https://github.com/hyperledger/fabric-samples/commit/4c2a0a4) [FAB-16147](https://jira.hyperledger.org/browse/FAB-16147) Update Commercial Paper to work with v2 (#98)
* [6d9fd6f](https://github.com/hyperledger/fabric-samples/commit/6d9fd6f) Remove Gerrit reference
* [a026a4f](https://github.com/hyperledger/fabric-samples/commit/a026a4f) Fixed typo (#90)
* [cdb0e8b](https://github.com/hyperledger/fabric-samples/commit/cdb0e8b) TYPO (#89)
* [94ac8b6](https://github.com/hyperledger/fabric-samples/commit/94ac8b6) Update to use beta levels of modules (#88)
* [d848633](https://github.com/hyperledger/fabric-samples/commit/d848633) [FAB-16844](https://jira.hyperledger.org/browse/FAB-16844) Correct BYFN CC name
* [73267e1](https://github.com/hyperledger/fabric-samples/commit/73267e1) Fix test network bugs for adding org3
* [5d58254](https://github.com/hyperledger/fabric-samples/commit/5d58254) [FAB-17145](https://jira.hyperledger.org/browse/FAB-17145) Add test network to Fabric Samples
* [e9f2957](https://github.com/hyperledger/fabric-samples/commit/e9f2957) [FAB-17062](https://jira.hyperledger.org/browse/FAB-17062) Fix typos in Commercial Paper readme
* [36694d0](https://github.com/hyperledger/fabric-samples/commit/36694d0) [FAB-17121](https://jira.hyperledger.org/browse/FAB-17121) Use new bootstrap config in orderer
* [429f087](https://github.com/hyperledger/fabric-samples/commit/429f087) update fabcar go to new programming model
* [1467086](https://github.com/hyperledger/fabric-samples/commit/1467086) Bump eslint-utils
* [33f349a](https://github.com/hyperledger/fabric-samples/commit/33f349a) Remove Stalebot
* [6af43bf](https://github.com/hyperledger/fabric-samples/commit/6af43bf) Change stalebot settings
* [4880401](https://github.com/hyperledger/fabric-samples/commit/4880401) [FAB-16284](https://jira.hyperledger.org/browse/FAB-16284) Remove E2E file and -f option from BYFN
* [fe96f60](https://github.com/hyperledger/fabric-samples/commit/fe96f60) [FAB-16850](https://jira.hyperledger.org/browse/FAB-16850) Set up CI with Azure Pipelines
* [81aabf4](https://github.com/hyperledger/fabric-samples/commit/81aabf4) [FAB-16849](https://jira.hyperledger.org/browse/FAB-16849) Various updates for Java version of FabCar
* [a42b858](https://github.com/hyperledger/fabric-samples/commit/a42b858) Update FabCar to reflect wallet API changes
* [890f9ea](https://github.com/hyperledger/fabric-samples/commit/890f9ea) [FAB-16713](https://jira.hyperledger.org/browse/FAB-16713) Fix npm audit warnings
* [e48e804](https://github.com/hyperledger/fabric-samples/commit/e48e804) [FAB-16776](https://jira.hyperledger.org/browse/FAB-16776) Move BYFN up to V2_0 capabilities
* [7b65a25](https://github.com/hyperledger/fabric-samples/commit/7b65a25) [IN-68] Add default GitHub SECURITY policy
* [408e0e8](https://github.com/hyperledger/fabric-samples/commit/408e0e8) [FAB-16619](https://jira.hyperledger.org/browse/FAB-16619) Fix the policy warning
* [670d446](https://github.com/hyperledger/fabric-samples/commit/670d446) [FAB-16668](https://jira.hyperledger.org/browse/FAB-16668) fabcar chaincode modify console output
* [f2939e2](https://github.com/hyperledger/fabric-samples/commit/f2939e2) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for Commercial Paper sample
* [3d19014](https://github.com/hyperledger/fabric-samples/commit/3d19014) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for FabCar sample
* [e2b7cb7](https://github.com/hyperledger/fabric-samples/commit/e2b7cb7) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for abstore sample
* [db48612](https://github.com/hyperledger/fabric-samples/commit/db48612) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Increase chaincode execute timeout
* [521a7ff](https://github.com/hyperledger/fabric-samples/commit/521a7ff) [FAB-16607](https://jira.hyperledger.org/browse/FAB-16607) Update FabCar to reflect CC updates
* [c13a5ec](https://github.com/hyperledger/fabric-samples/commit/c13a5ec) [FAB-16528](https://jira.hyperledger.org/browse/FAB-16528) marbles private chaincode sync up
* [3fad853](https://github.com/hyperledger/fabric-samples/commit/3fad853) [FAB-16528](https://jira.hyperledger.org/browse/FAB-16528) marbles private chaincode sync up
* [8b9b82f](https://github.com/hyperledger/fabric-samples/commit/8b9b82f) [FAB-16489](https://jira.hyperledger.org/browse/FAB-16489) Add CODEOWNERS
* [a6ce915](https://github.com/hyperledger/fabric-samples/commit/a6ce915) [FAB-16487](https://jira.hyperledger.org/browse/FAB-16487) Update eslint
* [48082cf](https://github.com/hyperledger/fabric-samples/commit/48082cf) [FAB-16362](https://jira.hyperledger.org/browse/FAB-16362) adding chaincode excution comments
* [1d379f3](https://github.com/hyperledger/fabric-samples/commit/1d379f3) [FAB-16474](https://jira.hyperledger.org/browse/FAB-16474) marbles02 chaincode error
* [18712ca](https://github.com/hyperledger/fabric-samples/commit/18712ca) [FAB-16133](https://jira.hyperledger.org/browse/FAB-16133) Remove Solo consensus from BYFN
* [91c720a](https://github.com/hyperledger/fabric-samples/commit/91c720a) [FAB-16390](https://jira.hyperledger.org/browse/FAB-16390) Added filter for invalid transactions
* [1d3e267](https://github.com/hyperledger/fabric-samples/commit/1d3e267) Redirect samples to fabric-{chaincode,protos}-go
* [398a5b1](https://github.com/hyperledger/fabric-samples/commit/398a5b1) [FABCI-394] Remove AnsiColor Wrapper
* [ce154e0](https://github.com/hyperledger/fabric-samples/commit/ce154e0) [FAB-16310](https://jira.hyperledger.org/browse/FAB-16310) Vendor Go dependencies in all samples
* [6ea7c71](https://github.com/hyperledger/fabric-samples/commit/6ea7c71) [FAB-16285](https://jira.hyperledger.org/browse/FAB-16285) Update blacklisted versions in BYFN
* [86cd831](https://github.com/hyperledger/fabric-samples/commit/86cd831) [FAB-16284](https://jira.hyperledger.org/browse/FAB-16284) Remove E2E file and -f option from BYFN
* [0063abe](https://github.com/hyperledger/fabric-samples/commit/0063abe) Update stale script name in interest rate swaps
* [3907507](https://github.com/hyperledger/fabric-samples/commit/3907507) [FAB-16277](https://jira.hyperledger.org/browse/FAB-16277) Update BYFN w/ Raft ports in Docker network
* [33b0065](https://github.com/hyperledger/fabric-samples/commit/33b0065) [FAB-14813](https://jira.hyperledger.org/browse/FAB-14813) Channel event sample in fabric-samples
* [b62d5bd](https://github.com/hyperledger/fabric-samples/commit/b62d5bd) [FAB-16132](https://jira.hyperledger.org/browse/FAB-16132) Remove Kafka consensus from BYFN
* [9b14525](https://github.com/hyperledger/fabric-samples/commit/9b14525) [FAB-15213](https://jira.hyperledger.org/browse/FAB-15213) Update Commercial Paper for Java
* [4158877](https://github.com/hyperledger/fabric-samples/commit/4158877) [FAB-16232](https://jira.hyperledger.org/browse/FAB-16232) Remove FabToken sample
* [b6380cc](https://github.com/hyperledger/fabric-samples/commit/b6380cc) [FAB-16198](https://jira.hyperledger.org/browse/FAB-16198) Run "go mod vendor" for FabCar Go contract
* [639848a](https://github.com/hyperledger/fabric-samples/commit/639848a) [FAB-16197](https://jira.hyperledger.org/browse/FAB-16197) Add connection profiles to .gitignore
* [3996db5](https://github.com/hyperledger/fabric-samples/commit/3996db5) [FAB-12219](https://jira.hyperledger.org/browse/FAB-12219) abstore node -> javascript
* [14ac271](https://github.com/hyperledger/fabric-samples/commit/14ac271) [FAB-12219](https://jira.hyperledger.org/browse/FAB-12219) marbles02 node -> javascript
* [13f16e5](https://github.com/hyperledger/fabric-samples/commit/13f16e5) [FGJ-4] CI tests for FabCar Java sample
* [171a7d2](https://github.com/hyperledger/fabric-samples/commit/171a7d2) FGJ-4 Fabcar sample
* [868f9d0](https://github.com/hyperledger/fabric-samples/commit/868f9d0) [FAB-15625](https://jira.hyperledger.org/browse/FAB-15625) Add UT for Simple Asset Chaincode
* [597d150](https://github.com/hyperledger/fabric-samples/commit/597d150) [FAB-14532](https://jira.hyperledger.org/browse/FAB-14532) Remove LL FabCar sample
* [583ff8f](https://github.com/hyperledger/fabric-samples/commit/583ff8f) Use renamed CheckCommitReadiness function
* [750f937](https://github.com/hyperledger/fabric-samples/commit/750f937) [FAB-15213](https://jira.hyperledger.org/browse/FAB-15213) Add Java FabCar sample contract
* [abbda95](https://github.com/hyperledger/fabric-samples/commit/abbda95) [FAB-15897](https://jira.hyperledger.org/browse/FAB-15897) Improve FabCar test logging
* [dd8150a](https://github.com/hyperledger/fabric-samples/commit/dd8150a) [FAB-15104](https://jira.hyperledger.org/browse/FAB-15104) Remove versions from fabric-samples readme
* [1387aa8](https://github.com/hyperledger/fabric-samples/commit/1387aa8) [FAB-15927](https://jira.hyperledger.org/browse/FAB-15927)  Better expression for golang
* [61c33d3](https://github.com/hyperledger/fabric-samples/commit/61c33d3) [FAB-15973](https://jira.hyperledger.org/browse/FAB-15973) use --output json on simulatecommit
* [8bbdd0f](https://github.com/hyperledger/fabric-samples/commit/8bbdd0f) [FAB-15716](https://jira.hyperledger.org/browse/FAB-15716) Fix instructions for dev-mode
* [0254d67](https://github.com/hyperledger/fabric-samples/commit/0254d67) QueryApprovalStatus -> SimulateCommitChaincodeDef
* [c57d67c](https://github.com/hyperledger/fabric-samples/commit/c57d67c) [FAB-15782](https://jira.hyperledger.org/browse/FAB-15782) Sample Go CC should include deps
* [6ba5a19](https://github.com/hyperledger/fabric-samples/commit/6ba5a19) Update to Go 1.12.5 in ci.properties
* [1774a25](https://github.com/hyperledger/fabric-samples/commit/1774a25) [FAB-15723](https://jira.hyperledger.org/browse/FAB-15723) Fix script and instruction with ccenv
* [6ae711c](https://github.com/hyperledger/fabric-samples/commit/6ae711c) [FAB-15717](https://jira.hyperledger.org/browse/FAB-15717) fix Error Unexpected end of JSON input
* [5be56d3](https://github.com/hyperledger/fabric-samples/commit/5be56d3) [FAB-15104](https://jira.hyperledger.org/browse/FAB-15104) Remove scripts/bootstrap.sh
* [779f8f3](https://github.com/hyperledger/fabric-samples/commit/779f8f3) [FAB-15649](https://jira.hyperledger.org/browse/FAB-15649)Fix Fabcar to install Chaincode on all peers
* [7c5f5d3](https://github.com/hyperledger/fabric-samples/commit/7c5f5d3) [FAB-15199](https://jira.hyperledger.org/browse/FAB-15199) Update interest rate sample
* [f0dca20](https://github.com/hyperledger/fabric-samples/commit/f0dca20) [FAB-14532](https://jira.hyperledger.org/browse/FAB-14532) Remove LL FabCar sample
* [1ed1a10](https://github.com/hyperledger/fabric-samples/commit/1ed1a10) [FAB-15573](https://jira.hyperledger.org/browse/FAB-15573) Fix typo in fabric-samples-ci.md
* [2e7fec9](https://github.com/hyperledger/fabric-samples/commit/2e7fec9) [FAB-15601](https://jira.hyperledger.org/browse/FAB-15601) BYFN: Fix MAX_RETRY for couchdb
* [1e9e4c4](https://github.com/hyperledger/fabric-samples/commit/1e9e4c4) [FAB-9329](https://jira.hyperledger.org/browse/FAB-9329) Remove the unused variable in BYFN/EYFN
* [964c09f](https://github.com/hyperledger/fabric-samples/commit/964c09f) [FAB-15601](https://jira.hyperledger.org/browse/FAB-15601) BYFN: Fix MAX_RETRY for couchdb
* [41dca99](https://github.com/hyperledger/fabric-samples/commit/41dca99) [FAB-15127](https://jira.hyperledger.org/browse/FAB-15127) Update high throughput sample
* [3fe014a](https://github.com/hyperledger/fabric-samples/commit/3fe014a) Use official CouchDB image
* [f2d0fa0](https://github.com/hyperledger/fabric-samples/commit/f2d0fa0) [FAB-14487](https://jira.hyperledger.org/browse/FAB-14487) Make FabCar use BYFN, not basic-network
* [e9c3649](https://github.com/hyperledger/fabric-samples/commit/e9c3649) [FAB-15276](https://jira.hyperledger.org/browse/FAB-15276) Fix license statements
* [fbe4036](https://github.com/hyperledger/fabric-samples/commit/fbe4036) [FAB-14486](https://jira.hyperledger.org/browse/FAB-14486) Extend BYFN to opt skip chaincode deploy
* [0c4141f](https://github.com/hyperledger/fabric-samples/commit/0c4141f) [FAB-14485](https://jira.hyperledger.org/browse/FAB-14485) Extend BYFN to opt inc cert authorities
* [529b83b](https://github.com/hyperledger/fabric-samples/commit/529b83b) [FAB-14330](https://jira.hyperledger.org/browse/FAB-14330) Add connection profiles for BYFN and EYFN
* [2c21c83](https://github.com/hyperledger/fabric-samples/commit/2c21c83) [FABN-1184] Update fabtoken/README.md
* [5056a23](https://github.com/hyperledger/fabric-samples/commit/5056a23) [FABN-1184] Add CI script for fabtoken sample app
* [5d6db95](https://github.com/hyperledger/fabric-samples/commit/5d6db95) Update maintainers for fabric-samples
* [f527815](https://github.com/hyperledger/fabric-samples/commit/f527815) [FAB-15119](https://jira.hyperledger.org/browse/FAB-15119) Fix BYFN with Java chaincode
* [8245252](https://github.com/hyperledger/fabric-samples/commit/8245252) [FABN-1184] Implement fabtoken sample app
* [1bd1c2f](https://github.com/hyperledger/fabric-samples/commit/1bd1c2f) FABCI-284 Update CI Pipeline script
* [c24abf9](https://github.com/hyperledger/fabric-samples/commit/c24abf9) [FAB-15022](https://jira.hyperledger.org/browse/FAB-15022) Basic-network support for new lifecycle
* [b64fd45](https://github.com/hyperledger/fabric-samples/commit/b64fd45) [FAB-15051](https://jira.hyperledger.org/browse/FAB-15051) delStandard() function for high-throughput
* [3e68a7e](https://github.com/hyperledger/fabric-samples/commit/3e68a7e) [FAB-14784](https://jira.hyperledger.org/browse/FAB-14784) Remove balance-transfer
* [eb3fe08](https://github.com/hyperledger/fabric-samples/commit/eb3fe08) [FAB-14779](https://jira.hyperledger.org/browse/FAB-14779) QueryApprovalStatus step in byfn
* [2777429](https://github.com/hyperledger/fabric-samples/commit/2777429) [FAB-14711](https://jira.hyperledger.org/browse/FAB-14711) update byfn with new lifecycle
* [aec3389](https://github.com/hyperledger/fabric-samples/commit/aec3389) [FAB-12215](https://jira.hyperledger.org/browse/FAB-12215)WYFA:Remove chainId in tx proposal request
* [b5d5026](https://github.com/hyperledger/fabric-samples/commit/b5d5026) [FAB-14633](https://jira.hyperledger.org/browse/FAB-14633) Remove apt-get from eyfn.sh
* [efaadd3](https://github.com/hyperledger/fabric-samples/commit/efaadd3) [FAB-14531](https://jira.hyperledger.org/browse/FAB-14531) BYFN Raft with 5 nodes
* [d63047c](https://github.com/hyperledger/fabric-samples/commit/d63047c) [FAB-14444](https://jira.hyperledger.org/browse/FAB-14444)
* [7e3d428](https://github.com/hyperledger/fabric-samples/commit/7e3d428) [FAB-14369](https://jira.hyperledger.org/browse/FAB-14369)Fix dev mode failing to build Chaincode
* [420ba23](https://github.com/hyperledger/fabric-samples/commit/420ba23) [FAB-12762](https://jira.hyperledger.org/browse/FAB-12762) Add etcd/raft consensus option to BYFN
* [2b68c80](https://github.com/hyperledger/fabric-samples/commit/2b68c80) [FAB-14317](https://jira.hyperledger.org/browse/FAB-14317) Add default policies to org3
* [f942010](https://github.com/hyperledger/fabric-samples/commit/f942010) [FAB-14268](https://jira.hyperledger.org/browse/FAB-14268) Make BYFN/EYFN ports match external ports
* [4e2ce23](https://github.com/hyperledger/fabric-samples/commit/4e2ce23) [FAB-14271](https://jira.hyperledger.org/browse/FAB-14271) Add channel policies to channel config
* [f26477c](https://github.com/hyperledger/fabric-samples/commit/f26477c) [FAB-11796](https://jira.hyperledger.org/browse/FAB-11796)high-throughput:Remove unnecessary prunesafe
* [137327a](https://github.com/hyperledger/fabric-samples/commit/137327a) [FAB-14162](https://jira.hyperledger.org/browse/FAB-14162) Pin fabric-samples in master to "unstable"
* [6007c09](https://github.com/hyperledger/fabric-samples/commit/6007c09) [FAB-13862](https://jira.hyperledger.org/browse/FAB-13862) Rename example02 ABstore
* [94cb603](https://github.com/hyperledger/fabric-samples/commit/94cb603) [FAB-13933](https://jira.hyperledger.org/browse/FAB-13933) Fix misspellings
* [a8a5539](https://github.com/hyperledger/fabric-samples/commit/a8a5539) Fix doc link Fix variable error
* [b0cda61](https://github.com/hyperledger/fabric-samples/commit/b0cda61) [FAB-13769](https://jira.hyperledger.org/browse/FAB-13769) Add UT code to ABAC sample Chaincode
* [c7438e1](https://github.com/hyperledger/fabric-samples/commit/c7438e1) [FAB-13668](https://jira.hyperledger.org/browse/FAB-13668) BYFN's container volume mapping is bad
* [e48b2de](https://github.com/hyperledger/fabric-samples/commit/e48b2de) [FAB-13489](https://jira.hyperledger.org/browse/FAB-13489) fabric-samples add error msg
* [6269941](https://github.com/hyperledger/fabric-samples/commit/6269941) Correct broken link

## "v1.4.4"

* [11e4c23](https://github.com/hyperledger/fabric-samples/commit/11e4c23) Update samples to use v2.0 or later dependencies (#161)
* [94beab7](https://github.com/hyperledger/fabric-samples/commit/94beab7) FABN-1531 Use v2.1.0 sdk-node modules
* [8820d2f](https://github.com/hyperledger/fabric-samples/commit/8820d2f) Fix commercial-paper README
* [aa9b577](https://github.com/hyperledger/fabric-samples/commit/aa9b577) Remove TLS enabled switch (#155)
* [381fb46](https://github.com/hyperledger/fabric-samples/commit/381fb46) upgraded abstore golang chaincode to use contract-api package (#154)
* [5e5d2c8](https://github.com/hyperledger/fabric-samples/commit/5e5d2c8) Update java chaincode to be compatible with doc and other implementations (#149)
* [c572c51](https://github.com/hyperledger/fabric-samples/commit/c572c51) Organize and Standardize `ci` Directory Content (#152)
* [aa40963](https://github.com/hyperledger/fabric-samples/commit/aa40963) Perform General Cleanup (#151)
* [da41afa](https://github.com/hyperledger/fabric-samples/commit/da41afa) Remove left over rm -rf command from BYFN (#148)
* [4bb48a9](https://github.com/hyperledger/fabric-samples/commit/4bb48a9) Jenkins no longer used (#145)
* [6f984e1](https://github.com/hyperledger/fabric-samples/commit/6f984e1) Bump acorn from 6.4.0 to 6.4.1 in /fabcar/javascript (#144)
* [b155620](https://github.com/hyperledger/fabric-samples/commit/b155620) Remove redundant invoke command from test network (#142)
* [851933b](https://github.com/hyperledger/fabric-samples/commit/851933b) Add enrollUser files to commercial paper (#140)
* [87600bd](https://github.com/hyperledger/fabric-samples/commit/87600bd) [FAB-17268](https://jira.hyperledger.org/browse/FAB-17268) Move fabcar sample to test network (#103)
* [9397788](https://github.com/hyperledger/fabric-samples/commit/9397788) Wrong groupId on hyperledger fabric dependencies for java-application (#134)
* [92555fb](https://github.com/hyperledger/fabric-samples/commit/92555fb) Update README.md (#133)
* [59c6641](https://github.com/hyperledger/fabric-samples/commit/59c6641) Change Download Location of Fabric Binaries (#143)
* [1f283fc](https://github.com/hyperledger/fabric-samples/commit/1f283fc) init function does not exist on fabcar (#141)
* [defb6bb](https://github.com/hyperledger/fabric-samples/commit/defb6bb) [FAB-17656](https://jira.hyperledger.org/browse/FAB-17656) echo Generating channel.tx (#139)
* [4c7bab0](https://github.com/hyperledger/fabric-samples/commit/4c7bab0) fix: package seletor REGEX (#135)
* [db69c6f](https://github.com/hyperledger/fabric-samples/commit/db69c6f) Add fabcar external service sample (#136)
* [7f5f5e6](https://github.com/hyperledger/fabric-samples/commit/7f5f5e6) [FAB-17504](https://jira.hyperledger.org/browse/FAB-17504) add Organizations.<Org>.OrdererEndpoints and remove Orderer.Addresses (#125)
* [f3fc08d](https://github.com/hyperledger/fabric-samples/commit/f3fc08d) Remove solo and kafka from test net configtx.yaml (#137)
* [e17574d](https://github.com/hyperledger/fabric-samples/commit/e17574d) Add CA's to docker test network (#124)
* [faac18e](https://github.com/hyperledger/fabric-samples/commit/faac18e) [FAB-17461](https://jira.hyperledger.org/browse/FAB-17461) Move off_chain_data sample to test network (#122)
* [121a44a](https://github.com/hyperledger/fabric-samples/commit/121a44a) [FAB-17460](https://jira.hyperledger.org/browse/FAB-17460) Move High Throughput sample to test network (#112)
* [a2f3a66](https://github.com/hyperledger/fabric-samples/commit/a2f3a66) Update docker image version
* [e5b898c](https://github.com/hyperledger/fabric-samples/commit/e5b898c) Revert "first-network/scripts/*: Make Chaincode name configurable (#118)" (#131)
* [9ef61e2](https://github.com/hyperledger/fabric-samples/commit/9ef61e2) first-network/scripts/*: Make Chaincode name configurable (#118)
* [e204ebb](https://github.com/hyperledger/fabric-samples/commit/e204ebb) Remove reference to 2.0 beta (#111)
* [3dbe116](https://github.com/hyperledger/fabric-samples/commit/3dbe116) [FAB-17456](https://jira.hyperledger.org/browse/FAB-17456) fabric-samples read ccp (#117)
* [965ed1f](https://github.com/hyperledger/fabric-samples/commit/965ed1f) [FAB-17498](https://jira.hyperledger.org/browse/FAB-17498) Beta Images removal, test test-network (#121)
* [403019e](https://github.com/hyperledger/fabric-samples/commit/403019e) [FAB-17495](https://jira.hyperledger.org/browse/FAB-17495) Remove Basic Network sample (#120)
* [883ef99](https://github.com/hyperledger/fabric-samples/commit/883ef99) [FAB-17457](https://jira.hyperledger.org/browse/FAB-17457) Script correction (#119)
* [b89ee34](https://github.com/hyperledger/fabric-samples/commit/b89ee34) Update Commercial Paper to v2.0 Lifecycle (#109)
* [4208644](https://github.com/hyperledger/fabric-samples/commit/4208644) [FAB-17478](https://jira.hyperledger.org/browse/FAB-17478) Update commercial paper to use go api v1.0.0 (#115)
* [0df5ed9](https://github.com/hyperledger/fabric-samples/commit/0df5ed9) [FAB-17477](https://jira.hyperledger.org/browse/FAB-17477) Update fabcar to use go api v1.0.0 (#116)
* [571733f](https://github.com/hyperledger/fabric-samples/commit/571733f) [FAB-17447](https://jira.hyperledger.org/browse/FAB-17447) Update to 2.0.0 Libraries
* [67b4ee7](https://github.com/hyperledger/fabric-samples/commit/67b4ee7) Add Org3 bugs in test network (#108)
* [5b93dd0](https://github.com/hyperledger/fabric-samples/commit/5b93dd0) [FAB-17140](https://jira.hyperledger.org/browse/FAB-17140) Add go commercial paper contract (#102)
* [4fe6a25](https://github.com/hyperledger/fabric-samples/commit/4fe6a25) [FABCI-482] Update Nexus URL's to Artifactory (#92)
* [1488fbb](https://github.com/hyperledger/fabric-samples/commit/1488fbb) Add 1.x versions of fabric to blacklisted versions
* [8ca279d](https://github.com/hyperledger/fabric-samples/commit/8ca279d) Add Support for Versioning NodeJS (#106)
* [b3b5267](https://github.com/hyperledger/fabric-samples/commit/b3b5267) [FAB-17243](https://jira.hyperledger.org/browse/FAB-17243) Add support for Fabric CA for Org3 on the (#91)
* [ce41ff7](https://github.com/hyperledger/fabric-samples/commit/ce41ff7) Remove references to vendoring chaincode from your gopath (#96)
* [4235d30](https://github.com/hyperledger/fabric-samples/commit/4235d30) [FAB-17306](https://jira.hyperledger.org/browse/FAB-17306) Fix artifact names in test-network (#97)
* [4c2a0a4](https://github.com/hyperledger/fabric-samples/commit/4c2a0a4) [FAB-16147](https://jira.hyperledger.org/browse/FAB-16147) Update Commercial Paper to work with v2 (#98)
* [6d9fd6f](https://github.com/hyperledger/fabric-samples/commit/6d9fd6f) Remove Gerrit reference
* [a026a4f](https://github.com/hyperledger/fabric-samples/commit/a026a4f) Fixed typo (#90)
* [cdb0e8b](https://github.com/hyperledger/fabric-samples/commit/cdb0e8b) TYPO (#89)
* [94ac8b6](https://github.com/hyperledger/fabric-samples/commit/94ac8b6) Update to use beta levels of modules (#88)
* [d848633](https://github.com/hyperledger/fabric-samples/commit/d848633) [FAB-16844](https://jira.hyperledger.org/browse/FAB-16844) Correct BYFN CC name
* [73267e1](https://github.com/hyperledger/fabric-samples/commit/73267e1) Fix test network bugs for adding org3
* [5d58254](https://github.com/hyperledger/fabric-samples/commit/5d58254) [FAB-17145](https://jira.hyperledger.org/browse/FAB-17145) Add test network to Fabric Samples
* [e9f2957](https://github.com/hyperledger/fabric-samples/commit/e9f2957) [FAB-17062](https://jira.hyperledger.org/browse/FAB-17062) Fix typos in Commercial Paper readme
* [36694d0](https://github.com/hyperledger/fabric-samples/commit/36694d0) [FAB-17121](https://jira.hyperledger.org/browse/FAB-17121) Use new bootstrap config in orderer
* [429f087](https://github.com/hyperledger/fabric-samples/commit/429f087) update fabcar go to new programming model
* [1467086](https://github.com/hyperledger/fabric-samples/commit/1467086) Bump eslint-utils
* [33f349a](https://github.com/hyperledger/fabric-samples/commit/33f349a) Remove Stalebot
* [6af43bf](https://github.com/hyperledger/fabric-samples/commit/6af43bf) Change stalebot settings
* [4880401](https://github.com/hyperledger/fabric-samples/commit/4880401) [FAB-16284](https://jira.hyperledger.org/browse/FAB-16284) Remove E2E file and -f option from BYFN
* [fe96f60](https://github.com/hyperledger/fabric-samples/commit/fe96f60) [FAB-16850](https://jira.hyperledger.org/browse/FAB-16850) Set up CI with Azure Pipelines
* [81aabf4](https://github.com/hyperledger/fabric-samples/commit/81aabf4) [FAB-16849](https://jira.hyperledger.org/browse/FAB-16849) Various updates for Java version of FabCar
* [a42b858](https://github.com/hyperledger/fabric-samples/commit/a42b858) Update FabCar to reflect wallet API changes
* [890f9ea](https://github.com/hyperledger/fabric-samples/commit/890f9ea) [FAB-16713](https://jira.hyperledger.org/browse/FAB-16713) Fix npm audit warnings
* [e48e804](https://github.com/hyperledger/fabric-samples/commit/e48e804) [FAB-16776](https://jira.hyperledger.org/browse/FAB-16776) Move BYFN up to V2_0 capabilities
* [7b65a25](https://github.com/hyperledger/fabric-samples/commit/7b65a25) [IN-68] Add default GitHub SECURITY policy
* [408e0e8](https://github.com/hyperledger/fabric-samples/commit/408e0e8) [FAB-16619](https://jira.hyperledger.org/browse/FAB-16619) Fix the policy warning
* [670d446](https://github.com/hyperledger/fabric-samples/commit/670d446) [FAB-16668](https://jira.hyperledger.org/browse/FAB-16668) fabcar chaincode modify console output
* [f2939e2](https://github.com/hyperledger/fabric-samples/commit/f2939e2) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for Commercial Paper sample
* [3d19014](https://github.com/hyperledger/fabric-samples/commit/3d19014) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for FabCar sample
* [e2b7cb7](https://github.com/hyperledger/fabric-samples/commit/e2b7cb7) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for abstore sample
* [db48612](https://github.com/hyperledger/fabric-samples/commit/db48612) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Increase chaincode execute timeout
* [521a7ff](https://github.com/hyperledger/fabric-samples/commit/521a7ff) [FAB-16607](https://jira.hyperledger.org/browse/FAB-16607) Update FabCar to reflect CC updates
* [c13a5ec](https://github.com/hyperledger/fabric-samples/commit/c13a5ec) [FAB-16528](https://jira.hyperledger.org/browse/FAB-16528) marbles private chaincode sync up
* [3fad853](https://github.com/hyperledger/fabric-samples/commit/3fad853) [FAB-16528](https://jira.hyperledger.org/browse/FAB-16528) marbles private chaincode sync up
* [8b9b82f](https://github.com/hyperledger/fabric-samples/commit/8b9b82f) [FAB-16489](https://jira.hyperledger.org/browse/FAB-16489) Add CODEOWNERS
* [a6ce915](https://github.com/hyperledger/fabric-samples/commit/a6ce915) [FAB-16487](https://jira.hyperledger.org/browse/FAB-16487) Update eslint
* [48082cf](https://github.com/hyperledger/fabric-samples/commit/48082cf) [FAB-16362](https://jira.hyperledger.org/browse/FAB-16362) adding chaincode excution comments
* [1d379f3](https://github.com/hyperledger/fabric-samples/commit/1d379f3) [FAB-16474](https://jira.hyperledger.org/browse/FAB-16474) marbles02 chaincode error
* [18712ca](https://github.com/hyperledger/fabric-samples/commit/18712ca) [FAB-16133](https://jira.hyperledger.org/browse/FAB-16133) Remove Solo consensus from BYFN
* [91c720a](https://github.com/hyperledger/fabric-samples/commit/91c720a) [FAB-16390](https://jira.hyperledger.org/browse/FAB-16390) Added filter for invalid transactions
* [1d3e267](https://github.com/hyperledger/fabric-samples/commit/1d3e267) Redirect samples to fabric-{chaincode,protos}-go
* [398a5b1](https://github.com/hyperledger/fabric-samples/commit/398a5b1) [FABCI-394] Remove AnsiColor Wrapper
* [ce154e0](https://github.com/hyperledger/fabric-samples/commit/ce154e0) [FAB-16310](https://jira.hyperledger.org/browse/FAB-16310) Vendor Go dependencies in all samples
* [6ea7c71](https://github.com/hyperledger/fabric-samples/commit/6ea7c71) [FAB-16285](https://jira.hyperledger.org/browse/FAB-16285) Update blacklisted versions in BYFN
* [86cd831](https://github.com/hyperledger/fabric-samples/commit/86cd831) [FAB-16284](https://jira.hyperledger.org/browse/FAB-16284) Remove E2E file and -f option from BYFN
* [0063abe](https://github.com/hyperledger/fabric-samples/commit/0063abe) Update stale script name in interest rate swaps
* [3907507](https://github.com/hyperledger/fabric-samples/commit/3907507) [FAB-16277](https://jira.hyperledger.org/browse/FAB-16277) Update BYFN w/ Raft ports in Docker network
* [33b0065](https://github.com/hyperledger/fabric-samples/commit/33b0065) [FAB-14813](https://jira.hyperledger.org/browse/FAB-14813) Channel event sample in fabric-samples
* [b62d5bd](https://github.com/hyperledger/fabric-samples/commit/b62d5bd) [FAB-16132](https://jira.hyperledger.org/browse/FAB-16132) Remove Kafka consensus from BYFN
* [9b14525](https://github.com/hyperledger/fabric-samples/commit/9b14525) [FAB-15213](https://jira.hyperledger.org/browse/FAB-15213) Update Commercial Paper for Java
* [4158877](https://github.com/hyperledger/fabric-samples/commit/4158877) [FAB-16232](https://jira.hyperledger.org/browse/FAB-16232) Remove FabToken sample
* [b6380cc](https://github.com/hyperledger/fabric-samples/commit/b6380cc) [FAB-16198](https://jira.hyperledger.org/browse/FAB-16198) Run "go mod vendor" for FabCar Go contract
* [639848a](https://github.com/hyperledger/fabric-samples/commit/639848a) [FAB-16197](https://jira.hyperledger.org/browse/FAB-16197) Add connection profiles to .gitignore
* [3996db5](https://github.com/hyperledger/fabric-samples/commit/3996db5) [FAB-12219](https://jira.hyperledger.org/browse/FAB-12219) abstore node -> javascript
* [14ac271](https://github.com/hyperledger/fabric-samples/commit/14ac271) [FAB-12219](https://jira.hyperledger.org/browse/FAB-12219) marbles02 node -> javascript
* [13f16e5](https://github.com/hyperledger/fabric-samples/commit/13f16e5) [FGJ-4] CI tests for FabCar Java sample
* [171a7d2](https://github.com/hyperledger/fabric-samples/commit/171a7d2) FGJ-4 Fabcar sample
* [868f9d0](https://github.com/hyperledger/fabric-samples/commit/868f9d0) [FAB-15625](https://jira.hyperledger.org/browse/FAB-15625) Add UT for Simple Asset Chaincode
* [597d150](https://github.com/hyperledger/fabric-samples/commit/597d150) [FAB-14532](https://jira.hyperledger.org/browse/FAB-14532) Remove LL FabCar sample
* [583ff8f](https://github.com/hyperledger/fabric-samples/commit/583ff8f) Use renamed CheckCommitReadiness function
* [750f937](https://github.com/hyperledger/fabric-samples/commit/750f937) [FAB-15213](https://jira.hyperledger.org/browse/FAB-15213) Add Java FabCar sample contract
* [abbda95](https://github.com/hyperledger/fabric-samples/commit/abbda95) [FAB-15897](https://jira.hyperledger.org/browse/FAB-15897) Improve FabCar test logging
* [dd8150a](https://github.com/hyperledger/fabric-samples/commit/dd8150a) [FAB-15104](https://jira.hyperledger.org/browse/FAB-15104) Remove versions from fabric-samples readme
* [1387aa8](https://github.com/hyperledger/fabric-samples/commit/1387aa8) [FAB-15927](https://jira.hyperledger.org/browse/FAB-15927)  Better expression for golang
* [61c33d3](https://github.com/hyperledger/fabric-samples/commit/61c33d3) [FAB-15973](https://jira.hyperledger.org/browse/FAB-15973) use --output json on simulatecommit
* [8bbdd0f](https://github.com/hyperledger/fabric-samples/commit/8bbdd0f) [FAB-15716](https://jira.hyperledger.org/browse/FAB-15716) Fix instructions for dev-mode
* [0254d67](https://github.com/hyperledger/fabric-samples/commit/0254d67) QueryApprovalStatus -> SimulateCommitChaincodeDef
* [c57d67c](https://github.com/hyperledger/fabric-samples/commit/c57d67c) [FAB-15782](https://jira.hyperledger.org/browse/FAB-15782) Sample Go CC should include deps
* [6ba5a19](https://github.com/hyperledger/fabric-samples/commit/6ba5a19) Update to Go 1.12.5 in ci.properties
* [1774a25](https://github.com/hyperledger/fabric-samples/commit/1774a25) [FAB-15723](https://jira.hyperledger.org/browse/FAB-15723) Fix script and instruction with ccenv
* [6ae711c](https://github.com/hyperledger/fabric-samples/commit/6ae711c) [FAB-15717](https://jira.hyperledger.org/browse/FAB-15717) fix Error Unexpected end of JSON input
* [5be56d3](https://github.com/hyperledger/fabric-samples/commit/5be56d3) [FAB-15104](https://jira.hyperledger.org/browse/FAB-15104) Remove scripts/bootstrap.sh
* [779f8f3](https://github.com/hyperledger/fabric-samples/commit/779f8f3) [FAB-15649](https://jira.hyperledger.org/browse/FAB-15649)Fix Fabcar to install Chaincode on all peers
* [7c5f5d3](https://github.com/hyperledger/fabric-samples/commit/7c5f5d3) [FAB-15199](https://jira.hyperledger.org/browse/FAB-15199) Update interest rate sample
* [f0dca20](https://github.com/hyperledger/fabric-samples/commit/f0dca20) [FAB-14532](https://jira.hyperledger.org/browse/FAB-14532) Remove LL FabCar sample
* [1ed1a10](https://github.com/hyperledger/fabric-samples/commit/1ed1a10) [FAB-15573](https://jira.hyperledger.org/browse/FAB-15573) Fix typo in fabric-samples-ci.md
* [2e7fec9](https://github.com/hyperledger/fabric-samples/commit/2e7fec9) [FAB-15601](https://jira.hyperledger.org/browse/FAB-15601) BYFN: Fix MAX_RETRY for couchdb
* [1e9e4c4](https://github.com/hyperledger/fabric-samples/commit/1e9e4c4) [FAB-9329](https://jira.hyperledger.org/browse/FAB-9329) Remove the unused variable in BYFN/EYFN
* [964c09f](https://github.com/hyperledger/fabric-samples/commit/964c09f) [FAB-15601](https://jira.hyperledger.org/browse/FAB-15601) BYFN: Fix MAX_RETRY for couchdb
* [41dca99](https://github.com/hyperledger/fabric-samples/commit/41dca99) [FAB-15127](https://jira.hyperledger.org/browse/FAB-15127) Update high throughput sample
* [3fe014a](https://github.com/hyperledger/fabric-samples/commit/3fe014a) Use official CouchDB image
* [f2d0fa0](https://github.com/hyperledger/fabric-samples/commit/f2d0fa0) [FAB-14487](https://jira.hyperledger.org/browse/FAB-14487) Make FabCar use BYFN, not basic-network
* [e9c3649](https://github.com/hyperledger/fabric-samples/commit/e9c3649) [FAB-15276](https://jira.hyperledger.org/browse/FAB-15276) Fix license statements
* [fbe4036](https://github.com/hyperledger/fabric-samples/commit/fbe4036) [FAB-14486](https://jira.hyperledger.org/browse/FAB-14486) Extend BYFN to opt skip chaincode deploy
* [0c4141f](https://github.com/hyperledger/fabric-samples/commit/0c4141f) [FAB-14485](https://jira.hyperledger.org/browse/FAB-14485) Extend BYFN to opt inc cert authorities
* [529b83b](https://github.com/hyperledger/fabric-samples/commit/529b83b) [FAB-14330](https://jira.hyperledger.org/browse/FAB-14330) Add connection profiles for BYFN and EYFN
* [2c21c83](https://github.com/hyperledger/fabric-samples/commit/2c21c83) [FABN-1184] Update fabtoken/README.md
* [5056a23](https://github.com/hyperledger/fabric-samples/commit/5056a23) [FABN-1184] Add CI script for fabtoken sample app
* [5d6db95](https://github.com/hyperledger/fabric-samples/commit/5d6db95) Update maintainers for fabric-samples
* [f527815](https://github.com/hyperledger/fabric-samples/commit/f527815) [FAB-15119](https://jira.hyperledger.org/browse/FAB-15119) Fix BYFN with Java chaincode
* [8245252](https://github.com/hyperledger/fabric-samples/commit/8245252) [FABN-1184] Implement fabtoken sample app
* [1bd1c2f](https://github.com/hyperledger/fabric-samples/commit/1bd1c2f) FABCI-284 Update CI Pipeline script
* [c24abf9](https://github.com/hyperledger/fabric-samples/commit/c24abf9) [FAB-15022](https://jira.hyperledger.org/browse/FAB-15022) Basic-network support for new lifecycle
* [b64fd45](https://github.com/hyperledger/fabric-samples/commit/b64fd45) [FAB-15051](https://jira.hyperledger.org/browse/FAB-15051) delStandard() function for high-throughput
* [3e68a7e](https://github.com/hyperledger/fabric-samples/commit/3e68a7e) [FAB-14784](https://jira.hyperledger.org/browse/FAB-14784) Remove balance-transfer
* [eb3fe08](https://github.com/hyperledger/fabric-samples/commit/eb3fe08) [FAB-14779](https://jira.hyperledger.org/browse/FAB-14779) QueryApprovalStatus step in byfn
* [2777429](https://github.com/hyperledger/fabric-samples/commit/2777429) [FAB-14711](https://jira.hyperledger.org/browse/FAB-14711) update byfn with new lifecycle
* [aec3389](https://github.com/hyperledger/fabric-samples/commit/aec3389) [FAB-12215](https://jira.hyperledger.org/browse/FAB-12215)WYFA:Remove chainId in tx proposal request
* [b5d5026](https://github.com/hyperledger/fabric-samples/commit/b5d5026) [FAB-14633](https://jira.hyperledger.org/browse/FAB-14633) Remove apt-get from eyfn.sh
* [efaadd3](https://github.com/hyperledger/fabric-samples/commit/efaadd3) [FAB-14531](https://jira.hyperledger.org/browse/FAB-14531) BYFN Raft with 5 nodes
* [d63047c](https://github.com/hyperledger/fabric-samples/commit/d63047c) [FAB-14444](https://jira.hyperledger.org/browse/FAB-14444)
* [7e3d428](https://github.com/hyperledger/fabric-samples/commit/7e3d428) [FAB-14369](https://jira.hyperledger.org/browse/FAB-14369)Fix dev mode failing to build Chaincode
* [420ba23](https://github.com/hyperledger/fabric-samples/commit/420ba23) [FAB-12762](https://jira.hyperledger.org/browse/FAB-12762) Add etcd/raft consensus option to BYFN
* [2b68c80](https://github.com/hyperledger/fabric-samples/commit/2b68c80) [FAB-14317](https://jira.hyperledger.org/browse/FAB-14317) Add default policies to org3
* [f942010](https://github.com/hyperledger/fabric-samples/commit/f942010) [FAB-14268](https://jira.hyperledger.org/browse/FAB-14268) Make BYFN/EYFN ports match external ports
* [4e2ce23](https://github.com/hyperledger/fabric-samples/commit/4e2ce23) [FAB-14271](https://jira.hyperledger.org/browse/FAB-14271) Add channel policies to channel config
* [f26477c](https://github.com/hyperledger/fabric-samples/commit/f26477c) [FAB-11796](https://jira.hyperledger.org/browse/FAB-11796)high-throughput:Remove unnecessary prunesafe
* [137327a](https://github.com/hyperledger/fabric-samples/commit/137327a) [FAB-14162](https://jira.hyperledger.org/browse/FAB-14162) Pin fabric-samples in master to "unstable"
* [6007c09](https://github.com/hyperledger/fabric-samples/commit/6007c09) [FAB-13862](https://jira.hyperledger.org/browse/FAB-13862) Rename example02 ABstore
* [94cb603](https://github.com/hyperledger/fabric-samples/commit/94cb603) [FAB-13933](https://jira.hyperledger.org/browse/FAB-13933) Fix misspellings
* [a8a5539](https://github.com/hyperledger/fabric-samples/commit/a8a5539) Fix doc link Fix variable error
* [b0cda61](https://github.com/hyperledger/fabric-samples/commit/b0cda61) [FAB-13769](https://jira.hyperledger.org/browse/FAB-13769) Add UT code to ABAC sample Chaincode
* [c7438e1](https://github.com/hyperledger/fabric-samples/commit/c7438e1) [FAB-13668](https://jira.hyperledger.org/browse/FAB-13668) BYFN's container volume mapping is bad
* [e48b2de](https://github.com/hyperledger/fabric-samples/commit/e48b2de) [FAB-13489](https://jira.hyperledger.org/browse/FAB-13489) fabric-samples add error msg
* [6269941](https://github.com/hyperledger/fabric-samples/commit/6269941) Correct broken link

## "v1.4.3"

* [11e4c23](https://github.com/hyperledger/fabric-samples/commit/11e4c23) Update samples to use v2.0 or later dependencies (#161)
* [94beab7](https://github.com/hyperledger/fabric-samples/commit/94beab7) FABN-1531 Use v2.1.0 sdk-node modules
* [8820d2f](https://github.com/hyperledger/fabric-samples/commit/8820d2f) Fix commercial-paper README
* [aa9b577](https://github.com/hyperledger/fabric-samples/commit/aa9b577) Remove TLS enabled switch (#155)
* [381fb46](https://github.com/hyperledger/fabric-samples/commit/381fb46) upgraded abstore golang chaincode to use contract-api package (#154)
* [5e5d2c8](https://github.com/hyperledger/fabric-samples/commit/5e5d2c8) Update java chaincode to be compatible with doc and other implementations (#149)
* [c572c51](https://github.com/hyperledger/fabric-samples/commit/c572c51) Organize and Standardize `ci` Directory Content (#152)
* [aa40963](https://github.com/hyperledger/fabric-samples/commit/aa40963) Perform General Cleanup (#151)
* [da41afa](https://github.com/hyperledger/fabric-samples/commit/da41afa) Remove left over rm -rf command from BYFN (#148)
* [4bb48a9](https://github.com/hyperledger/fabric-samples/commit/4bb48a9) Jenkins no longer used (#145)
* [6f984e1](https://github.com/hyperledger/fabric-samples/commit/6f984e1) Bump acorn from 6.4.0 to 6.4.1 in /fabcar/javascript (#144)
* [b155620](https://github.com/hyperledger/fabric-samples/commit/b155620) Remove redundant invoke command from test network (#142)
* [851933b](https://github.com/hyperledger/fabric-samples/commit/851933b) Add enrollUser files to commercial paper (#140)
* [87600bd](https://github.com/hyperledger/fabric-samples/commit/87600bd) [FAB-17268](https://jira.hyperledger.org/browse/FAB-17268) Move fabcar sample to test network (#103)
* [9397788](https://github.com/hyperledger/fabric-samples/commit/9397788) Wrong groupId on hyperledger fabric dependencies for java-application (#134)
* [92555fb](https://github.com/hyperledger/fabric-samples/commit/92555fb) Update README.md (#133)
* [59c6641](https://github.com/hyperledger/fabric-samples/commit/59c6641) Change Download Location of Fabric Binaries (#143)
* [1f283fc](https://github.com/hyperledger/fabric-samples/commit/1f283fc) init function does not exist on fabcar (#141)
* [defb6bb](https://github.com/hyperledger/fabric-samples/commit/defb6bb) [FAB-17656](https://jira.hyperledger.org/browse/FAB-17656) echo Generating channel.tx (#139)
* [4c7bab0](https://github.com/hyperledger/fabric-samples/commit/4c7bab0) fix: package seletor REGEX (#135)
* [db69c6f](https://github.com/hyperledger/fabric-samples/commit/db69c6f) Add fabcar external service sample (#136)
* [7f5f5e6](https://github.com/hyperledger/fabric-samples/commit/7f5f5e6) [FAB-17504](https://jira.hyperledger.org/browse/FAB-17504) add Organizations.<Org>.OrdererEndpoints and remove Orderer.Addresses (#125)
* [f3fc08d](https://github.com/hyperledger/fabric-samples/commit/f3fc08d) Remove solo and kafka from test net configtx.yaml (#137)
* [e17574d](https://github.com/hyperledger/fabric-samples/commit/e17574d) Add CA's to docker test network (#124)
* [faac18e](https://github.com/hyperledger/fabric-samples/commit/faac18e) [FAB-17461](https://jira.hyperledger.org/browse/FAB-17461) Move off_chain_data sample to test network (#122)
* [121a44a](https://github.com/hyperledger/fabric-samples/commit/121a44a) [FAB-17460](https://jira.hyperledger.org/browse/FAB-17460) Move High Throughput sample to test network (#112)
* [a2f3a66](https://github.com/hyperledger/fabric-samples/commit/a2f3a66) Update docker image version
* [e5b898c](https://github.com/hyperledger/fabric-samples/commit/e5b898c) Revert "first-network/scripts/*: Make Chaincode name configurable (#118)" (#131)
* [9ef61e2](https://github.com/hyperledger/fabric-samples/commit/9ef61e2) first-network/scripts/*: Make Chaincode name configurable (#118)
* [e204ebb](https://github.com/hyperledger/fabric-samples/commit/e204ebb) Remove reference to 2.0 beta (#111)
* [3dbe116](https://github.com/hyperledger/fabric-samples/commit/3dbe116) [FAB-17456](https://jira.hyperledger.org/browse/FAB-17456) fabric-samples read ccp (#117)
* [965ed1f](https://github.com/hyperledger/fabric-samples/commit/965ed1f) [FAB-17498](https://jira.hyperledger.org/browse/FAB-17498) Beta Images removal, test test-network (#121)
* [403019e](https://github.com/hyperledger/fabric-samples/commit/403019e) [FAB-17495](https://jira.hyperledger.org/browse/FAB-17495) Remove Basic Network sample (#120)
* [883ef99](https://github.com/hyperledger/fabric-samples/commit/883ef99) [FAB-17457](https://jira.hyperledger.org/browse/FAB-17457) Script correction (#119)
* [b89ee34](https://github.com/hyperledger/fabric-samples/commit/b89ee34) Update Commercial Paper to v2.0 Lifecycle (#109)
* [4208644](https://github.com/hyperledger/fabric-samples/commit/4208644) [FAB-17478](https://jira.hyperledger.org/browse/FAB-17478) Update commercial paper to use go api v1.0.0 (#115)
* [0df5ed9](https://github.com/hyperledger/fabric-samples/commit/0df5ed9) [FAB-17477](https://jira.hyperledger.org/browse/FAB-17477) Update fabcar to use go api v1.0.0 (#116)
* [571733f](https://github.com/hyperledger/fabric-samples/commit/571733f) [FAB-17447](https://jira.hyperledger.org/browse/FAB-17447) Update to 2.0.0 Libraries
* [67b4ee7](https://github.com/hyperledger/fabric-samples/commit/67b4ee7) Add Org3 bugs in test network (#108)
* [5b93dd0](https://github.com/hyperledger/fabric-samples/commit/5b93dd0) [FAB-17140](https://jira.hyperledger.org/browse/FAB-17140) Add go commercial paper contract (#102)
* [4fe6a25](https://github.com/hyperledger/fabric-samples/commit/4fe6a25) [FABCI-482] Update Nexus URL's to Artifactory (#92)
* [1488fbb](https://github.com/hyperledger/fabric-samples/commit/1488fbb) Add 1.x versions of fabric to blacklisted versions
* [8ca279d](https://github.com/hyperledger/fabric-samples/commit/8ca279d) Add Support for Versioning NodeJS (#106)
* [b3b5267](https://github.com/hyperledger/fabric-samples/commit/b3b5267) [FAB-17243](https://jira.hyperledger.org/browse/FAB-17243) Add support for Fabric CA for Org3 on the (#91)
* [ce41ff7](https://github.com/hyperledger/fabric-samples/commit/ce41ff7) Remove references to vendoring chaincode from your gopath (#96)
* [4235d30](https://github.com/hyperledger/fabric-samples/commit/4235d30) [FAB-17306](https://jira.hyperledger.org/browse/FAB-17306) Fix artifact names in test-network (#97)
* [4c2a0a4](https://github.com/hyperledger/fabric-samples/commit/4c2a0a4) [FAB-16147](https://jira.hyperledger.org/browse/FAB-16147) Update Commercial Paper to work with v2 (#98)
* [6d9fd6f](https://github.com/hyperledger/fabric-samples/commit/6d9fd6f) Remove Gerrit reference
* [a026a4f](https://github.com/hyperledger/fabric-samples/commit/a026a4f) Fixed typo (#90)
* [cdb0e8b](https://github.com/hyperledger/fabric-samples/commit/cdb0e8b) TYPO (#89)
* [94ac8b6](https://github.com/hyperledger/fabric-samples/commit/94ac8b6) Update to use beta levels of modules (#88)
* [d848633](https://github.com/hyperledger/fabric-samples/commit/d848633) [FAB-16844](https://jira.hyperledger.org/browse/FAB-16844) Correct BYFN CC name
* [73267e1](https://github.com/hyperledger/fabric-samples/commit/73267e1) Fix test network bugs for adding org3
* [5d58254](https://github.com/hyperledger/fabric-samples/commit/5d58254) [FAB-17145](https://jira.hyperledger.org/browse/FAB-17145) Add test network to Fabric Samples
* [e9f2957](https://github.com/hyperledger/fabric-samples/commit/e9f2957) [FAB-17062](https://jira.hyperledger.org/browse/FAB-17062) Fix typos in Commercial Paper readme
* [36694d0](https://github.com/hyperledger/fabric-samples/commit/36694d0) [FAB-17121](https://jira.hyperledger.org/browse/FAB-17121) Use new bootstrap config in orderer
* [429f087](https://github.com/hyperledger/fabric-samples/commit/429f087) update fabcar go to new programming model
* [1467086](https://github.com/hyperledger/fabric-samples/commit/1467086) Bump eslint-utils
* [33f349a](https://github.com/hyperledger/fabric-samples/commit/33f349a) Remove Stalebot
* [6af43bf](https://github.com/hyperledger/fabric-samples/commit/6af43bf) Change stalebot settings
* [4880401](https://github.com/hyperledger/fabric-samples/commit/4880401) [FAB-16284](https://jira.hyperledger.org/browse/FAB-16284) Remove E2E file and -f option from BYFN
* [fe96f60](https://github.com/hyperledger/fabric-samples/commit/fe96f60) [FAB-16850](https://jira.hyperledger.org/browse/FAB-16850) Set up CI with Azure Pipelines
* [81aabf4](https://github.com/hyperledger/fabric-samples/commit/81aabf4) [FAB-16849](https://jira.hyperledger.org/browse/FAB-16849) Various updates for Java version of FabCar
* [a42b858](https://github.com/hyperledger/fabric-samples/commit/a42b858) Update FabCar to reflect wallet API changes
* [890f9ea](https://github.com/hyperledger/fabric-samples/commit/890f9ea) [FAB-16713](https://jira.hyperledger.org/browse/FAB-16713) Fix npm audit warnings
* [e48e804](https://github.com/hyperledger/fabric-samples/commit/e48e804) [FAB-16776](https://jira.hyperledger.org/browse/FAB-16776) Move BYFN up to V2_0 capabilities
* [7b65a25](https://github.com/hyperledger/fabric-samples/commit/7b65a25) [IN-68] Add default GitHub SECURITY policy
* [408e0e8](https://github.com/hyperledger/fabric-samples/commit/408e0e8) [FAB-16619](https://jira.hyperledger.org/browse/FAB-16619) Fix the policy warning
* [670d446](https://github.com/hyperledger/fabric-samples/commit/670d446) [FAB-16668](https://jira.hyperledger.org/browse/FAB-16668) fabcar chaincode modify console output
* [f2939e2](https://github.com/hyperledger/fabric-samples/commit/f2939e2) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for Commercial Paper sample
* [3d19014](https://github.com/hyperledger/fabric-samples/commit/3d19014) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for FabCar sample
* [e2b7cb7](https://github.com/hyperledger/fabric-samples/commit/e2b7cb7) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for abstore sample
* [db48612](https://github.com/hyperledger/fabric-samples/commit/db48612) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Increase chaincode execute timeout
* [521a7ff](https://github.com/hyperledger/fabric-samples/commit/521a7ff) [FAB-16607](https://jira.hyperledger.org/browse/FAB-16607) Update FabCar to reflect CC updates
* [c13a5ec](https://github.com/hyperledger/fabric-samples/commit/c13a5ec) [FAB-16528](https://jira.hyperledger.org/browse/FAB-16528) marbles private chaincode sync up
* [3fad853](https://github.com/hyperledger/fabric-samples/commit/3fad853) [FAB-16528](https://jira.hyperledger.org/browse/FAB-16528) marbles private chaincode sync up
* [8b9b82f](https://github.com/hyperledger/fabric-samples/commit/8b9b82f) [FAB-16489](https://jira.hyperledger.org/browse/FAB-16489) Add CODEOWNERS
* [a6ce915](https://github.com/hyperledger/fabric-samples/commit/a6ce915) [FAB-16487](https://jira.hyperledger.org/browse/FAB-16487) Update eslint
* [48082cf](https://github.com/hyperledger/fabric-samples/commit/48082cf) [FAB-16362](https://jira.hyperledger.org/browse/FAB-16362) adding chaincode excution comments
* [1d379f3](https://github.com/hyperledger/fabric-samples/commit/1d379f3) [FAB-16474](https://jira.hyperledger.org/browse/FAB-16474) marbles02 chaincode error
* [18712ca](https://github.com/hyperledger/fabric-samples/commit/18712ca) [FAB-16133](https://jira.hyperledger.org/browse/FAB-16133) Remove Solo consensus from BYFN
* [91c720a](https://github.com/hyperledger/fabric-samples/commit/91c720a) [FAB-16390](https://jira.hyperledger.org/browse/FAB-16390) Added filter for invalid transactions
* [1d3e267](https://github.com/hyperledger/fabric-samples/commit/1d3e267) Redirect samples to fabric-{chaincode,protos}-go
* [398a5b1](https://github.com/hyperledger/fabric-samples/commit/398a5b1) [FABCI-394] Remove AnsiColor Wrapper
* [ce154e0](https://github.com/hyperledger/fabric-samples/commit/ce154e0) [FAB-16310](https://jira.hyperledger.org/browse/FAB-16310) Vendor Go dependencies in all samples
* [6ea7c71](https://github.com/hyperledger/fabric-samples/commit/6ea7c71) [FAB-16285](https://jira.hyperledger.org/browse/FAB-16285) Update blacklisted versions in BYFN
* [86cd831](https://github.com/hyperledger/fabric-samples/commit/86cd831) [FAB-16284](https://jira.hyperledger.org/browse/FAB-16284) Remove E2E file and -f option from BYFN
* [0063abe](https://github.com/hyperledger/fabric-samples/commit/0063abe) Update stale script name in interest rate swaps
* [3907507](https://github.com/hyperledger/fabric-samples/commit/3907507) [FAB-16277](https://jira.hyperledger.org/browse/FAB-16277) Update BYFN w/ Raft ports in Docker network
* [33b0065](https://github.com/hyperledger/fabric-samples/commit/33b0065) [FAB-14813](https://jira.hyperledger.org/browse/FAB-14813) Channel event sample in fabric-samples
* [b62d5bd](https://github.com/hyperledger/fabric-samples/commit/b62d5bd) [FAB-16132](https://jira.hyperledger.org/browse/FAB-16132) Remove Kafka consensus from BYFN
* [9b14525](https://github.com/hyperledger/fabric-samples/commit/9b14525) [FAB-15213](https://jira.hyperledger.org/browse/FAB-15213) Update Commercial Paper for Java
* [4158877](https://github.com/hyperledger/fabric-samples/commit/4158877) [FAB-16232](https://jira.hyperledger.org/browse/FAB-16232) Remove FabToken sample
* [b6380cc](https://github.com/hyperledger/fabric-samples/commit/b6380cc) [FAB-16198](https://jira.hyperledger.org/browse/FAB-16198) Run "go mod vendor" for FabCar Go contract
* [639848a](https://github.com/hyperledger/fabric-samples/commit/639848a) [FAB-16197](https://jira.hyperledger.org/browse/FAB-16197) Add connection profiles to .gitignore
* [3996db5](https://github.com/hyperledger/fabric-samples/commit/3996db5) [FAB-12219](https://jira.hyperledger.org/browse/FAB-12219) abstore node -> javascript
* [14ac271](https://github.com/hyperledger/fabric-samples/commit/14ac271) [FAB-12219](https://jira.hyperledger.org/browse/FAB-12219) marbles02 node -> javascript
* [13f16e5](https://github.com/hyperledger/fabric-samples/commit/13f16e5) [FGJ-4] CI tests for FabCar Java sample
* [171a7d2](https://github.com/hyperledger/fabric-samples/commit/171a7d2) FGJ-4 Fabcar sample
* [868f9d0](https://github.com/hyperledger/fabric-samples/commit/868f9d0) [FAB-15625](https://jira.hyperledger.org/browse/FAB-15625) Add UT for Simple Asset Chaincode
* [597d150](https://github.com/hyperledger/fabric-samples/commit/597d150) [FAB-14532](https://jira.hyperledger.org/browse/FAB-14532) Remove LL FabCar sample
* [583ff8f](https://github.com/hyperledger/fabric-samples/commit/583ff8f) Use renamed CheckCommitReadiness function
* [750f937](https://github.com/hyperledger/fabric-samples/commit/750f937) [FAB-15213](https://jira.hyperledger.org/browse/FAB-15213) Add Java FabCar sample contract
* [abbda95](https://github.com/hyperledger/fabric-samples/commit/abbda95) [FAB-15897](https://jira.hyperledger.org/browse/FAB-15897) Improve FabCar test logging
* [dd8150a](https://github.com/hyperledger/fabric-samples/commit/dd8150a) [FAB-15104](https://jira.hyperledger.org/browse/FAB-15104) Remove versions from fabric-samples readme
* [1387aa8](https://github.com/hyperledger/fabric-samples/commit/1387aa8) [FAB-15927](https://jira.hyperledger.org/browse/FAB-15927)  Better expression for golang
* [61c33d3](https://github.com/hyperledger/fabric-samples/commit/61c33d3) [FAB-15973](https://jira.hyperledger.org/browse/FAB-15973) use --output json on simulatecommit
* [8bbdd0f](https://github.com/hyperledger/fabric-samples/commit/8bbdd0f) [FAB-15716](https://jira.hyperledger.org/browse/FAB-15716) Fix instructions for dev-mode
* [0254d67](https://github.com/hyperledger/fabric-samples/commit/0254d67) QueryApprovalStatus -> SimulateCommitChaincodeDef
* [c57d67c](https://github.com/hyperledger/fabric-samples/commit/c57d67c) [FAB-15782](https://jira.hyperledger.org/browse/FAB-15782) Sample Go CC should include deps
* [6ba5a19](https://github.com/hyperledger/fabric-samples/commit/6ba5a19) Update to Go 1.12.5 in ci.properties
* [1774a25](https://github.com/hyperledger/fabric-samples/commit/1774a25) [FAB-15723](https://jira.hyperledger.org/browse/FAB-15723) Fix script and instruction with ccenv
* [6ae711c](https://github.com/hyperledger/fabric-samples/commit/6ae711c) [FAB-15717](https://jira.hyperledger.org/browse/FAB-15717) fix Error Unexpected end of JSON input
* [5be56d3](https://github.com/hyperledger/fabric-samples/commit/5be56d3) [FAB-15104](https://jira.hyperledger.org/browse/FAB-15104) Remove scripts/bootstrap.sh
* [779f8f3](https://github.com/hyperledger/fabric-samples/commit/779f8f3) [FAB-15649](https://jira.hyperledger.org/browse/FAB-15649)Fix Fabcar to install Chaincode on all peers
* [7c5f5d3](https://github.com/hyperledger/fabric-samples/commit/7c5f5d3) [FAB-15199](https://jira.hyperledger.org/browse/FAB-15199) Update interest rate sample
* [f0dca20](https://github.com/hyperledger/fabric-samples/commit/f0dca20) [FAB-14532](https://jira.hyperledger.org/browse/FAB-14532) Remove LL FabCar sample
* [1ed1a10](https://github.com/hyperledger/fabric-samples/commit/1ed1a10) [FAB-15573](https://jira.hyperledger.org/browse/FAB-15573) Fix typo in fabric-samples-ci.md
* [2e7fec9](https://github.com/hyperledger/fabric-samples/commit/2e7fec9) [FAB-15601](https://jira.hyperledger.org/browse/FAB-15601) BYFN: Fix MAX_RETRY for couchdb
* [1e9e4c4](https://github.com/hyperledger/fabric-samples/commit/1e9e4c4) [FAB-9329](https://jira.hyperledger.org/browse/FAB-9329) Remove the unused variable in BYFN/EYFN
* [964c09f](https://github.com/hyperledger/fabric-samples/commit/964c09f) [FAB-15601](https://jira.hyperledger.org/browse/FAB-15601) BYFN: Fix MAX_RETRY for couchdb
* [41dca99](https://github.com/hyperledger/fabric-samples/commit/41dca99) [FAB-15127](https://jira.hyperledger.org/browse/FAB-15127) Update high throughput sample
* [3fe014a](https://github.com/hyperledger/fabric-samples/commit/3fe014a) Use official CouchDB image
* [f2d0fa0](https://github.com/hyperledger/fabric-samples/commit/f2d0fa0) [FAB-14487](https://jira.hyperledger.org/browse/FAB-14487) Make FabCar use BYFN, not basic-network
* [e9c3649](https://github.com/hyperledger/fabric-samples/commit/e9c3649) [FAB-15276](https://jira.hyperledger.org/browse/FAB-15276) Fix license statements
* [fbe4036](https://github.com/hyperledger/fabric-samples/commit/fbe4036) [FAB-14486](https://jira.hyperledger.org/browse/FAB-14486) Extend BYFN to opt skip chaincode deploy
* [0c4141f](https://github.com/hyperledger/fabric-samples/commit/0c4141f) [FAB-14485](https://jira.hyperledger.org/browse/FAB-14485) Extend BYFN to opt inc cert authorities
* [529b83b](https://github.com/hyperledger/fabric-samples/commit/529b83b) [FAB-14330](https://jira.hyperledger.org/browse/FAB-14330) Add connection profiles for BYFN and EYFN
* [2c21c83](https://github.com/hyperledger/fabric-samples/commit/2c21c83) [FABN-1184] Update fabtoken/README.md
* [5056a23](https://github.com/hyperledger/fabric-samples/commit/5056a23) [FABN-1184] Add CI script for fabtoken sample app
* [5d6db95](https://github.com/hyperledger/fabric-samples/commit/5d6db95) Update maintainers for fabric-samples
* [f527815](https://github.com/hyperledger/fabric-samples/commit/f527815) [FAB-15119](https://jira.hyperledger.org/browse/FAB-15119) Fix BYFN with Java chaincode
* [8245252](https://github.com/hyperledger/fabric-samples/commit/8245252) [FABN-1184] Implement fabtoken sample app
* [1bd1c2f](https://github.com/hyperledger/fabric-samples/commit/1bd1c2f) FABCI-284 Update CI Pipeline script
* [c24abf9](https://github.com/hyperledger/fabric-samples/commit/c24abf9) [FAB-15022](https://jira.hyperledger.org/browse/FAB-15022) Basic-network support for new lifecycle
* [b64fd45](https://github.com/hyperledger/fabric-samples/commit/b64fd45) [FAB-15051](https://jira.hyperledger.org/browse/FAB-15051) delStandard() function for high-throughput
* [3e68a7e](https://github.com/hyperledger/fabric-samples/commit/3e68a7e) [FAB-14784](https://jira.hyperledger.org/browse/FAB-14784) Remove balance-transfer
* [eb3fe08](https://github.com/hyperledger/fabric-samples/commit/eb3fe08) [FAB-14779](https://jira.hyperledger.org/browse/FAB-14779) QueryApprovalStatus step in byfn
* [2777429](https://github.com/hyperledger/fabric-samples/commit/2777429) [FAB-14711](https://jira.hyperledger.org/browse/FAB-14711) update byfn with new lifecycle
* [aec3389](https://github.com/hyperledger/fabric-samples/commit/aec3389) [FAB-12215](https://jira.hyperledger.org/browse/FAB-12215)WYFA:Remove chainId in tx proposal request
* [b5d5026](https://github.com/hyperledger/fabric-samples/commit/b5d5026) [FAB-14633](https://jira.hyperledger.org/browse/FAB-14633) Remove apt-get from eyfn.sh
* [efaadd3](https://github.com/hyperledger/fabric-samples/commit/efaadd3) [FAB-14531](https://jira.hyperledger.org/browse/FAB-14531) BYFN Raft with 5 nodes
* [d63047c](https://github.com/hyperledger/fabric-samples/commit/d63047c) [FAB-14444](https://jira.hyperledger.org/browse/FAB-14444)
* [7e3d428](https://github.com/hyperledger/fabric-samples/commit/7e3d428) [FAB-14369](https://jira.hyperledger.org/browse/FAB-14369)Fix dev mode failing to build Chaincode
* [420ba23](https://github.com/hyperledger/fabric-samples/commit/420ba23) [FAB-12762](https://jira.hyperledger.org/browse/FAB-12762) Add etcd/raft consensus option to BYFN
* [2b68c80](https://github.com/hyperledger/fabric-samples/commit/2b68c80) [FAB-14317](https://jira.hyperledger.org/browse/FAB-14317) Add default policies to org3
* [f942010](https://github.com/hyperledger/fabric-samples/commit/f942010) [FAB-14268](https://jira.hyperledger.org/browse/FAB-14268) Make BYFN/EYFN ports match external ports
* [4e2ce23](https://github.com/hyperledger/fabric-samples/commit/4e2ce23) [FAB-14271](https://jira.hyperledger.org/browse/FAB-14271) Add channel policies to channel config
* [f26477c](https://github.com/hyperledger/fabric-samples/commit/f26477c) [FAB-11796](https://jira.hyperledger.org/browse/FAB-11796)high-throughput:Remove unnecessary prunesafe
* [137327a](https://github.com/hyperledger/fabric-samples/commit/137327a) [FAB-14162](https://jira.hyperledger.org/browse/FAB-14162) Pin fabric-samples in master to "unstable"
* [6007c09](https://github.com/hyperledger/fabric-samples/commit/6007c09) [FAB-13862](https://jira.hyperledger.org/browse/FAB-13862) Rename example02 ABstore
* [94cb603](https://github.com/hyperledger/fabric-samples/commit/94cb603) [FAB-13933](https://jira.hyperledger.org/browse/FAB-13933) Fix misspellings
* [a8a5539](https://github.com/hyperledger/fabric-samples/commit/a8a5539) Fix doc link Fix variable error
* [b0cda61](https://github.com/hyperledger/fabric-samples/commit/b0cda61) [FAB-13769](https://jira.hyperledger.org/browse/FAB-13769) Add UT code to ABAC sample Chaincode
* [c7438e1](https://github.com/hyperledger/fabric-samples/commit/c7438e1) [FAB-13668](https://jira.hyperledger.org/browse/FAB-13668) BYFN's container volume mapping is bad
* [e48b2de](https://github.com/hyperledger/fabric-samples/commit/e48b2de) [FAB-13489](https://jira.hyperledger.org/browse/FAB-13489) fabric-samples add error msg
* [6269941](https://github.com/hyperledger/fabric-samples/commit/6269941) Correct broken link

## "v1.4.2"

* [11e4c23](https://github.com/hyperledger/fabric-samples/commit/11e4c23) Update samples to use v2.0 or later dependencies (#161)
* [94beab7](https://github.com/hyperledger/fabric-samples/commit/94beab7) FABN-1531 Use v2.1.0 sdk-node modules
* [8820d2f](https://github.com/hyperledger/fabric-samples/commit/8820d2f) Fix commercial-paper README
* [aa9b577](https://github.com/hyperledger/fabric-samples/commit/aa9b577) Remove TLS enabled switch (#155)
* [381fb46](https://github.com/hyperledger/fabric-samples/commit/381fb46) upgraded abstore golang chaincode to use contract-api package (#154)
* [5e5d2c8](https://github.com/hyperledger/fabric-samples/commit/5e5d2c8) Update java chaincode to be compatible with doc and other implementations (#149)
* [c572c51](https://github.com/hyperledger/fabric-samples/commit/c572c51) Organize and Standardize `ci` Directory Content (#152)
* [aa40963](https://github.com/hyperledger/fabric-samples/commit/aa40963) Perform General Cleanup (#151)
* [da41afa](https://github.com/hyperledger/fabric-samples/commit/da41afa) Remove left over rm -rf command from BYFN (#148)
* [4bb48a9](https://github.com/hyperledger/fabric-samples/commit/4bb48a9) Jenkins no longer used (#145)
* [6f984e1](https://github.com/hyperledger/fabric-samples/commit/6f984e1) Bump acorn from 6.4.0 to 6.4.1 in /fabcar/javascript (#144)
* [b155620](https://github.com/hyperledger/fabric-samples/commit/b155620) Remove redundant invoke command from test network (#142)
* [851933b](https://github.com/hyperledger/fabric-samples/commit/851933b) Add enrollUser files to commercial paper (#140)
* [87600bd](https://github.com/hyperledger/fabric-samples/commit/87600bd) [FAB-17268](https://jira.hyperledger.org/browse/FAB-17268) Move fabcar sample to test network (#103)
* [9397788](https://github.com/hyperledger/fabric-samples/commit/9397788) Wrong groupId on hyperledger fabric dependencies for java-application (#134)
* [92555fb](https://github.com/hyperledger/fabric-samples/commit/92555fb) Update README.md (#133)
* [59c6641](https://github.com/hyperledger/fabric-samples/commit/59c6641) Change Download Location of Fabric Binaries (#143)
* [1f283fc](https://github.com/hyperledger/fabric-samples/commit/1f283fc) init function does not exist on fabcar (#141)
* [defb6bb](https://github.com/hyperledger/fabric-samples/commit/defb6bb) [FAB-17656](https://jira.hyperledger.org/browse/FAB-17656) echo Generating channel.tx (#139)
* [4c7bab0](https://github.com/hyperledger/fabric-samples/commit/4c7bab0) fix: package seletor REGEX (#135)
* [db69c6f](https://github.com/hyperledger/fabric-samples/commit/db69c6f) Add fabcar external service sample (#136)
* [7f5f5e6](https://github.com/hyperledger/fabric-samples/commit/7f5f5e6) [FAB-17504](https://jira.hyperledger.org/browse/FAB-17504) add Organizations.<Org>.OrdererEndpoints and remove Orderer.Addresses (#125)
* [f3fc08d](https://github.com/hyperledger/fabric-samples/commit/f3fc08d) Remove solo and kafka from test net configtx.yaml (#137)
* [e17574d](https://github.com/hyperledger/fabric-samples/commit/e17574d) Add CA's to docker test network (#124)
* [faac18e](https://github.com/hyperledger/fabric-samples/commit/faac18e) [FAB-17461](https://jira.hyperledger.org/browse/FAB-17461) Move off_chain_data sample to test network (#122)
* [121a44a](https://github.com/hyperledger/fabric-samples/commit/121a44a) [FAB-17460](https://jira.hyperledger.org/browse/FAB-17460) Move High Throughput sample to test network (#112)
* [a2f3a66](https://github.com/hyperledger/fabric-samples/commit/a2f3a66) Update docker image version
* [e5b898c](https://github.com/hyperledger/fabric-samples/commit/e5b898c) Revert "first-network/scripts/*: Make Chaincode name configurable (#118)" (#131)
* [9ef61e2](https://github.com/hyperledger/fabric-samples/commit/9ef61e2) first-network/scripts/*: Make Chaincode name configurable (#118)
* [e204ebb](https://github.com/hyperledger/fabric-samples/commit/e204ebb) Remove reference to 2.0 beta (#111)
* [3dbe116](https://github.com/hyperledger/fabric-samples/commit/3dbe116) [FAB-17456](https://jira.hyperledger.org/browse/FAB-17456) fabric-samples read ccp (#117)
* [965ed1f](https://github.com/hyperledger/fabric-samples/commit/965ed1f) [FAB-17498](https://jira.hyperledger.org/browse/FAB-17498) Beta Images removal, test test-network (#121)
* [403019e](https://github.com/hyperledger/fabric-samples/commit/403019e) [FAB-17495](https://jira.hyperledger.org/browse/FAB-17495) Remove Basic Network sample (#120)
* [883ef99](https://github.com/hyperledger/fabric-samples/commit/883ef99) [FAB-17457](https://jira.hyperledger.org/browse/FAB-17457) Script correction (#119)
* [b89ee34](https://github.com/hyperledger/fabric-samples/commit/b89ee34) Update Commercial Paper to v2.0 Lifecycle (#109)
* [4208644](https://github.com/hyperledger/fabric-samples/commit/4208644) [FAB-17478](https://jira.hyperledger.org/browse/FAB-17478) Update commercial paper to use go api v1.0.0 (#115)
* [0df5ed9](https://github.com/hyperledger/fabric-samples/commit/0df5ed9) [FAB-17477](https://jira.hyperledger.org/browse/FAB-17477) Update fabcar to use go api v1.0.0 (#116)
* [571733f](https://github.com/hyperledger/fabric-samples/commit/571733f) [FAB-17447](https://jira.hyperledger.org/browse/FAB-17447) Update to 2.0.0 Libraries
* [67b4ee7](https://github.com/hyperledger/fabric-samples/commit/67b4ee7) Add Org3 bugs in test network (#108)
* [5b93dd0](https://github.com/hyperledger/fabric-samples/commit/5b93dd0) [FAB-17140](https://jira.hyperledger.org/browse/FAB-17140) Add go commercial paper contract (#102)
* [4fe6a25](https://github.com/hyperledger/fabric-samples/commit/4fe6a25) [FABCI-482] Update Nexus URL's to Artifactory (#92)
* [1488fbb](https://github.com/hyperledger/fabric-samples/commit/1488fbb) Add 1.x versions of fabric to blacklisted versions
* [8ca279d](https://github.com/hyperledger/fabric-samples/commit/8ca279d) Add Support for Versioning NodeJS (#106)
* [b3b5267](https://github.com/hyperledger/fabric-samples/commit/b3b5267) [FAB-17243](https://jira.hyperledger.org/browse/FAB-17243) Add support for Fabric CA for Org3 on the (#91)
* [ce41ff7](https://github.com/hyperledger/fabric-samples/commit/ce41ff7) Remove references to vendoring chaincode from your gopath (#96)
* [4235d30](https://github.com/hyperledger/fabric-samples/commit/4235d30) [FAB-17306](https://jira.hyperledger.org/browse/FAB-17306) Fix artifact names in test-network (#97)
* [4c2a0a4](https://github.com/hyperledger/fabric-samples/commit/4c2a0a4) [FAB-16147](https://jira.hyperledger.org/browse/FAB-16147) Update Commercial Paper to work with v2 (#98)
* [6d9fd6f](https://github.com/hyperledger/fabric-samples/commit/6d9fd6f) Remove Gerrit reference
* [a026a4f](https://github.com/hyperledger/fabric-samples/commit/a026a4f) Fixed typo (#90)
* [cdb0e8b](https://github.com/hyperledger/fabric-samples/commit/cdb0e8b) TYPO (#89)
* [94ac8b6](https://github.com/hyperledger/fabric-samples/commit/94ac8b6) Update to use beta levels of modules (#88)
* [d848633](https://github.com/hyperledger/fabric-samples/commit/d848633) [FAB-16844](https://jira.hyperledger.org/browse/FAB-16844) Correct BYFN CC name
* [73267e1](https://github.com/hyperledger/fabric-samples/commit/73267e1) Fix test network bugs for adding org3
* [5d58254](https://github.com/hyperledger/fabric-samples/commit/5d58254) [FAB-17145](https://jira.hyperledger.org/browse/FAB-17145) Add test network to Fabric Samples
* [e9f2957](https://github.com/hyperledger/fabric-samples/commit/e9f2957) [FAB-17062](https://jira.hyperledger.org/browse/FAB-17062) Fix typos in Commercial Paper readme
* [36694d0](https://github.com/hyperledger/fabric-samples/commit/36694d0) [FAB-17121](https://jira.hyperledger.org/browse/FAB-17121) Use new bootstrap config in orderer
* [429f087](https://github.com/hyperledger/fabric-samples/commit/429f087) update fabcar go to new programming model
* [1467086](https://github.com/hyperledger/fabric-samples/commit/1467086) Bump eslint-utils
* [33f349a](https://github.com/hyperledger/fabric-samples/commit/33f349a) Remove Stalebot
* [6af43bf](https://github.com/hyperledger/fabric-samples/commit/6af43bf) Change stalebot settings
* [4880401](https://github.com/hyperledger/fabric-samples/commit/4880401) [FAB-16284](https://jira.hyperledger.org/browse/FAB-16284) Remove E2E file and -f option from BYFN
* [fe96f60](https://github.com/hyperledger/fabric-samples/commit/fe96f60) [FAB-16850](https://jira.hyperledger.org/browse/FAB-16850) Set up CI with Azure Pipelines
* [81aabf4](https://github.com/hyperledger/fabric-samples/commit/81aabf4) [FAB-16849](https://jira.hyperledger.org/browse/FAB-16849) Various updates for Java version of FabCar
* [a42b858](https://github.com/hyperledger/fabric-samples/commit/a42b858) Update FabCar to reflect wallet API changes
* [890f9ea](https://github.com/hyperledger/fabric-samples/commit/890f9ea) [FAB-16713](https://jira.hyperledger.org/browse/FAB-16713) Fix npm audit warnings
* [e48e804](https://github.com/hyperledger/fabric-samples/commit/e48e804) [FAB-16776](https://jira.hyperledger.org/browse/FAB-16776) Move BYFN up to V2_0 capabilities
* [7b65a25](https://github.com/hyperledger/fabric-samples/commit/7b65a25) [IN-68] Add default GitHub SECURITY policy
* [408e0e8](https://github.com/hyperledger/fabric-samples/commit/408e0e8) [FAB-16619](https://jira.hyperledger.org/browse/FAB-16619) Fix the policy warning
* [670d446](https://github.com/hyperledger/fabric-samples/commit/670d446) [FAB-16668](https://jira.hyperledger.org/browse/FAB-16668) fabcar chaincode modify console output
* [f2939e2](https://github.com/hyperledger/fabric-samples/commit/f2939e2) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for Commercial Paper sample
* [3d19014](https://github.com/hyperledger/fabric-samples/commit/3d19014) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for FabCar sample
* [e2b7cb7](https://github.com/hyperledger/fabric-samples/commit/e2b7cb7) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for abstore sample
* [db48612](https://github.com/hyperledger/fabric-samples/commit/db48612) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Increase chaincode execute timeout
* [521a7ff](https://github.com/hyperledger/fabric-samples/commit/521a7ff) [FAB-16607](https://jira.hyperledger.org/browse/FAB-16607) Update FabCar to reflect CC updates
* [c13a5ec](https://github.com/hyperledger/fabric-samples/commit/c13a5ec) [FAB-16528](https://jira.hyperledger.org/browse/FAB-16528) marbles private chaincode sync up
* [3fad853](https://github.com/hyperledger/fabric-samples/commit/3fad853) [FAB-16528](https://jira.hyperledger.org/browse/FAB-16528) marbles private chaincode sync up
* [8b9b82f](https://github.com/hyperledger/fabric-samples/commit/8b9b82f) [FAB-16489](https://jira.hyperledger.org/browse/FAB-16489) Add CODEOWNERS
* [a6ce915](https://github.com/hyperledger/fabric-samples/commit/a6ce915) [FAB-16487](https://jira.hyperledger.org/browse/FAB-16487) Update eslint
* [48082cf](https://github.com/hyperledger/fabric-samples/commit/48082cf) [FAB-16362](https://jira.hyperledger.org/browse/FAB-16362) adding chaincode excution comments
* [1d379f3](https://github.com/hyperledger/fabric-samples/commit/1d379f3) [FAB-16474](https://jira.hyperledger.org/browse/FAB-16474) marbles02 chaincode error
* [18712ca](https://github.com/hyperledger/fabric-samples/commit/18712ca) [FAB-16133](https://jira.hyperledger.org/browse/FAB-16133) Remove Solo consensus from BYFN
* [91c720a](https://github.com/hyperledger/fabric-samples/commit/91c720a) [FAB-16390](https://jira.hyperledger.org/browse/FAB-16390) Added filter for invalid transactions
* [1d3e267](https://github.com/hyperledger/fabric-samples/commit/1d3e267) Redirect samples to fabric-{chaincode,protos}-go
* [398a5b1](https://github.com/hyperledger/fabric-samples/commit/398a5b1) [FABCI-394] Remove AnsiColor Wrapper
* [ce154e0](https://github.com/hyperledger/fabric-samples/commit/ce154e0) [FAB-16310](https://jira.hyperledger.org/browse/FAB-16310) Vendor Go dependencies in all samples
* [6ea7c71](https://github.com/hyperledger/fabric-samples/commit/6ea7c71) [FAB-16285](https://jira.hyperledger.org/browse/FAB-16285) Update blacklisted versions in BYFN
* [86cd831](https://github.com/hyperledger/fabric-samples/commit/86cd831) [FAB-16284](https://jira.hyperledger.org/browse/FAB-16284) Remove E2E file and -f option from BYFN
* [0063abe](https://github.com/hyperledger/fabric-samples/commit/0063abe) Update stale script name in interest rate swaps
* [3907507](https://github.com/hyperledger/fabric-samples/commit/3907507) [FAB-16277](https://jira.hyperledger.org/browse/FAB-16277) Update BYFN w/ Raft ports in Docker network
* [33b0065](https://github.com/hyperledger/fabric-samples/commit/33b0065) [FAB-14813](https://jira.hyperledger.org/browse/FAB-14813) Channel event sample in fabric-samples
* [b62d5bd](https://github.com/hyperledger/fabric-samples/commit/b62d5bd) [FAB-16132](https://jira.hyperledger.org/browse/FAB-16132) Remove Kafka consensus from BYFN
* [9b14525](https://github.com/hyperledger/fabric-samples/commit/9b14525) [FAB-15213](https://jira.hyperledger.org/browse/FAB-15213) Update Commercial Paper for Java
* [4158877](https://github.com/hyperledger/fabric-samples/commit/4158877) [FAB-16232](https://jira.hyperledger.org/browse/FAB-16232) Remove FabToken sample
* [b6380cc](https://github.com/hyperledger/fabric-samples/commit/b6380cc) [FAB-16198](https://jira.hyperledger.org/browse/FAB-16198) Run "go mod vendor" for FabCar Go contract
* [639848a](https://github.com/hyperledger/fabric-samples/commit/639848a) [FAB-16197](https://jira.hyperledger.org/browse/FAB-16197) Add connection profiles to .gitignore
* [3996db5](https://github.com/hyperledger/fabric-samples/commit/3996db5) [FAB-12219](https://jira.hyperledger.org/browse/FAB-12219) abstore node -> javascript
* [14ac271](https://github.com/hyperledger/fabric-samples/commit/14ac271) [FAB-12219](https://jira.hyperledger.org/browse/FAB-12219) marbles02 node -> javascript
* [13f16e5](https://github.com/hyperledger/fabric-samples/commit/13f16e5) [FGJ-4] CI tests for FabCar Java sample
* [171a7d2](https://github.com/hyperledger/fabric-samples/commit/171a7d2) FGJ-4 Fabcar sample
* [868f9d0](https://github.com/hyperledger/fabric-samples/commit/868f9d0) [FAB-15625](https://jira.hyperledger.org/browse/FAB-15625) Add UT for Simple Asset Chaincode
* [597d150](https://github.com/hyperledger/fabric-samples/commit/597d150) [FAB-14532](https://jira.hyperledger.org/browse/FAB-14532) Remove LL FabCar sample
* [583ff8f](https://github.com/hyperledger/fabric-samples/commit/583ff8f) Use renamed CheckCommitReadiness function
* [750f937](https://github.com/hyperledger/fabric-samples/commit/750f937) [FAB-15213](https://jira.hyperledger.org/browse/FAB-15213) Add Java FabCar sample contract
* [abbda95](https://github.com/hyperledger/fabric-samples/commit/abbda95) [FAB-15897](https://jira.hyperledger.org/browse/FAB-15897) Improve FabCar test logging
* [dd8150a](https://github.com/hyperledger/fabric-samples/commit/dd8150a) [FAB-15104](https://jira.hyperledger.org/browse/FAB-15104) Remove versions from fabric-samples readme
* [1387aa8](https://github.com/hyperledger/fabric-samples/commit/1387aa8) [FAB-15927](https://jira.hyperledger.org/browse/FAB-15927)  Better expression for golang
* [61c33d3](https://github.com/hyperledger/fabric-samples/commit/61c33d3) [FAB-15973](https://jira.hyperledger.org/browse/FAB-15973) use --output json on simulatecommit
* [8bbdd0f](https://github.com/hyperledger/fabric-samples/commit/8bbdd0f) [FAB-15716](https://jira.hyperledger.org/browse/FAB-15716) Fix instructions for dev-mode
* [0254d67](https://github.com/hyperledger/fabric-samples/commit/0254d67) QueryApprovalStatus -> SimulateCommitChaincodeDef
* [c57d67c](https://github.com/hyperledger/fabric-samples/commit/c57d67c) [FAB-15782](https://jira.hyperledger.org/browse/FAB-15782) Sample Go CC should include deps
* [6ba5a19](https://github.com/hyperledger/fabric-samples/commit/6ba5a19) Update to Go 1.12.5 in ci.properties
* [1774a25](https://github.com/hyperledger/fabric-samples/commit/1774a25) [FAB-15723](https://jira.hyperledger.org/browse/FAB-15723) Fix script and instruction with ccenv
* [6ae711c](https://github.com/hyperledger/fabric-samples/commit/6ae711c) [FAB-15717](https://jira.hyperledger.org/browse/FAB-15717) fix Error Unexpected end of JSON input
* [5be56d3](https://github.com/hyperledger/fabric-samples/commit/5be56d3) [FAB-15104](https://jira.hyperledger.org/browse/FAB-15104) Remove scripts/bootstrap.sh
* [779f8f3](https://github.com/hyperledger/fabric-samples/commit/779f8f3) [FAB-15649](https://jira.hyperledger.org/browse/FAB-15649)Fix Fabcar to install Chaincode on all peers
* [7c5f5d3](https://github.com/hyperledger/fabric-samples/commit/7c5f5d3) [FAB-15199](https://jira.hyperledger.org/browse/FAB-15199) Update interest rate sample
* [f0dca20](https://github.com/hyperledger/fabric-samples/commit/f0dca20) [FAB-14532](https://jira.hyperledger.org/browse/FAB-14532) Remove LL FabCar sample
* [1ed1a10](https://github.com/hyperledger/fabric-samples/commit/1ed1a10) [FAB-15573](https://jira.hyperledger.org/browse/FAB-15573) Fix typo in fabric-samples-ci.md
* [2e7fec9](https://github.com/hyperledger/fabric-samples/commit/2e7fec9) [FAB-15601](https://jira.hyperledger.org/browse/FAB-15601) BYFN: Fix MAX_RETRY for couchdb
* [1e9e4c4](https://github.com/hyperledger/fabric-samples/commit/1e9e4c4) [FAB-9329](https://jira.hyperledger.org/browse/FAB-9329) Remove the unused variable in BYFN/EYFN
* [964c09f](https://github.com/hyperledger/fabric-samples/commit/964c09f) [FAB-15601](https://jira.hyperledger.org/browse/FAB-15601) BYFN: Fix MAX_RETRY for couchdb
* [41dca99](https://github.com/hyperledger/fabric-samples/commit/41dca99) [FAB-15127](https://jira.hyperledger.org/browse/FAB-15127) Update high throughput sample
* [3fe014a](https://github.com/hyperledger/fabric-samples/commit/3fe014a) Use official CouchDB image
* [f2d0fa0](https://github.com/hyperledger/fabric-samples/commit/f2d0fa0) [FAB-14487](https://jira.hyperledger.org/browse/FAB-14487) Make FabCar use BYFN, not basic-network
* [e9c3649](https://github.com/hyperledger/fabric-samples/commit/e9c3649) [FAB-15276](https://jira.hyperledger.org/browse/FAB-15276) Fix license statements
* [fbe4036](https://github.com/hyperledger/fabric-samples/commit/fbe4036) [FAB-14486](https://jira.hyperledger.org/browse/FAB-14486) Extend BYFN to opt skip chaincode deploy
* [0c4141f](https://github.com/hyperledger/fabric-samples/commit/0c4141f) [FAB-14485](https://jira.hyperledger.org/browse/FAB-14485) Extend BYFN to opt inc cert authorities
* [529b83b](https://github.com/hyperledger/fabric-samples/commit/529b83b) [FAB-14330](https://jira.hyperledger.org/browse/FAB-14330) Add connection profiles for BYFN and EYFN
* [2c21c83](https://github.com/hyperledger/fabric-samples/commit/2c21c83) [FABN-1184] Update fabtoken/README.md
* [5056a23](https://github.com/hyperledger/fabric-samples/commit/5056a23) [FABN-1184] Add CI script for fabtoken sample app
* [5d6db95](https://github.com/hyperledger/fabric-samples/commit/5d6db95) Update maintainers for fabric-samples
* [f527815](https://github.com/hyperledger/fabric-samples/commit/f527815) [FAB-15119](https://jira.hyperledger.org/browse/FAB-15119) Fix BYFN with Java chaincode
* [8245252](https://github.com/hyperledger/fabric-samples/commit/8245252) [FABN-1184] Implement fabtoken sample app
* [1bd1c2f](https://github.com/hyperledger/fabric-samples/commit/1bd1c2f) FABCI-284 Update CI Pipeline script
* [c24abf9](https://github.com/hyperledger/fabric-samples/commit/c24abf9) [FAB-15022](https://jira.hyperledger.org/browse/FAB-15022) Basic-network support for new lifecycle
* [b64fd45](https://github.com/hyperledger/fabric-samples/commit/b64fd45) [FAB-15051](https://jira.hyperledger.org/browse/FAB-15051) delStandard() function for high-throughput
* [3e68a7e](https://github.com/hyperledger/fabric-samples/commit/3e68a7e) [FAB-14784](https://jira.hyperledger.org/browse/FAB-14784) Remove balance-transfer
* [eb3fe08](https://github.com/hyperledger/fabric-samples/commit/eb3fe08) [FAB-14779](https://jira.hyperledger.org/browse/FAB-14779) QueryApprovalStatus step in byfn
* [2777429](https://github.com/hyperledger/fabric-samples/commit/2777429) [FAB-14711](https://jira.hyperledger.org/browse/FAB-14711) update byfn with new lifecycle
* [aec3389](https://github.com/hyperledger/fabric-samples/commit/aec3389) [FAB-12215](https://jira.hyperledger.org/browse/FAB-12215)WYFA:Remove chainId in tx proposal request
* [b5d5026](https://github.com/hyperledger/fabric-samples/commit/b5d5026) [FAB-14633](https://jira.hyperledger.org/browse/FAB-14633) Remove apt-get from eyfn.sh
* [efaadd3](https://github.com/hyperledger/fabric-samples/commit/efaadd3) [FAB-14531](https://jira.hyperledger.org/browse/FAB-14531) BYFN Raft with 5 nodes
* [d63047c](https://github.com/hyperledger/fabric-samples/commit/d63047c) [FAB-14444](https://jira.hyperledger.org/browse/FAB-14444)
* [7e3d428](https://github.com/hyperledger/fabric-samples/commit/7e3d428) [FAB-14369](https://jira.hyperledger.org/browse/FAB-14369)Fix dev mode failing to build Chaincode
* [420ba23](https://github.com/hyperledger/fabric-samples/commit/420ba23) [FAB-12762](https://jira.hyperledger.org/browse/FAB-12762) Add etcd/raft consensus option to BYFN
* [2b68c80](https://github.com/hyperledger/fabric-samples/commit/2b68c80) [FAB-14317](https://jira.hyperledger.org/browse/FAB-14317) Add default policies to org3
* [f942010](https://github.com/hyperledger/fabric-samples/commit/f942010) [FAB-14268](https://jira.hyperledger.org/browse/FAB-14268) Make BYFN/EYFN ports match external ports
* [4e2ce23](https://github.com/hyperledger/fabric-samples/commit/4e2ce23) [FAB-14271](https://jira.hyperledger.org/browse/FAB-14271) Add channel policies to channel config
* [f26477c](https://github.com/hyperledger/fabric-samples/commit/f26477c) [FAB-11796](https://jira.hyperledger.org/browse/FAB-11796)high-throughput:Remove unnecessary prunesafe
* [137327a](https://github.com/hyperledger/fabric-samples/commit/137327a) [FAB-14162](https://jira.hyperledger.org/browse/FAB-14162) Pin fabric-samples in master to "unstable"
* [6007c09](https://github.com/hyperledger/fabric-samples/commit/6007c09) [FAB-13862](https://jira.hyperledger.org/browse/FAB-13862) Rename example02 ABstore
* [94cb603](https://github.com/hyperledger/fabric-samples/commit/94cb603) [FAB-13933](https://jira.hyperledger.org/browse/FAB-13933) Fix misspellings
* [a8a5539](https://github.com/hyperledger/fabric-samples/commit/a8a5539) Fix doc link Fix variable error
* [b0cda61](https://github.com/hyperledger/fabric-samples/commit/b0cda61) [FAB-13769](https://jira.hyperledger.org/browse/FAB-13769) Add UT code to ABAC sample Chaincode
* [c7438e1](https://github.com/hyperledger/fabric-samples/commit/c7438e1) [FAB-13668](https://jira.hyperledger.org/browse/FAB-13668) BYFN's container volume mapping is bad
* [e48b2de](https://github.com/hyperledger/fabric-samples/commit/e48b2de) [FAB-13489](https://jira.hyperledger.org/browse/FAB-13489) fabric-samples add error msg
* [6269941](https://github.com/hyperledger/fabric-samples/commit/6269941) Correct broken link

## "v1.4.1"

* [11e4c23](https://github.com/hyperledger/fabric-samples/commit/11e4c23) Update samples to use v2.0 or later dependencies (#161)
* [94beab7](https://github.com/hyperledger/fabric-samples/commit/94beab7) FABN-1531 Use v2.1.0 sdk-node modules
* [8820d2f](https://github.com/hyperledger/fabric-samples/commit/8820d2f) Fix commercial-paper README
* [aa9b577](https://github.com/hyperledger/fabric-samples/commit/aa9b577) Remove TLS enabled switch (#155)
* [381fb46](https://github.com/hyperledger/fabric-samples/commit/381fb46) upgraded abstore golang chaincode to use contract-api package (#154)
* [5e5d2c8](https://github.com/hyperledger/fabric-samples/commit/5e5d2c8) Update java chaincode to be compatible with doc and other implementations (#149)
* [c572c51](https://github.com/hyperledger/fabric-samples/commit/c572c51) Organize and Standardize `ci` Directory Content (#152)
* [aa40963](https://github.com/hyperledger/fabric-samples/commit/aa40963) Perform General Cleanup (#151)
* [da41afa](https://github.com/hyperledger/fabric-samples/commit/da41afa) Remove left over rm -rf command from BYFN (#148)
* [4bb48a9](https://github.com/hyperledger/fabric-samples/commit/4bb48a9) Jenkins no longer used (#145)
* [6f984e1](https://github.com/hyperledger/fabric-samples/commit/6f984e1) Bump acorn from 6.4.0 to 6.4.1 in /fabcar/javascript (#144)
* [b155620](https://github.com/hyperledger/fabric-samples/commit/b155620) Remove redundant invoke command from test network (#142)
* [851933b](https://github.com/hyperledger/fabric-samples/commit/851933b) Add enrollUser files to commercial paper (#140)
* [87600bd](https://github.com/hyperledger/fabric-samples/commit/87600bd) [FAB-17268](https://jira.hyperledger.org/browse/FAB-17268) Move fabcar sample to test network (#103)
* [9397788](https://github.com/hyperledger/fabric-samples/commit/9397788) Wrong groupId on hyperledger fabric dependencies for java-application (#134)
* [92555fb](https://github.com/hyperledger/fabric-samples/commit/92555fb) Update README.md (#133)
* [59c6641](https://github.com/hyperledger/fabric-samples/commit/59c6641) Change Download Location of Fabric Binaries (#143)
* [1f283fc](https://github.com/hyperledger/fabric-samples/commit/1f283fc) init function does not exist on fabcar (#141)
* [defb6bb](https://github.com/hyperledger/fabric-samples/commit/defb6bb) [FAB-17656](https://jira.hyperledger.org/browse/FAB-17656) echo Generating channel.tx (#139)
* [4c7bab0](https://github.com/hyperledger/fabric-samples/commit/4c7bab0) fix: package seletor REGEX (#135)
* [db69c6f](https://github.com/hyperledger/fabric-samples/commit/db69c6f) Add fabcar external service sample (#136)
* [7f5f5e6](https://github.com/hyperledger/fabric-samples/commit/7f5f5e6) [FAB-17504](https://jira.hyperledger.org/browse/FAB-17504) add Organizations.<Org>.OrdererEndpoints and remove Orderer.Addresses (#125)
* [f3fc08d](https://github.com/hyperledger/fabric-samples/commit/f3fc08d) Remove solo and kafka from test net configtx.yaml (#137)
* [e17574d](https://github.com/hyperledger/fabric-samples/commit/e17574d) Add CA's to docker test network (#124)
* [faac18e](https://github.com/hyperledger/fabric-samples/commit/faac18e) [FAB-17461](https://jira.hyperledger.org/browse/FAB-17461) Move off_chain_data sample to test network (#122)
* [121a44a](https://github.com/hyperledger/fabric-samples/commit/121a44a) [FAB-17460](https://jira.hyperledger.org/browse/FAB-17460) Move High Throughput sample to test network (#112)
* [a2f3a66](https://github.com/hyperledger/fabric-samples/commit/a2f3a66) Update docker image version
* [e5b898c](https://github.com/hyperledger/fabric-samples/commit/e5b898c) Revert "first-network/scripts/*: Make Chaincode name configurable (#118)" (#131)
* [9ef61e2](https://github.com/hyperledger/fabric-samples/commit/9ef61e2) first-network/scripts/*: Make Chaincode name configurable (#118)
* [e204ebb](https://github.com/hyperledger/fabric-samples/commit/e204ebb) Remove reference to 2.0 beta (#111)
* [3dbe116](https://github.com/hyperledger/fabric-samples/commit/3dbe116) [FAB-17456](https://jira.hyperledger.org/browse/FAB-17456) fabric-samples read ccp (#117)
* [965ed1f](https://github.com/hyperledger/fabric-samples/commit/965ed1f) [FAB-17498](https://jira.hyperledger.org/browse/FAB-17498) Beta Images removal, test test-network (#121)
* [403019e](https://github.com/hyperledger/fabric-samples/commit/403019e) [FAB-17495](https://jira.hyperledger.org/browse/FAB-17495) Remove Basic Network sample (#120)
* [883ef99](https://github.com/hyperledger/fabric-samples/commit/883ef99) [FAB-17457](https://jira.hyperledger.org/browse/FAB-17457) Script correction (#119)
* [b89ee34](https://github.com/hyperledger/fabric-samples/commit/b89ee34) Update Commercial Paper to v2.0 Lifecycle (#109)
* [4208644](https://github.com/hyperledger/fabric-samples/commit/4208644) [FAB-17478](https://jira.hyperledger.org/browse/FAB-17478) Update commercial paper to use go api v1.0.0 (#115)
* [0df5ed9](https://github.com/hyperledger/fabric-samples/commit/0df5ed9) [FAB-17477](https://jira.hyperledger.org/browse/FAB-17477) Update fabcar to use go api v1.0.0 (#116)
* [571733f](https://github.com/hyperledger/fabric-samples/commit/571733f) [FAB-17447](https://jira.hyperledger.org/browse/FAB-17447) Update to 2.0.0 Libraries
* [67b4ee7](https://github.com/hyperledger/fabric-samples/commit/67b4ee7) Add Org3 bugs in test network (#108)
* [5b93dd0](https://github.com/hyperledger/fabric-samples/commit/5b93dd0) [FAB-17140](https://jira.hyperledger.org/browse/FAB-17140) Add go commercial paper contract (#102)
* [4fe6a25](https://github.com/hyperledger/fabric-samples/commit/4fe6a25) [FABCI-482] Update Nexus URL's to Artifactory (#92)
* [1488fbb](https://github.com/hyperledger/fabric-samples/commit/1488fbb) Add 1.x versions of fabric to blacklisted versions
* [8ca279d](https://github.com/hyperledger/fabric-samples/commit/8ca279d) Add Support for Versioning NodeJS (#106)
* [b3b5267](https://github.com/hyperledger/fabric-samples/commit/b3b5267) [FAB-17243](https://jira.hyperledger.org/browse/FAB-17243) Add support for Fabric CA for Org3 on the (#91)
* [ce41ff7](https://github.com/hyperledger/fabric-samples/commit/ce41ff7) Remove references to vendoring chaincode from your gopath (#96)
* [4235d30](https://github.com/hyperledger/fabric-samples/commit/4235d30) [FAB-17306](https://jira.hyperledger.org/browse/FAB-17306) Fix artifact names in test-network (#97)
* [4c2a0a4](https://github.com/hyperledger/fabric-samples/commit/4c2a0a4) [FAB-16147](https://jira.hyperledger.org/browse/FAB-16147) Update Commercial Paper to work with v2 (#98)
* [6d9fd6f](https://github.com/hyperledger/fabric-samples/commit/6d9fd6f) Remove Gerrit reference
* [a026a4f](https://github.com/hyperledger/fabric-samples/commit/a026a4f) Fixed typo (#90)
* [cdb0e8b](https://github.com/hyperledger/fabric-samples/commit/cdb0e8b) TYPO (#89)
* [94ac8b6](https://github.com/hyperledger/fabric-samples/commit/94ac8b6) Update to use beta levels of modules (#88)
* [d848633](https://github.com/hyperledger/fabric-samples/commit/d848633) [FAB-16844](https://jira.hyperledger.org/browse/FAB-16844) Correct BYFN CC name
* [73267e1](https://github.com/hyperledger/fabric-samples/commit/73267e1) Fix test network bugs for adding org3
* [5d58254](https://github.com/hyperledger/fabric-samples/commit/5d58254) [FAB-17145](https://jira.hyperledger.org/browse/FAB-17145) Add test network to Fabric Samples
* [e9f2957](https://github.com/hyperledger/fabric-samples/commit/e9f2957) [FAB-17062](https://jira.hyperledger.org/browse/FAB-17062) Fix typos in Commercial Paper readme
* [36694d0](https://github.com/hyperledger/fabric-samples/commit/36694d0) [FAB-17121](https://jira.hyperledger.org/browse/FAB-17121) Use new bootstrap config in orderer
* [429f087](https://github.com/hyperledger/fabric-samples/commit/429f087) update fabcar go to new programming model
* [1467086](https://github.com/hyperledger/fabric-samples/commit/1467086) Bump eslint-utils
* [33f349a](https://github.com/hyperledger/fabric-samples/commit/33f349a) Remove Stalebot
* [6af43bf](https://github.com/hyperledger/fabric-samples/commit/6af43bf) Change stalebot settings
* [4880401](https://github.com/hyperledger/fabric-samples/commit/4880401) [FAB-16284](https://jira.hyperledger.org/browse/FAB-16284) Remove E2E file and -f option from BYFN
* [fe96f60](https://github.com/hyperledger/fabric-samples/commit/fe96f60) [FAB-16850](https://jira.hyperledger.org/browse/FAB-16850) Set up CI with Azure Pipelines
* [81aabf4](https://github.com/hyperledger/fabric-samples/commit/81aabf4) [FAB-16849](https://jira.hyperledger.org/browse/FAB-16849) Various updates for Java version of FabCar
* [a42b858](https://github.com/hyperledger/fabric-samples/commit/a42b858) Update FabCar to reflect wallet API changes
* [890f9ea](https://github.com/hyperledger/fabric-samples/commit/890f9ea) [FAB-16713](https://jira.hyperledger.org/browse/FAB-16713) Fix npm audit warnings
* [e48e804](https://github.com/hyperledger/fabric-samples/commit/e48e804) [FAB-16776](https://jira.hyperledger.org/browse/FAB-16776) Move BYFN up to V2_0 capabilities
* [7b65a25](https://github.com/hyperledger/fabric-samples/commit/7b65a25) [IN-68] Add default GitHub SECURITY policy
* [408e0e8](https://github.com/hyperledger/fabric-samples/commit/408e0e8) [FAB-16619](https://jira.hyperledger.org/browse/FAB-16619) Fix the policy warning
* [670d446](https://github.com/hyperledger/fabric-samples/commit/670d446) [FAB-16668](https://jira.hyperledger.org/browse/FAB-16668) fabcar chaincode modify console output
* [f2939e2](https://github.com/hyperledger/fabric-samples/commit/f2939e2) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for Commercial Paper sample
* [3d19014](https://github.com/hyperledger/fabric-samples/commit/3d19014) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for FabCar sample
* [e2b7cb7](https://github.com/hyperledger/fabric-samples/commit/e2b7cb7) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for abstore sample
* [db48612](https://github.com/hyperledger/fabric-samples/commit/db48612) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Increase chaincode execute timeout
* [521a7ff](https://github.com/hyperledger/fabric-samples/commit/521a7ff) [FAB-16607](https://jira.hyperledger.org/browse/FAB-16607) Update FabCar to reflect CC updates
* [c13a5ec](https://github.com/hyperledger/fabric-samples/commit/c13a5ec) [FAB-16528](https://jira.hyperledger.org/browse/FAB-16528) marbles private chaincode sync up
* [3fad853](https://github.com/hyperledger/fabric-samples/commit/3fad853) [FAB-16528](https://jira.hyperledger.org/browse/FAB-16528) marbles private chaincode sync up
* [8b9b82f](https://github.com/hyperledger/fabric-samples/commit/8b9b82f) [FAB-16489](https://jira.hyperledger.org/browse/FAB-16489) Add CODEOWNERS
* [a6ce915](https://github.com/hyperledger/fabric-samples/commit/a6ce915) [FAB-16487](https://jira.hyperledger.org/browse/FAB-16487) Update eslint
* [48082cf](https://github.com/hyperledger/fabric-samples/commit/48082cf) [FAB-16362](https://jira.hyperledger.org/browse/FAB-16362) adding chaincode excution comments
* [1d379f3](https://github.com/hyperledger/fabric-samples/commit/1d379f3) [FAB-16474](https://jira.hyperledger.org/browse/FAB-16474) marbles02 chaincode error
* [18712ca](https://github.com/hyperledger/fabric-samples/commit/18712ca) [FAB-16133](https://jira.hyperledger.org/browse/FAB-16133) Remove Solo consensus from BYFN
* [91c720a](https://github.com/hyperledger/fabric-samples/commit/91c720a) [FAB-16390](https://jira.hyperledger.org/browse/FAB-16390) Added filter for invalid transactions
* [1d3e267](https://github.com/hyperledger/fabric-samples/commit/1d3e267) Redirect samples to fabric-{chaincode,protos}-go
* [398a5b1](https://github.com/hyperledger/fabric-samples/commit/398a5b1) [FABCI-394] Remove AnsiColor Wrapper
* [ce154e0](https://github.com/hyperledger/fabric-samples/commit/ce154e0) [FAB-16310](https://jira.hyperledger.org/browse/FAB-16310) Vendor Go dependencies in all samples
* [6ea7c71](https://github.com/hyperledger/fabric-samples/commit/6ea7c71) [FAB-16285](https://jira.hyperledger.org/browse/FAB-16285) Update blacklisted versions in BYFN
* [86cd831](https://github.com/hyperledger/fabric-samples/commit/86cd831) [FAB-16284](https://jira.hyperledger.org/browse/FAB-16284) Remove E2E file and -f option from BYFN
* [0063abe](https://github.com/hyperledger/fabric-samples/commit/0063abe) Update stale script name in interest rate swaps
* [3907507](https://github.com/hyperledger/fabric-samples/commit/3907507) [FAB-16277](https://jira.hyperledger.org/browse/FAB-16277) Update BYFN w/ Raft ports in Docker network
* [33b0065](https://github.com/hyperledger/fabric-samples/commit/33b0065) [FAB-14813](https://jira.hyperledger.org/browse/FAB-14813) Channel event sample in fabric-samples
* [b62d5bd](https://github.com/hyperledger/fabric-samples/commit/b62d5bd) [FAB-16132](https://jira.hyperledger.org/browse/FAB-16132) Remove Kafka consensus from BYFN
* [9b14525](https://github.com/hyperledger/fabric-samples/commit/9b14525) [FAB-15213](https://jira.hyperledger.org/browse/FAB-15213) Update Commercial Paper for Java
* [4158877](https://github.com/hyperledger/fabric-samples/commit/4158877) [FAB-16232](https://jira.hyperledger.org/browse/FAB-16232) Remove FabToken sample
* [b6380cc](https://github.com/hyperledger/fabric-samples/commit/b6380cc) [FAB-16198](https://jira.hyperledger.org/browse/FAB-16198) Run "go mod vendor" for FabCar Go contract
* [639848a](https://github.com/hyperledger/fabric-samples/commit/639848a) [FAB-16197](https://jira.hyperledger.org/browse/FAB-16197) Add connection profiles to .gitignore
* [3996db5](https://github.com/hyperledger/fabric-samples/commit/3996db5) [FAB-12219](https://jira.hyperledger.org/browse/FAB-12219) abstore node -> javascript
* [14ac271](https://github.com/hyperledger/fabric-samples/commit/14ac271) [FAB-12219](https://jira.hyperledger.org/browse/FAB-12219) marbles02 node -> javascript
* [13f16e5](https://github.com/hyperledger/fabric-samples/commit/13f16e5) [FGJ-4] CI tests for FabCar Java sample
* [171a7d2](https://github.com/hyperledger/fabric-samples/commit/171a7d2) FGJ-4 Fabcar sample
* [868f9d0](https://github.com/hyperledger/fabric-samples/commit/868f9d0) [FAB-15625](https://jira.hyperledger.org/browse/FAB-15625) Add UT for Simple Asset Chaincode
* [597d150](https://github.com/hyperledger/fabric-samples/commit/597d150) [FAB-14532](https://jira.hyperledger.org/browse/FAB-14532) Remove LL FabCar sample
* [583ff8f](https://github.com/hyperledger/fabric-samples/commit/583ff8f) Use renamed CheckCommitReadiness function
* [750f937](https://github.com/hyperledger/fabric-samples/commit/750f937) [FAB-15213](https://jira.hyperledger.org/browse/FAB-15213) Add Java FabCar sample contract
* [abbda95](https://github.com/hyperledger/fabric-samples/commit/abbda95) [FAB-15897](https://jira.hyperledger.org/browse/FAB-15897) Improve FabCar test logging
* [dd8150a](https://github.com/hyperledger/fabric-samples/commit/dd8150a) [FAB-15104](https://jira.hyperledger.org/browse/FAB-15104) Remove versions from fabric-samples readme
* [1387aa8](https://github.com/hyperledger/fabric-samples/commit/1387aa8) [FAB-15927](https://jira.hyperledger.org/browse/FAB-15927)  Better expression for golang
* [61c33d3](https://github.com/hyperledger/fabric-samples/commit/61c33d3) [FAB-15973](https://jira.hyperledger.org/browse/FAB-15973) use --output json on simulatecommit
* [8bbdd0f](https://github.com/hyperledger/fabric-samples/commit/8bbdd0f) [FAB-15716](https://jira.hyperledger.org/browse/FAB-15716) Fix instructions for dev-mode
* [0254d67](https://github.com/hyperledger/fabric-samples/commit/0254d67) QueryApprovalStatus -> SimulateCommitChaincodeDef
* [c57d67c](https://github.com/hyperledger/fabric-samples/commit/c57d67c) [FAB-15782](https://jira.hyperledger.org/browse/FAB-15782) Sample Go CC should include deps
* [6ba5a19](https://github.com/hyperledger/fabric-samples/commit/6ba5a19) Update to Go 1.12.5 in ci.properties
* [1774a25](https://github.com/hyperledger/fabric-samples/commit/1774a25) [FAB-15723](https://jira.hyperledger.org/browse/FAB-15723) Fix script and instruction with ccenv
* [6ae711c](https://github.com/hyperledger/fabric-samples/commit/6ae711c) [FAB-15717](https://jira.hyperledger.org/browse/FAB-15717) fix Error Unexpected end of JSON input
* [5be56d3](https://github.com/hyperledger/fabric-samples/commit/5be56d3) [FAB-15104](https://jira.hyperledger.org/browse/FAB-15104) Remove scripts/bootstrap.sh
* [779f8f3](https://github.com/hyperledger/fabric-samples/commit/779f8f3) [FAB-15649](https://jira.hyperledger.org/browse/FAB-15649)Fix Fabcar to install Chaincode on all peers
* [7c5f5d3](https://github.com/hyperledger/fabric-samples/commit/7c5f5d3) [FAB-15199](https://jira.hyperledger.org/browse/FAB-15199) Update interest rate sample
* [f0dca20](https://github.com/hyperledger/fabric-samples/commit/f0dca20) [FAB-14532](https://jira.hyperledger.org/browse/FAB-14532) Remove LL FabCar sample
* [1ed1a10](https://github.com/hyperledger/fabric-samples/commit/1ed1a10) [FAB-15573](https://jira.hyperledger.org/browse/FAB-15573) Fix typo in fabric-samples-ci.md
* [2e7fec9](https://github.com/hyperledger/fabric-samples/commit/2e7fec9) [FAB-15601](https://jira.hyperledger.org/browse/FAB-15601) BYFN: Fix MAX_RETRY for couchdb
* [1e9e4c4](https://github.com/hyperledger/fabric-samples/commit/1e9e4c4) [FAB-9329](https://jira.hyperledger.org/browse/FAB-9329) Remove the unused variable in BYFN/EYFN
* [964c09f](https://github.com/hyperledger/fabric-samples/commit/964c09f) [FAB-15601](https://jira.hyperledger.org/browse/FAB-15601) BYFN: Fix MAX_RETRY for couchdb
* [41dca99](https://github.com/hyperledger/fabric-samples/commit/41dca99) [FAB-15127](https://jira.hyperledger.org/browse/FAB-15127) Update high throughput sample
* [3fe014a](https://github.com/hyperledger/fabric-samples/commit/3fe014a) Use official CouchDB image
* [f2d0fa0](https://github.com/hyperledger/fabric-samples/commit/f2d0fa0) [FAB-14487](https://jira.hyperledger.org/browse/FAB-14487) Make FabCar use BYFN, not basic-network
* [e9c3649](https://github.com/hyperledger/fabric-samples/commit/e9c3649) [FAB-15276](https://jira.hyperledger.org/browse/FAB-15276) Fix license statements
* [fbe4036](https://github.com/hyperledger/fabric-samples/commit/fbe4036) [FAB-14486](https://jira.hyperledger.org/browse/FAB-14486) Extend BYFN to opt skip chaincode deploy
* [0c4141f](https://github.com/hyperledger/fabric-samples/commit/0c4141f) [FAB-14485](https://jira.hyperledger.org/browse/FAB-14485) Extend BYFN to opt inc cert authorities
* [529b83b](https://github.com/hyperledger/fabric-samples/commit/529b83b) [FAB-14330](https://jira.hyperledger.org/browse/FAB-14330) Add connection profiles for BYFN and EYFN
* [2c21c83](https://github.com/hyperledger/fabric-samples/commit/2c21c83) [FABN-1184] Update fabtoken/README.md
* [5056a23](https://github.com/hyperledger/fabric-samples/commit/5056a23) [FABN-1184] Add CI script for fabtoken sample app
* [5d6db95](https://github.com/hyperledger/fabric-samples/commit/5d6db95) Update maintainers for fabric-samples
* [f527815](https://github.com/hyperledger/fabric-samples/commit/f527815) [FAB-15119](https://jira.hyperledger.org/browse/FAB-15119) Fix BYFN with Java chaincode
* [8245252](https://github.com/hyperledger/fabric-samples/commit/8245252) [FABN-1184] Implement fabtoken sample app
* [1bd1c2f](https://github.com/hyperledger/fabric-samples/commit/1bd1c2f) FABCI-284 Update CI Pipeline script
* [c24abf9](https://github.com/hyperledger/fabric-samples/commit/c24abf9) [FAB-15022](https://jira.hyperledger.org/browse/FAB-15022) Basic-network support for new lifecycle
* [b64fd45](https://github.com/hyperledger/fabric-samples/commit/b64fd45) [FAB-15051](https://jira.hyperledger.org/browse/FAB-15051) delStandard() function for high-throughput
* [3e68a7e](https://github.com/hyperledger/fabric-samples/commit/3e68a7e) [FAB-14784](https://jira.hyperledger.org/browse/FAB-14784) Remove balance-transfer
* [eb3fe08](https://github.com/hyperledger/fabric-samples/commit/eb3fe08) [FAB-14779](https://jira.hyperledger.org/browse/FAB-14779) QueryApprovalStatus step in byfn
* [2777429](https://github.com/hyperledger/fabric-samples/commit/2777429) [FAB-14711](https://jira.hyperledger.org/browse/FAB-14711) update byfn with new lifecycle
* [aec3389](https://github.com/hyperledger/fabric-samples/commit/aec3389) [FAB-12215](https://jira.hyperledger.org/browse/FAB-12215)WYFA:Remove chainId in tx proposal request
* [b5d5026](https://github.com/hyperledger/fabric-samples/commit/b5d5026) [FAB-14633](https://jira.hyperledger.org/browse/FAB-14633) Remove apt-get from eyfn.sh
* [efaadd3](https://github.com/hyperledger/fabric-samples/commit/efaadd3) [FAB-14531](https://jira.hyperledger.org/browse/FAB-14531) BYFN Raft with 5 nodes
* [d63047c](https://github.com/hyperledger/fabric-samples/commit/d63047c) [FAB-14444](https://jira.hyperledger.org/browse/FAB-14444)
* [7e3d428](https://github.com/hyperledger/fabric-samples/commit/7e3d428) [FAB-14369](https://jira.hyperledger.org/browse/FAB-14369)Fix dev mode failing to build Chaincode
* [420ba23](https://github.com/hyperledger/fabric-samples/commit/420ba23) [FAB-12762](https://jira.hyperledger.org/browse/FAB-12762) Add etcd/raft consensus option to BYFN
* [2b68c80](https://github.com/hyperledger/fabric-samples/commit/2b68c80) [FAB-14317](https://jira.hyperledger.org/browse/FAB-14317) Add default policies to org3
* [f942010](https://github.com/hyperledger/fabric-samples/commit/f942010) [FAB-14268](https://jira.hyperledger.org/browse/FAB-14268) Make BYFN/EYFN ports match external ports
* [4e2ce23](https://github.com/hyperledger/fabric-samples/commit/4e2ce23) [FAB-14271](https://jira.hyperledger.org/browse/FAB-14271) Add channel policies to channel config
* [f26477c](https://github.com/hyperledger/fabric-samples/commit/f26477c) [FAB-11796](https://jira.hyperledger.org/browse/FAB-11796)high-throughput:Remove unnecessary prunesafe
* [137327a](https://github.com/hyperledger/fabric-samples/commit/137327a) [FAB-14162](https://jira.hyperledger.org/browse/FAB-14162) Pin fabric-samples in master to "unstable"
* [6007c09](https://github.com/hyperledger/fabric-samples/commit/6007c09) [FAB-13862](https://jira.hyperledger.org/browse/FAB-13862) Rename example02 ABstore
* [94cb603](https://github.com/hyperledger/fabric-samples/commit/94cb603) [FAB-13933](https://jira.hyperledger.org/browse/FAB-13933) Fix misspellings
* [a8a5539](https://github.com/hyperledger/fabric-samples/commit/a8a5539) Fix doc link Fix variable error
* [b0cda61](https://github.com/hyperledger/fabric-samples/commit/b0cda61) [FAB-13769](https://jira.hyperledger.org/browse/FAB-13769) Add UT code to ABAC sample Chaincode
* [c7438e1](https://github.com/hyperledger/fabric-samples/commit/c7438e1) [FAB-13668](https://jira.hyperledger.org/browse/FAB-13668) BYFN's container volume mapping is bad
* [e48b2de](https://github.com/hyperledger/fabric-samples/commit/e48b2de) [FAB-13489](https://jira.hyperledger.org/browse/FAB-13489) fabric-samples add error msg
* [6269941](https://github.com/hyperledger/fabric-samples/commit/6269941) Correct broken link

## "v1.4.0"

* [11e4c23](https://github.com/hyperledger/fabric-samples/commit/11e4c23) Update samples to use v2.0 or later dependencies (#161)
* [94beab7](https://github.com/hyperledger/fabric-samples/commit/94beab7) FABN-1531 Use v2.1.0 sdk-node modules
* [8820d2f](https://github.com/hyperledger/fabric-samples/commit/8820d2f) Fix commercial-paper README
* [aa9b577](https://github.com/hyperledger/fabric-samples/commit/aa9b577) Remove TLS enabled switch (#155)
* [381fb46](https://github.com/hyperledger/fabric-samples/commit/381fb46) upgraded abstore golang chaincode to use contract-api package (#154)
* [5e5d2c8](https://github.com/hyperledger/fabric-samples/commit/5e5d2c8) Update java chaincode to be compatible with doc and other implementations (#149)
* [c572c51](https://github.com/hyperledger/fabric-samples/commit/c572c51) Organize and Standardize `ci` Directory Content (#152)
* [aa40963](https://github.com/hyperledger/fabric-samples/commit/aa40963) Perform General Cleanup (#151)
* [da41afa](https://github.com/hyperledger/fabric-samples/commit/da41afa) Remove left over rm -rf command from BYFN (#148)
* [4bb48a9](https://github.com/hyperledger/fabric-samples/commit/4bb48a9) Jenkins no longer used (#145)
* [6f984e1](https://github.com/hyperledger/fabric-samples/commit/6f984e1) Bump acorn from 6.4.0 to 6.4.1 in /fabcar/javascript (#144)
* [b155620](https://github.com/hyperledger/fabric-samples/commit/b155620) Remove redundant invoke command from test network (#142)
* [851933b](https://github.com/hyperledger/fabric-samples/commit/851933b) Add enrollUser files to commercial paper (#140)
* [87600bd](https://github.com/hyperledger/fabric-samples/commit/87600bd) [FAB-17268](https://jira.hyperledger.org/browse/FAB-17268) Move fabcar sample to test network (#103)
* [9397788](https://github.com/hyperledger/fabric-samples/commit/9397788) Wrong groupId on hyperledger fabric dependencies for java-application (#134)
* [92555fb](https://github.com/hyperledger/fabric-samples/commit/92555fb) Update README.md (#133)
* [59c6641](https://github.com/hyperledger/fabric-samples/commit/59c6641) Change Download Location of Fabric Binaries (#143)
* [1f283fc](https://github.com/hyperledger/fabric-samples/commit/1f283fc) init function does not exist on fabcar (#141)
* [defb6bb](https://github.com/hyperledger/fabric-samples/commit/defb6bb) [FAB-17656](https://jira.hyperledger.org/browse/FAB-17656) echo Generating channel.tx (#139)
* [4c7bab0](https://github.com/hyperledger/fabric-samples/commit/4c7bab0) fix: package seletor REGEX (#135)
* [db69c6f](https://github.com/hyperledger/fabric-samples/commit/db69c6f) Add fabcar external service sample (#136)
* [7f5f5e6](https://github.com/hyperledger/fabric-samples/commit/7f5f5e6) [FAB-17504](https://jira.hyperledger.org/browse/FAB-17504) add Organizations.<Org>.OrdererEndpoints and remove Orderer.Addresses (#125)
* [f3fc08d](https://github.com/hyperledger/fabric-samples/commit/f3fc08d) Remove solo and kafka from test net configtx.yaml (#137)
* [e17574d](https://github.com/hyperledger/fabric-samples/commit/e17574d) Add CA's to docker test network (#124)
* [faac18e](https://github.com/hyperledger/fabric-samples/commit/faac18e) [FAB-17461](https://jira.hyperledger.org/browse/FAB-17461) Move off_chain_data sample to test network (#122)
* [121a44a](https://github.com/hyperledger/fabric-samples/commit/121a44a) [FAB-17460](https://jira.hyperledger.org/browse/FAB-17460) Move High Throughput sample to test network (#112)
* [a2f3a66](https://github.com/hyperledger/fabric-samples/commit/a2f3a66) Update docker image version
* [e5b898c](https://github.com/hyperledger/fabric-samples/commit/e5b898c) Revert "first-network/scripts/*: Make Chaincode name configurable (#118)" (#131)
* [9ef61e2](https://github.com/hyperledger/fabric-samples/commit/9ef61e2) first-network/scripts/*: Make Chaincode name configurable (#118)
* [e204ebb](https://github.com/hyperledger/fabric-samples/commit/e204ebb) Remove reference to 2.0 beta (#111)
* [3dbe116](https://github.com/hyperledger/fabric-samples/commit/3dbe116) [FAB-17456](https://jira.hyperledger.org/browse/FAB-17456) fabric-samples read ccp (#117)
* [965ed1f](https://github.com/hyperledger/fabric-samples/commit/965ed1f) [FAB-17498](https://jira.hyperledger.org/browse/FAB-17498) Beta Images removal, test test-network (#121)
* [403019e](https://github.com/hyperledger/fabric-samples/commit/403019e) [FAB-17495](https://jira.hyperledger.org/browse/FAB-17495) Remove Basic Network sample (#120)
* [883ef99](https://github.com/hyperledger/fabric-samples/commit/883ef99) [FAB-17457](https://jira.hyperledger.org/browse/FAB-17457) Script correction (#119)
* [b89ee34](https://github.com/hyperledger/fabric-samples/commit/b89ee34) Update Commercial Paper to v2.0 Lifecycle (#109)
* [4208644](https://github.com/hyperledger/fabric-samples/commit/4208644) [FAB-17478](https://jira.hyperledger.org/browse/FAB-17478) Update commercial paper to use go api v1.0.0 (#115)
* [0df5ed9](https://github.com/hyperledger/fabric-samples/commit/0df5ed9) [FAB-17477](https://jira.hyperledger.org/browse/FAB-17477) Update fabcar to use go api v1.0.0 (#116)
* [571733f](https://github.com/hyperledger/fabric-samples/commit/571733f) [FAB-17447](https://jira.hyperledger.org/browse/FAB-17447) Update to 2.0.0 Libraries
* [67b4ee7](https://github.com/hyperledger/fabric-samples/commit/67b4ee7) Add Org3 bugs in test network (#108)
* [5b93dd0](https://github.com/hyperledger/fabric-samples/commit/5b93dd0) [FAB-17140](https://jira.hyperledger.org/browse/FAB-17140) Add go commercial paper contract (#102)
* [4fe6a25](https://github.com/hyperledger/fabric-samples/commit/4fe6a25) [FABCI-482] Update Nexus URL's to Artifactory (#92)
* [1488fbb](https://github.com/hyperledger/fabric-samples/commit/1488fbb) Add 1.x versions of fabric to blacklisted versions
* [8ca279d](https://github.com/hyperledger/fabric-samples/commit/8ca279d) Add Support for Versioning NodeJS (#106)
* [b3b5267](https://github.com/hyperledger/fabric-samples/commit/b3b5267) [FAB-17243](https://jira.hyperledger.org/browse/FAB-17243) Add support for Fabric CA for Org3 on the (#91)
* [ce41ff7](https://github.com/hyperledger/fabric-samples/commit/ce41ff7) Remove references to vendoring chaincode from your gopath (#96)
* [4235d30](https://github.com/hyperledger/fabric-samples/commit/4235d30) [FAB-17306](https://jira.hyperledger.org/browse/FAB-17306) Fix artifact names in test-network (#97)
* [4c2a0a4](https://github.com/hyperledger/fabric-samples/commit/4c2a0a4) [FAB-16147](https://jira.hyperledger.org/browse/FAB-16147) Update Commercial Paper to work with v2 (#98)
* [6d9fd6f](https://github.com/hyperledger/fabric-samples/commit/6d9fd6f) Remove Gerrit reference
* [a026a4f](https://github.com/hyperledger/fabric-samples/commit/a026a4f) Fixed typo (#90)
* [cdb0e8b](https://github.com/hyperledger/fabric-samples/commit/cdb0e8b) TYPO (#89)
* [94ac8b6](https://github.com/hyperledger/fabric-samples/commit/94ac8b6) Update to use beta levels of modules (#88)
* [d848633](https://github.com/hyperledger/fabric-samples/commit/d848633) [FAB-16844](https://jira.hyperledger.org/browse/FAB-16844) Correct BYFN CC name
* [73267e1](https://github.com/hyperledger/fabric-samples/commit/73267e1) Fix test network bugs for adding org3
* [5d58254](https://github.com/hyperledger/fabric-samples/commit/5d58254) [FAB-17145](https://jira.hyperledger.org/browse/FAB-17145) Add test network to Fabric Samples
* [e9f2957](https://github.com/hyperledger/fabric-samples/commit/e9f2957) [FAB-17062](https://jira.hyperledger.org/browse/FAB-17062) Fix typos in Commercial Paper readme
* [36694d0](https://github.com/hyperledger/fabric-samples/commit/36694d0) [FAB-17121](https://jira.hyperledger.org/browse/FAB-17121) Use new bootstrap config in orderer
* [429f087](https://github.com/hyperledger/fabric-samples/commit/429f087) update fabcar go to new programming model
* [1467086](https://github.com/hyperledger/fabric-samples/commit/1467086) Bump eslint-utils
* [33f349a](https://github.com/hyperledger/fabric-samples/commit/33f349a) Remove Stalebot
* [6af43bf](https://github.com/hyperledger/fabric-samples/commit/6af43bf) Change stalebot settings
* [4880401](https://github.com/hyperledger/fabric-samples/commit/4880401) [FAB-16284](https://jira.hyperledger.org/browse/FAB-16284) Remove E2E file and -f option from BYFN
* [fe96f60](https://github.com/hyperledger/fabric-samples/commit/fe96f60) [FAB-16850](https://jira.hyperledger.org/browse/FAB-16850) Set up CI with Azure Pipelines
* [81aabf4](https://github.com/hyperledger/fabric-samples/commit/81aabf4) [FAB-16849](https://jira.hyperledger.org/browse/FAB-16849) Various updates for Java version of FabCar
* [a42b858](https://github.com/hyperledger/fabric-samples/commit/a42b858) Update FabCar to reflect wallet API changes
* [890f9ea](https://github.com/hyperledger/fabric-samples/commit/890f9ea) [FAB-16713](https://jira.hyperledger.org/browse/FAB-16713) Fix npm audit warnings
* [e48e804](https://github.com/hyperledger/fabric-samples/commit/e48e804) [FAB-16776](https://jira.hyperledger.org/browse/FAB-16776) Move BYFN up to V2_0 capabilities
* [7b65a25](https://github.com/hyperledger/fabric-samples/commit/7b65a25) [IN-68] Add default GitHub SECURITY policy
* [408e0e8](https://github.com/hyperledger/fabric-samples/commit/408e0e8) [FAB-16619](https://jira.hyperledger.org/browse/FAB-16619) Fix the policy warning
* [670d446](https://github.com/hyperledger/fabric-samples/commit/670d446) [FAB-16668](https://jira.hyperledger.org/browse/FAB-16668) fabcar chaincode modify console output
* [f2939e2](https://github.com/hyperledger/fabric-samples/commit/f2939e2) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for Commercial Paper sample
* [3d19014](https://github.com/hyperledger/fabric-samples/commit/3d19014) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for FabCar sample
* [e2b7cb7](https://github.com/hyperledger/fabric-samples/commit/e2b7cb7) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for abstore sample
* [db48612](https://github.com/hyperledger/fabric-samples/commit/db48612) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Increase chaincode execute timeout
* [521a7ff](https://github.com/hyperledger/fabric-samples/commit/521a7ff) [FAB-16607](https://jira.hyperledger.org/browse/FAB-16607) Update FabCar to reflect CC updates
* [c13a5ec](https://github.com/hyperledger/fabric-samples/commit/c13a5ec) [FAB-16528](https://jira.hyperledger.org/browse/FAB-16528) marbles private chaincode sync up
* [3fad853](https://github.com/hyperledger/fabric-samples/commit/3fad853) [FAB-16528](https://jira.hyperledger.org/browse/FAB-16528) marbles private chaincode sync up
* [8b9b82f](https://github.com/hyperledger/fabric-samples/commit/8b9b82f) [FAB-16489](https://jira.hyperledger.org/browse/FAB-16489) Add CODEOWNERS
* [a6ce915](https://github.com/hyperledger/fabric-samples/commit/a6ce915) [FAB-16487](https://jira.hyperledger.org/browse/FAB-16487) Update eslint
* [48082cf](https://github.com/hyperledger/fabric-samples/commit/48082cf) [FAB-16362](https://jira.hyperledger.org/browse/FAB-16362) adding chaincode excution comments
* [1d379f3](https://github.com/hyperledger/fabric-samples/commit/1d379f3) [FAB-16474](https://jira.hyperledger.org/browse/FAB-16474) marbles02 chaincode error
* [18712ca](https://github.com/hyperledger/fabric-samples/commit/18712ca) [FAB-16133](https://jira.hyperledger.org/browse/FAB-16133) Remove Solo consensus from BYFN
* [91c720a](https://github.com/hyperledger/fabric-samples/commit/91c720a) [FAB-16390](https://jira.hyperledger.org/browse/FAB-16390) Added filter for invalid transactions
* [1d3e267](https://github.com/hyperledger/fabric-samples/commit/1d3e267) Redirect samples to fabric-{chaincode,protos}-go
* [398a5b1](https://github.com/hyperledger/fabric-samples/commit/398a5b1) [FABCI-394] Remove AnsiColor Wrapper
* [ce154e0](https://github.com/hyperledger/fabric-samples/commit/ce154e0) [FAB-16310](https://jira.hyperledger.org/browse/FAB-16310) Vendor Go dependencies in all samples
* [6ea7c71](https://github.com/hyperledger/fabric-samples/commit/6ea7c71) [FAB-16285](https://jira.hyperledger.org/browse/FAB-16285) Update blacklisted versions in BYFN
* [86cd831](https://github.com/hyperledger/fabric-samples/commit/86cd831) [FAB-16284](https://jira.hyperledger.org/browse/FAB-16284) Remove E2E file and -f option from BYFN
* [0063abe](https://github.com/hyperledger/fabric-samples/commit/0063abe) Update stale script name in interest rate swaps
* [3907507](https://github.com/hyperledger/fabric-samples/commit/3907507) [FAB-16277](https://jira.hyperledger.org/browse/FAB-16277) Update BYFN w/ Raft ports in Docker network
* [33b0065](https://github.com/hyperledger/fabric-samples/commit/33b0065) [FAB-14813](https://jira.hyperledger.org/browse/FAB-14813) Channel event sample in fabric-samples
* [b62d5bd](https://github.com/hyperledger/fabric-samples/commit/b62d5bd) [FAB-16132](https://jira.hyperledger.org/browse/FAB-16132) Remove Kafka consensus from BYFN
* [9b14525](https://github.com/hyperledger/fabric-samples/commit/9b14525) [FAB-15213](https://jira.hyperledger.org/browse/FAB-15213) Update Commercial Paper for Java
* [4158877](https://github.com/hyperledger/fabric-samples/commit/4158877) [FAB-16232](https://jira.hyperledger.org/browse/FAB-16232) Remove FabToken sample
* [b6380cc](https://github.com/hyperledger/fabric-samples/commit/b6380cc) [FAB-16198](https://jira.hyperledger.org/browse/FAB-16198) Run "go mod vendor" for FabCar Go contract
* [639848a](https://github.com/hyperledger/fabric-samples/commit/639848a) [FAB-16197](https://jira.hyperledger.org/browse/FAB-16197) Add connection profiles to .gitignore
* [3996db5](https://github.com/hyperledger/fabric-samples/commit/3996db5) [FAB-12219](https://jira.hyperledger.org/browse/FAB-12219) abstore node -> javascript
* [14ac271](https://github.com/hyperledger/fabric-samples/commit/14ac271) [FAB-12219](https://jira.hyperledger.org/browse/FAB-12219) marbles02 node -> javascript
* [13f16e5](https://github.com/hyperledger/fabric-samples/commit/13f16e5) [FGJ-4] CI tests for FabCar Java sample
* [171a7d2](https://github.com/hyperledger/fabric-samples/commit/171a7d2) FGJ-4 Fabcar sample
* [868f9d0](https://github.com/hyperledger/fabric-samples/commit/868f9d0) [FAB-15625](https://jira.hyperledger.org/browse/FAB-15625) Add UT for Simple Asset Chaincode
* [597d150](https://github.com/hyperledger/fabric-samples/commit/597d150) [FAB-14532](https://jira.hyperledger.org/browse/FAB-14532) Remove LL FabCar sample
* [583ff8f](https://github.com/hyperledger/fabric-samples/commit/583ff8f) Use renamed CheckCommitReadiness function
* [750f937](https://github.com/hyperledger/fabric-samples/commit/750f937) [FAB-15213](https://jira.hyperledger.org/browse/FAB-15213) Add Java FabCar sample contract
* [abbda95](https://github.com/hyperledger/fabric-samples/commit/abbda95) [FAB-15897](https://jira.hyperledger.org/browse/FAB-15897) Improve FabCar test logging
* [dd8150a](https://github.com/hyperledger/fabric-samples/commit/dd8150a) [FAB-15104](https://jira.hyperledger.org/browse/FAB-15104) Remove versions from fabric-samples readme
* [1387aa8](https://github.com/hyperledger/fabric-samples/commit/1387aa8) [FAB-15927](https://jira.hyperledger.org/browse/FAB-15927)  Better expression for golang
* [61c33d3](https://github.com/hyperledger/fabric-samples/commit/61c33d3) [FAB-15973](https://jira.hyperledger.org/browse/FAB-15973) use --output json on simulatecommit
* [8bbdd0f](https://github.com/hyperledger/fabric-samples/commit/8bbdd0f) [FAB-15716](https://jira.hyperledger.org/browse/FAB-15716) Fix instructions for dev-mode
* [0254d67](https://github.com/hyperledger/fabric-samples/commit/0254d67) QueryApprovalStatus -> SimulateCommitChaincodeDef
* [c57d67c](https://github.com/hyperledger/fabric-samples/commit/c57d67c) [FAB-15782](https://jira.hyperledger.org/browse/FAB-15782) Sample Go CC should include deps
* [6ba5a19](https://github.com/hyperledger/fabric-samples/commit/6ba5a19) Update to Go 1.12.5 in ci.properties
* [1774a25](https://github.com/hyperledger/fabric-samples/commit/1774a25) [FAB-15723](https://jira.hyperledger.org/browse/FAB-15723) Fix script and instruction with ccenv
* [6ae711c](https://github.com/hyperledger/fabric-samples/commit/6ae711c) [FAB-15717](https://jira.hyperledger.org/browse/FAB-15717) fix Error Unexpected end of JSON input
* [5be56d3](https://github.com/hyperledger/fabric-samples/commit/5be56d3) [FAB-15104](https://jira.hyperledger.org/browse/FAB-15104) Remove scripts/bootstrap.sh
* [779f8f3](https://github.com/hyperledger/fabric-samples/commit/779f8f3) [FAB-15649](https://jira.hyperledger.org/browse/FAB-15649)Fix Fabcar to install Chaincode on all peers
* [7c5f5d3](https://github.com/hyperledger/fabric-samples/commit/7c5f5d3) [FAB-15199](https://jira.hyperledger.org/browse/FAB-15199) Update interest rate sample
* [f0dca20](https://github.com/hyperledger/fabric-samples/commit/f0dca20) [FAB-14532](https://jira.hyperledger.org/browse/FAB-14532) Remove LL FabCar sample
* [1ed1a10](https://github.com/hyperledger/fabric-samples/commit/1ed1a10) [FAB-15573](https://jira.hyperledger.org/browse/FAB-15573) Fix typo in fabric-samples-ci.md
* [2e7fec9](https://github.com/hyperledger/fabric-samples/commit/2e7fec9) [FAB-15601](https://jira.hyperledger.org/browse/FAB-15601) BYFN: Fix MAX_RETRY for couchdb
* [1e9e4c4](https://github.com/hyperledger/fabric-samples/commit/1e9e4c4) [FAB-9329](https://jira.hyperledger.org/browse/FAB-9329) Remove the unused variable in BYFN/EYFN
* [964c09f](https://github.com/hyperledger/fabric-samples/commit/964c09f) [FAB-15601](https://jira.hyperledger.org/browse/FAB-15601) BYFN: Fix MAX_RETRY for couchdb
* [41dca99](https://github.com/hyperledger/fabric-samples/commit/41dca99) [FAB-15127](https://jira.hyperledger.org/browse/FAB-15127) Update high throughput sample
* [3fe014a](https://github.com/hyperledger/fabric-samples/commit/3fe014a) Use official CouchDB image
* [f2d0fa0](https://github.com/hyperledger/fabric-samples/commit/f2d0fa0) [FAB-14487](https://jira.hyperledger.org/browse/FAB-14487) Make FabCar use BYFN, not basic-network
* [e9c3649](https://github.com/hyperledger/fabric-samples/commit/e9c3649) [FAB-15276](https://jira.hyperledger.org/browse/FAB-15276) Fix license statements
* [fbe4036](https://github.com/hyperledger/fabric-samples/commit/fbe4036) [FAB-14486](https://jira.hyperledger.org/browse/FAB-14486) Extend BYFN to opt skip chaincode deploy
* [0c4141f](https://github.com/hyperledger/fabric-samples/commit/0c4141f) [FAB-14485](https://jira.hyperledger.org/browse/FAB-14485) Extend BYFN to opt inc cert authorities
* [529b83b](https://github.com/hyperledger/fabric-samples/commit/529b83b) [FAB-14330](https://jira.hyperledger.org/browse/FAB-14330) Add connection profiles for BYFN and EYFN
* [2c21c83](https://github.com/hyperledger/fabric-samples/commit/2c21c83) [FABN-1184] Update fabtoken/README.md
* [5056a23](https://github.com/hyperledger/fabric-samples/commit/5056a23) [FABN-1184] Add CI script for fabtoken sample app
* [5d6db95](https://github.com/hyperledger/fabric-samples/commit/5d6db95) Update maintainers for fabric-samples
* [f527815](https://github.com/hyperledger/fabric-samples/commit/f527815) [FAB-15119](https://jira.hyperledger.org/browse/FAB-15119) Fix BYFN with Java chaincode
* [8245252](https://github.com/hyperledger/fabric-samples/commit/8245252) [FABN-1184] Implement fabtoken sample app
* [1bd1c2f](https://github.com/hyperledger/fabric-samples/commit/1bd1c2f) FABCI-284 Update CI Pipeline script
* [c24abf9](https://github.com/hyperledger/fabric-samples/commit/c24abf9) [FAB-15022](https://jira.hyperledger.org/browse/FAB-15022) Basic-network support for new lifecycle
* [b64fd45](https://github.com/hyperledger/fabric-samples/commit/b64fd45) [FAB-15051](https://jira.hyperledger.org/browse/FAB-15051) delStandard() function for high-throughput
* [3e68a7e](https://github.com/hyperledger/fabric-samples/commit/3e68a7e) [FAB-14784](https://jira.hyperledger.org/browse/FAB-14784) Remove balance-transfer
* [eb3fe08](https://github.com/hyperledger/fabric-samples/commit/eb3fe08) [FAB-14779](https://jira.hyperledger.org/browse/FAB-14779) QueryApprovalStatus step in byfn
* [2777429](https://github.com/hyperledger/fabric-samples/commit/2777429) [FAB-14711](https://jira.hyperledger.org/browse/FAB-14711) update byfn with new lifecycle
* [aec3389](https://github.com/hyperledger/fabric-samples/commit/aec3389) [FAB-12215](https://jira.hyperledger.org/browse/FAB-12215)WYFA:Remove chainId in tx proposal request
* [b5d5026](https://github.com/hyperledger/fabric-samples/commit/b5d5026) [FAB-14633](https://jira.hyperledger.org/browse/FAB-14633) Remove apt-get from eyfn.sh
* [efaadd3](https://github.com/hyperledger/fabric-samples/commit/efaadd3) [FAB-14531](https://jira.hyperledger.org/browse/FAB-14531) BYFN Raft with 5 nodes
* [d63047c](https://github.com/hyperledger/fabric-samples/commit/d63047c) [FAB-14444](https://jira.hyperledger.org/browse/FAB-14444)
* [7e3d428](https://github.com/hyperledger/fabric-samples/commit/7e3d428) [FAB-14369](https://jira.hyperledger.org/browse/FAB-14369)Fix dev mode failing to build Chaincode
* [420ba23](https://github.com/hyperledger/fabric-samples/commit/420ba23) [FAB-12762](https://jira.hyperledger.org/browse/FAB-12762) Add etcd/raft consensus option to BYFN
* [2b68c80](https://github.com/hyperledger/fabric-samples/commit/2b68c80) [FAB-14317](https://jira.hyperledger.org/browse/FAB-14317) Add default policies to org3
* [f942010](https://github.com/hyperledger/fabric-samples/commit/f942010) [FAB-14268](https://jira.hyperledger.org/browse/FAB-14268) Make BYFN/EYFN ports match external ports
* [4e2ce23](https://github.com/hyperledger/fabric-samples/commit/4e2ce23) [FAB-14271](https://jira.hyperledger.org/browse/FAB-14271) Add channel policies to channel config
* [f26477c](https://github.com/hyperledger/fabric-samples/commit/f26477c) [FAB-11796](https://jira.hyperledger.org/browse/FAB-11796)high-throughput:Remove unnecessary prunesafe
* [137327a](https://github.com/hyperledger/fabric-samples/commit/137327a) [FAB-14162](https://jira.hyperledger.org/browse/FAB-14162) Pin fabric-samples in master to "unstable"
* [6007c09](https://github.com/hyperledger/fabric-samples/commit/6007c09) [FAB-13862](https://jira.hyperledger.org/browse/FAB-13862) Rename example02 ABstore
* [94cb603](https://github.com/hyperledger/fabric-samples/commit/94cb603) [FAB-13933](https://jira.hyperledger.org/browse/FAB-13933) Fix misspellings
* [a8a5539](https://github.com/hyperledger/fabric-samples/commit/a8a5539) Fix doc link Fix variable error
* [b0cda61](https://github.com/hyperledger/fabric-samples/commit/b0cda61) [FAB-13769](https://jira.hyperledger.org/browse/FAB-13769) Add UT code to ABAC sample Chaincode
* [c7438e1](https://github.com/hyperledger/fabric-samples/commit/c7438e1) [FAB-13668](https://jira.hyperledger.org/browse/FAB-13668) BYFN's container volume mapping is bad
* [e48b2de](https://github.com/hyperledger/fabric-samples/commit/e48b2de) [FAB-13489](https://jira.hyperledger.org/browse/FAB-13489) fabric-samples add error msg
* [8a458b5](https://github.com/hyperledger/fabric-samples/commit/8a458b5) [FAB-12056](https://jira.hyperledger.org/browse/FAB-12056) Private marbles cc use transient data
* [6269941](https://github.com/hyperledger/fabric-samples/commit/6269941) Correct broken link
* [461b6ab](https://github.com/hyperledger/fabric-samples/commit/461b6ab) FABC-781 Remove fabric-ca sample
* [e9b9477](https://github.com/hyperledger/fabric-samples/commit/e9b9477) [FAB-13372](https://jira.hyperledger.org/browse/FAB-13372) Fabric-Samples return error msg
* [e3da220](https://github.com/hyperledger/fabric-samples/commit/e3da220) [FAB-13433](https://jira.hyperledger.org/browse/FAB-13433) - Update Jenkinsfile configuration
* [33db64e](https://github.com/hyperledger/fabric-samples/commit/33db64e) Configure Stale ProBot
* [5cd277f](https://github.com/hyperledger/fabric-samples/commit/5cd277f) [FAB-11951](https://jira.hyperledger.org/browse/FAB-11951) Interest-rate swap example for SBE
* [9567985](https://github.com/hyperledger/fabric-samples/commit/9567985) [FAB-13407](https://jira.hyperledger.org/browse/FAB-13407) Align fabric-samples with 1.4.0-rc2 release
* [c7572aa](https://github.com/hyperledger/fabric-samples/commit/c7572aa) [FAB-13305](https://jira.hyperledger.org/browse/FAB-13305) Update scripts to pull latest artifacts
* [ab46e35](https://github.com/hyperledger/fabric-samples/commit/ab46e35) [FAB-13283](https://jira.hyperledger.org/browse/FAB-13283) Update sample code for commercial paper
* [f677821](https://github.com/hyperledger/fabric-samples/commit/f677821) [FAB-13232](https://jira.hyperledger.org/browse/FAB-13232) fix peer node start command
* [6a7472e](https://github.com/hyperledger/fabric-samples/commit/6a7472e) [FAB-13126](https://jira.hyperledger.org/browse/FAB-13126) Align fabric-samples with 1.4.0-rc1 release
* [445ccbc](https://github.com/hyperledger/fabric-samples/commit/445ccbc) [FAB-12880](https://jira.hyperledger.org/browse/FAB-12880) Move old prog model samples for FabCar
* [5be62b5](https://github.com/hyperledger/fabric-samples/commit/5be62b5) [FAB-13207](https://jira.hyperledger.org/browse/FAB-13207) Remove incorrect discovery options
* [fdbd92d](https://github.com/hyperledger/fabric-samples/commit/fdbd92d) [FAB-13206](https://jira.hyperledger.org/browse/FAB-13206) Remove dependencies on fabric-client
* [eff0046](https://github.com/hyperledger/fabric-samples/commit/eff0046) [FAB-12877](https://jira.hyperledger.org/browse/FAB-12877) Add fabcar app using new prog model (JS)
* [c184196](https://github.com/hyperledger/fabric-samples/commit/c184196) [FAB-12878](https://jira.hyperledger.org/browse/FAB-12878) Add fabcar app using new prog model (TS)
* [e1a39e6](https://github.com/hyperledger/fabric-samples/commit/e1a39e6) [FAB-12724](https://jira.hyperledger.org/browse/FAB-12724) Upgrade from 1.3.x to 1.4.0
* [c21bbba](https://github.com/hyperledger/fabric-samples/commit/c21bbba) Update samples to use new logging env variables
* [7ad9f19](https://github.com/hyperledger/fabric-samples/commit/7ad9f19) [FAB-13011](https://jira.hyperledger.org/browse/FAB-13011) add kafka consensus type to byfn sample
* [33f064f](https://github.com/hyperledger/fabric-samples/commit/33f064f) [FAB-13170](https://jira.hyperledger.org/browse/FAB-13170) Add memberOnlyRead to marbles sample
* [928b72b](https://github.com/hyperledger/fabric-samples/commit/928b72b) [FAB-12875](https://jira.hyperledger.org/browse/FAB-12875) Add automated tests for fabcar sample
* [5c087f1](https://github.com/hyperledger/fabric-samples/commit/5c087f1) [FAB-13046](https://jira.hyperledger.org/browse/FAB-13046) Update TypeScript contract dependencies
* [3748983](https://github.com/hyperledger/fabric-samples/commit/3748983) [FAB-12879](https://jira.hyperledger.org/browse/FAB-12879) Update fabcar script for new contracts
* [4fb3b57](https://github.com/hyperledger/fabric-samples/commit/4fb3b57) [FAB-12852](https://jira.hyperledger.org/browse/FAB-12852) Add fabcar contract w/ new prog model (TS)
* [9facb42](https://github.com/hyperledger/fabric-samples/commit/9facb42) [FAB-12851](https://jira.hyperledger.org/browse/FAB-12851) Add fabcar contract w/ new prog model (JS)
* [e67fcf1](https://github.com/hyperledger/fabric-samples/commit/e67fcf1) [FAB-12322](https://jira.hyperledger.org/browse/FAB-12322) Update commercial-paper sample
* [fd6e2c4](https://github.com/hyperledger/fabric-samples/commit/fd6e2c4) [FAB-12703](https://jira.hyperledger.org/browse/FAB-12703) Fix misspelling "lauches"
* [c05f172](https://github.com/hyperledger/fabric-samples/commit/c05f172) [FAB-12608](https://jira.hyperledger.org/browse/FAB-12608) Update pipeline script
* [286861e](https://github.com/hyperledger/fabric-samples/commit/286861e) [FAB-12371](https://jira.hyperledger.org/browse/FAB-12371)Fix the abac sample to use new cid package
* [24c5e47](https://github.com/hyperledger/fabric-samples/commit/24c5e47) [FAB-12026](https://jira.hyperledger.org/browse/FAB-12026) pagination samples for node marbles02
* [6dc5ce5](https://github.com/hyperledger/fabric-samples/commit/6dc5ce5) [FAB-12587](https://jira.hyperledger.org/browse/FAB-12587) Fix for Query Block by block hash API
* [df311ce](https://github.com/hyperledger/fabric-samples/commit/df311ce) [FAB-12173](https://jira.hyperledger.org/browse/FAB-12173) balance-transfer: Update anchor peers
* [c925148](https://github.com/hyperledger/fabric-samples/commit/c925148) [FAB-12415](https://jira.hyperledger.org/browse/FAB-12415) samples for 1.3.0 (master cleanup)
* [3a12c60](https://github.com/hyperledger/fabric-samples/commit/3a12c60) [FAB-12275](https://jira.hyperledger.org/browse/FAB-12275) Fix the warn in creating genesis block
* [c6f6324](https://github.com/hyperledger/fabric-samples/commit/c6f6324) [FAB-12257](https://jira.hyperledger.org/browse/FAB-12257) allow balance-transfer for doscovery
* [4445e8d](https://github.com/hyperledger/fabric-samples/commit/4445e8d) [FAB-12272](https://jira.hyperledger.org/browse/FAB-12272) Increase MAX_RETRY to 10
* [33d333f](https://github.com/hyperledger/fabric-samples/commit/33d333f) [FAB-12190](https://jira.hyperledger.org/browse/FAB-12190) Update stable version in CI scripts
* [4089786](https://github.com/hyperledger/fabric-samples/commit/4089786) [FAB-11867](https://jira.hyperledger.org/browse/FAB-11867) Develop Apps:Sample pt 2 -- application
* [d776651](https://github.com/hyperledger/fabric-samples/commit/d776651) [FAB-11723](https://jira.hyperledger.org/browse/FAB-11723) Developing Apps: Sample pt 1 -- contract

## "v1.3.0"

* [11e4c23](https://github.com/hyperledger/fabric-samples/commit/11e4c23) Update samples to use v2.0 or later dependencies (#161)
* [94beab7](https://github.com/hyperledger/fabric-samples/commit/94beab7) FABN-1531 Use v2.1.0 sdk-node modules
* [8820d2f](https://github.com/hyperledger/fabric-samples/commit/8820d2f) Fix commercial-paper README
* [aa9b577](https://github.com/hyperledger/fabric-samples/commit/aa9b577) Remove TLS enabled switch (#155)
* [381fb46](https://github.com/hyperledger/fabric-samples/commit/381fb46) upgraded abstore golang chaincode to use contract-api package (#154)
* [5e5d2c8](https://github.com/hyperledger/fabric-samples/commit/5e5d2c8) Update java chaincode to be compatible with doc and other implementations (#149)
* [c572c51](https://github.com/hyperledger/fabric-samples/commit/c572c51) Organize and Standardize `ci` Directory Content (#152)
* [aa40963](https://github.com/hyperledger/fabric-samples/commit/aa40963) Perform General Cleanup (#151)
* [da41afa](https://github.com/hyperledger/fabric-samples/commit/da41afa) Remove left over rm -rf command from BYFN (#148)
* [4bb48a9](https://github.com/hyperledger/fabric-samples/commit/4bb48a9) Jenkins no longer used (#145)
* [6f984e1](https://github.com/hyperledger/fabric-samples/commit/6f984e1) Bump acorn from 6.4.0 to 6.4.1 in /fabcar/javascript (#144)
* [b155620](https://github.com/hyperledger/fabric-samples/commit/b155620) Remove redundant invoke command from test network (#142)
* [851933b](https://github.com/hyperledger/fabric-samples/commit/851933b) Add enrollUser files to commercial paper (#140)
* [87600bd](https://github.com/hyperledger/fabric-samples/commit/87600bd) [FAB-17268](https://jira.hyperledger.org/browse/FAB-17268) Move fabcar sample to test network (#103)
* [9397788](https://github.com/hyperledger/fabric-samples/commit/9397788) Wrong groupId on hyperledger fabric dependencies for java-application (#134)
* [92555fb](https://github.com/hyperledger/fabric-samples/commit/92555fb) Update README.md (#133)
* [59c6641](https://github.com/hyperledger/fabric-samples/commit/59c6641) Change Download Location of Fabric Binaries (#143)
* [1f283fc](https://github.com/hyperledger/fabric-samples/commit/1f283fc) init function does not exist on fabcar (#141)
* [defb6bb](https://github.com/hyperledger/fabric-samples/commit/defb6bb) [FAB-17656](https://jira.hyperledger.org/browse/FAB-17656) echo Generating channel.tx (#139)
* [4c7bab0](https://github.com/hyperledger/fabric-samples/commit/4c7bab0) fix: package seletor REGEX (#135)
* [db69c6f](https://github.com/hyperledger/fabric-samples/commit/db69c6f) Add fabcar external service sample (#136)
* [7f5f5e6](https://github.com/hyperledger/fabric-samples/commit/7f5f5e6) [FAB-17504](https://jira.hyperledger.org/browse/FAB-17504) add Organizations.<Org>.OrdererEndpoints and remove Orderer.Addresses (#125)
* [f3fc08d](https://github.com/hyperledger/fabric-samples/commit/f3fc08d) Remove solo and kafka from test net configtx.yaml (#137)
* [e17574d](https://github.com/hyperledger/fabric-samples/commit/e17574d) Add CA's to docker test network (#124)
* [faac18e](https://github.com/hyperledger/fabric-samples/commit/faac18e) [FAB-17461](https://jira.hyperledger.org/browse/FAB-17461) Move off_chain_data sample to test network (#122)
* [121a44a](https://github.com/hyperledger/fabric-samples/commit/121a44a) [FAB-17460](https://jira.hyperledger.org/browse/FAB-17460) Move High Throughput sample to test network (#112)
* [a2f3a66](https://github.com/hyperledger/fabric-samples/commit/a2f3a66) Update docker image version
* [e5b898c](https://github.com/hyperledger/fabric-samples/commit/e5b898c) Revert "first-network/scripts/*: Make Chaincode name configurable (#118)" (#131)
* [9ef61e2](https://github.com/hyperledger/fabric-samples/commit/9ef61e2) first-network/scripts/*: Make Chaincode name configurable (#118)
* [e204ebb](https://github.com/hyperledger/fabric-samples/commit/e204ebb) Remove reference to 2.0 beta (#111)
* [3dbe116](https://github.com/hyperledger/fabric-samples/commit/3dbe116) [FAB-17456](https://jira.hyperledger.org/browse/FAB-17456) fabric-samples read ccp (#117)
* [965ed1f](https://github.com/hyperledger/fabric-samples/commit/965ed1f) [FAB-17498](https://jira.hyperledger.org/browse/FAB-17498) Beta Images removal, test test-network (#121)
* [403019e](https://github.com/hyperledger/fabric-samples/commit/403019e) [FAB-17495](https://jira.hyperledger.org/browse/FAB-17495) Remove Basic Network sample (#120)
* [883ef99](https://github.com/hyperledger/fabric-samples/commit/883ef99) [FAB-17457](https://jira.hyperledger.org/browse/FAB-17457) Script correction (#119)
* [b89ee34](https://github.com/hyperledger/fabric-samples/commit/b89ee34) Update Commercial Paper to v2.0 Lifecycle (#109)
* [4208644](https://github.com/hyperledger/fabric-samples/commit/4208644) [FAB-17478](https://jira.hyperledger.org/browse/FAB-17478) Update commercial paper to use go api v1.0.0 (#115)
* [0df5ed9](https://github.com/hyperledger/fabric-samples/commit/0df5ed9) [FAB-17477](https://jira.hyperledger.org/browse/FAB-17477) Update fabcar to use go api v1.0.0 (#116)
* [571733f](https://github.com/hyperledger/fabric-samples/commit/571733f) [FAB-17447](https://jira.hyperledger.org/browse/FAB-17447) Update to 2.0.0 Libraries
* [67b4ee7](https://github.com/hyperledger/fabric-samples/commit/67b4ee7) Add Org3 bugs in test network (#108)
* [5b93dd0](https://github.com/hyperledger/fabric-samples/commit/5b93dd0) [FAB-17140](https://jira.hyperledger.org/browse/FAB-17140) Add go commercial paper contract (#102)
* [4fe6a25](https://github.com/hyperledger/fabric-samples/commit/4fe6a25) [FABCI-482] Update Nexus URL's to Artifactory (#92)
* [1488fbb](https://github.com/hyperledger/fabric-samples/commit/1488fbb) Add 1.x versions of fabric to blacklisted versions
* [8ca279d](https://github.com/hyperledger/fabric-samples/commit/8ca279d) Add Support for Versioning NodeJS (#106)
* [b3b5267](https://github.com/hyperledger/fabric-samples/commit/b3b5267) [FAB-17243](https://jira.hyperledger.org/browse/FAB-17243) Add support for Fabric CA for Org3 on the (#91)
* [ce41ff7](https://github.com/hyperledger/fabric-samples/commit/ce41ff7) Remove references to vendoring chaincode from your gopath (#96)
* [4235d30](https://github.com/hyperledger/fabric-samples/commit/4235d30) [FAB-17306](https://jira.hyperledger.org/browse/FAB-17306) Fix artifact names in test-network (#97)
* [4c2a0a4](https://github.com/hyperledger/fabric-samples/commit/4c2a0a4) [FAB-16147](https://jira.hyperledger.org/browse/FAB-16147) Update Commercial Paper to work with v2 (#98)
* [6d9fd6f](https://github.com/hyperledger/fabric-samples/commit/6d9fd6f) Remove Gerrit reference
* [a026a4f](https://github.com/hyperledger/fabric-samples/commit/a026a4f) Fixed typo (#90)
* [cdb0e8b](https://github.com/hyperledger/fabric-samples/commit/cdb0e8b) TYPO (#89)
* [94ac8b6](https://github.com/hyperledger/fabric-samples/commit/94ac8b6) Update to use beta levels of modules (#88)
* [d848633](https://github.com/hyperledger/fabric-samples/commit/d848633) [FAB-16844](https://jira.hyperledger.org/browse/FAB-16844) Correct BYFN CC name
* [73267e1](https://github.com/hyperledger/fabric-samples/commit/73267e1) Fix test network bugs for adding org3
* [5d58254](https://github.com/hyperledger/fabric-samples/commit/5d58254) [FAB-17145](https://jira.hyperledger.org/browse/FAB-17145) Add test network to Fabric Samples
* [e9f2957](https://github.com/hyperledger/fabric-samples/commit/e9f2957) [FAB-17062](https://jira.hyperledger.org/browse/FAB-17062) Fix typos in Commercial Paper readme
* [36694d0](https://github.com/hyperledger/fabric-samples/commit/36694d0) [FAB-17121](https://jira.hyperledger.org/browse/FAB-17121) Use new bootstrap config in orderer
* [429f087](https://github.com/hyperledger/fabric-samples/commit/429f087) update fabcar go to new programming model
* [1467086](https://github.com/hyperledger/fabric-samples/commit/1467086) Bump eslint-utils
* [33f349a](https://github.com/hyperledger/fabric-samples/commit/33f349a) Remove Stalebot
* [6af43bf](https://github.com/hyperledger/fabric-samples/commit/6af43bf) Change stalebot settings
* [4880401](https://github.com/hyperledger/fabric-samples/commit/4880401) [FAB-16284](https://jira.hyperledger.org/browse/FAB-16284) Remove E2E file and -f option from BYFN
* [fe96f60](https://github.com/hyperledger/fabric-samples/commit/fe96f60) [FAB-16850](https://jira.hyperledger.org/browse/FAB-16850) Set up CI with Azure Pipelines
* [81aabf4](https://github.com/hyperledger/fabric-samples/commit/81aabf4) [FAB-16849](https://jira.hyperledger.org/browse/FAB-16849) Various updates for Java version of FabCar
* [a42b858](https://github.com/hyperledger/fabric-samples/commit/a42b858) Update FabCar to reflect wallet API changes
* [890f9ea](https://github.com/hyperledger/fabric-samples/commit/890f9ea) [FAB-16713](https://jira.hyperledger.org/browse/FAB-16713) Fix npm audit warnings
* [e48e804](https://github.com/hyperledger/fabric-samples/commit/e48e804) [FAB-16776](https://jira.hyperledger.org/browse/FAB-16776) Move BYFN up to V2_0 capabilities
* [7b65a25](https://github.com/hyperledger/fabric-samples/commit/7b65a25) [IN-68] Add default GitHub SECURITY policy
* [408e0e8](https://github.com/hyperledger/fabric-samples/commit/408e0e8) [FAB-16619](https://jira.hyperledger.org/browse/FAB-16619) Fix the policy warning
* [670d446](https://github.com/hyperledger/fabric-samples/commit/670d446) [FAB-16668](https://jira.hyperledger.org/browse/FAB-16668) fabcar chaincode modify console output
* [f2939e2](https://github.com/hyperledger/fabric-samples/commit/f2939e2) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for Commercial Paper sample
* [3d19014](https://github.com/hyperledger/fabric-samples/commit/3d19014) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for FabCar sample
* [e2b7cb7](https://github.com/hyperledger/fabric-samples/commit/e2b7cb7) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for abstore sample
* [db48612](https://github.com/hyperledger/fabric-samples/commit/db48612) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Increase chaincode execute timeout
* [521a7ff](https://github.com/hyperledger/fabric-samples/commit/521a7ff) [FAB-16607](https://jira.hyperledger.org/browse/FAB-16607) Update FabCar to reflect CC updates
* [c13a5ec](https://github.com/hyperledger/fabric-samples/commit/c13a5ec) [FAB-16528](https://jira.hyperledger.org/browse/FAB-16528) marbles private chaincode sync up
* [3fad853](https://github.com/hyperledger/fabric-samples/commit/3fad853) [FAB-16528](https://jira.hyperledger.org/browse/FAB-16528) marbles private chaincode sync up
* [8b9b82f](https://github.com/hyperledger/fabric-samples/commit/8b9b82f) [FAB-16489](https://jira.hyperledger.org/browse/FAB-16489) Add CODEOWNERS
* [a6ce915](https://github.com/hyperledger/fabric-samples/commit/a6ce915) [FAB-16487](https://jira.hyperledger.org/browse/FAB-16487) Update eslint
* [48082cf](https://github.com/hyperledger/fabric-samples/commit/48082cf) [FAB-16362](https://jira.hyperledger.org/browse/FAB-16362) adding chaincode excution comments
* [1d379f3](https://github.com/hyperledger/fabric-samples/commit/1d379f3) [FAB-16474](https://jira.hyperledger.org/browse/FAB-16474) marbles02 chaincode error
* [18712ca](https://github.com/hyperledger/fabric-samples/commit/18712ca) [FAB-16133](https://jira.hyperledger.org/browse/FAB-16133) Remove Solo consensus from BYFN
* [91c720a](https://github.com/hyperledger/fabric-samples/commit/91c720a) [FAB-16390](https://jira.hyperledger.org/browse/FAB-16390) Added filter for invalid transactions
* [1d3e267](https://github.com/hyperledger/fabric-samples/commit/1d3e267) Redirect samples to fabric-{chaincode,protos}-go
* [398a5b1](https://github.com/hyperledger/fabric-samples/commit/398a5b1) [FABCI-394] Remove AnsiColor Wrapper
* [ce154e0](https://github.com/hyperledger/fabric-samples/commit/ce154e0) [FAB-16310](https://jira.hyperledger.org/browse/FAB-16310) Vendor Go dependencies in all samples
* [6ea7c71](https://github.com/hyperledger/fabric-samples/commit/6ea7c71) [FAB-16285](https://jira.hyperledger.org/browse/FAB-16285) Update blacklisted versions in BYFN
* [86cd831](https://github.com/hyperledger/fabric-samples/commit/86cd831) [FAB-16284](https://jira.hyperledger.org/browse/FAB-16284) Remove E2E file and -f option from BYFN
* [0063abe](https://github.com/hyperledger/fabric-samples/commit/0063abe) Update stale script name in interest rate swaps
* [3907507](https://github.com/hyperledger/fabric-samples/commit/3907507) [FAB-16277](https://jira.hyperledger.org/browse/FAB-16277) Update BYFN w/ Raft ports in Docker network
* [33b0065](https://github.com/hyperledger/fabric-samples/commit/33b0065) [FAB-14813](https://jira.hyperledger.org/browse/FAB-14813) Channel event sample in fabric-samples
* [b62d5bd](https://github.com/hyperledger/fabric-samples/commit/b62d5bd) [FAB-16132](https://jira.hyperledger.org/browse/FAB-16132) Remove Kafka consensus from BYFN
* [9b14525](https://github.com/hyperledger/fabric-samples/commit/9b14525) [FAB-15213](https://jira.hyperledger.org/browse/FAB-15213) Update Commercial Paper for Java
* [4158877](https://github.com/hyperledger/fabric-samples/commit/4158877) [FAB-16232](https://jira.hyperledger.org/browse/FAB-16232) Remove FabToken sample
* [b6380cc](https://github.com/hyperledger/fabric-samples/commit/b6380cc) [FAB-16198](https://jira.hyperledger.org/browse/FAB-16198) Run "go mod vendor" for FabCar Go contract
* [639848a](https://github.com/hyperledger/fabric-samples/commit/639848a) [FAB-16197](https://jira.hyperledger.org/browse/FAB-16197) Add connection profiles to .gitignore
* [3996db5](https://github.com/hyperledger/fabric-samples/commit/3996db5) [FAB-12219](https://jira.hyperledger.org/browse/FAB-12219) abstore node -> javascript
* [14ac271](https://github.com/hyperledger/fabric-samples/commit/14ac271) [FAB-12219](https://jira.hyperledger.org/browse/FAB-12219) marbles02 node -> javascript
* [13f16e5](https://github.com/hyperledger/fabric-samples/commit/13f16e5) [FGJ-4] CI tests for FabCar Java sample
* [171a7d2](https://github.com/hyperledger/fabric-samples/commit/171a7d2) FGJ-4 Fabcar sample
* [868f9d0](https://github.com/hyperledger/fabric-samples/commit/868f9d0) [FAB-15625](https://jira.hyperledger.org/browse/FAB-15625) Add UT for Simple Asset Chaincode
* [597d150](https://github.com/hyperledger/fabric-samples/commit/597d150) [FAB-14532](https://jira.hyperledger.org/browse/FAB-14532) Remove LL FabCar sample
* [583ff8f](https://github.com/hyperledger/fabric-samples/commit/583ff8f) Use renamed CheckCommitReadiness function
* [750f937](https://github.com/hyperledger/fabric-samples/commit/750f937) [FAB-15213](https://jira.hyperledger.org/browse/FAB-15213) Add Java FabCar sample contract
* [abbda95](https://github.com/hyperledger/fabric-samples/commit/abbda95) [FAB-15897](https://jira.hyperledger.org/browse/FAB-15897) Improve FabCar test logging
* [dd8150a](https://github.com/hyperledger/fabric-samples/commit/dd8150a) [FAB-15104](https://jira.hyperledger.org/browse/FAB-15104) Remove versions from fabric-samples readme
* [1387aa8](https://github.com/hyperledger/fabric-samples/commit/1387aa8) [FAB-15927](https://jira.hyperledger.org/browse/FAB-15927)  Better expression for golang
* [61c33d3](https://github.com/hyperledger/fabric-samples/commit/61c33d3) [FAB-15973](https://jira.hyperledger.org/browse/FAB-15973) use --output json on simulatecommit
* [8bbdd0f](https://github.com/hyperledger/fabric-samples/commit/8bbdd0f) [FAB-15716](https://jira.hyperledger.org/browse/FAB-15716) Fix instructions for dev-mode
* [0254d67](https://github.com/hyperledger/fabric-samples/commit/0254d67) QueryApprovalStatus -> SimulateCommitChaincodeDef
* [c57d67c](https://github.com/hyperledger/fabric-samples/commit/c57d67c) [FAB-15782](https://jira.hyperledger.org/browse/FAB-15782) Sample Go CC should include deps
* [6ba5a19](https://github.com/hyperledger/fabric-samples/commit/6ba5a19) Update to Go 1.12.5 in ci.properties
* [1774a25](https://github.com/hyperledger/fabric-samples/commit/1774a25) [FAB-15723](https://jira.hyperledger.org/browse/FAB-15723) Fix script and instruction with ccenv
* [6ae711c](https://github.com/hyperledger/fabric-samples/commit/6ae711c) [FAB-15717](https://jira.hyperledger.org/browse/FAB-15717) fix Error Unexpected end of JSON input
* [5be56d3](https://github.com/hyperledger/fabric-samples/commit/5be56d3) [FAB-15104](https://jira.hyperledger.org/browse/FAB-15104) Remove scripts/bootstrap.sh
* [779f8f3](https://github.com/hyperledger/fabric-samples/commit/779f8f3) [FAB-15649](https://jira.hyperledger.org/browse/FAB-15649)Fix Fabcar to install Chaincode on all peers
* [7c5f5d3](https://github.com/hyperledger/fabric-samples/commit/7c5f5d3) [FAB-15199](https://jira.hyperledger.org/browse/FAB-15199) Update interest rate sample
* [f0dca20](https://github.com/hyperledger/fabric-samples/commit/f0dca20) [FAB-14532](https://jira.hyperledger.org/browse/FAB-14532) Remove LL FabCar sample
* [1ed1a10](https://github.com/hyperledger/fabric-samples/commit/1ed1a10) [FAB-15573](https://jira.hyperledger.org/browse/FAB-15573) Fix typo in fabric-samples-ci.md
* [2e7fec9](https://github.com/hyperledger/fabric-samples/commit/2e7fec9) [FAB-15601](https://jira.hyperledger.org/browse/FAB-15601) BYFN: Fix MAX_RETRY for couchdb
* [1e9e4c4](https://github.com/hyperledger/fabric-samples/commit/1e9e4c4) [FAB-9329](https://jira.hyperledger.org/browse/FAB-9329) Remove the unused variable in BYFN/EYFN
* [964c09f](https://github.com/hyperledger/fabric-samples/commit/964c09f) [FAB-15601](https://jira.hyperledger.org/browse/FAB-15601) BYFN: Fix MAX_RETRY for couchdb
* [41dca99](https://github.com/hyperledger/fabric-samples/commit/41dca99) [FAB-15127](https://jira.hyperledger.org/browse/FAB-15127) Update high throughput sample
* [3fe014a](https://github.com/hyperledger/fabric-samples/commit/3fe014a) Use official CouchDB image
* [f2d0fa0](https://github.com/hyperledger/fabric-samples/commit/f2d0fa0) [FAB-14487](https://jira.hyperledger.org/browse/FAB-14487) Make FabCar use BYFN, not basic-network
* [e9c3649](https://github.com/hyperledger/fabric-samples/commit/e9c3649) [FAB-15276](https://jira.hyperledger.org/browse/FAB-15276) Fix license statements
* [fbe4036](https://github.com/hyperledger/fabric-samples/commit/fbe4036) [FAB-14486](https://jira.hyperledger.org/browse/FAB-14486) Extend BYFN to opt skip chaincode deploy
* [0c4141f](https://github.com/hyperledger/fabric-samples/commit/0c4141f) [FAB-14485](https://jira.hyperledger.org/browse/FAB-14485) Extend BYFN to opt inc cert authorities
* [529b83b](https://github.com/hyperledger/fabric-samples/commit/529b83b) [FAB-14330](https://jira.hyperledger.org/browse/FAB-14330) Add connection profiles for BYFN and EYFN
* [2c21c83](https://github.com/hyperledger/fabric-samples/commit/2c21c83) [FABN-1184] Update fabtoken/README.md
* [5056a23](https://github.com/hyperledger/fabric-samples/commit/5056a23) [FABN-1184] Add CI script for fabtoken sample app
* [5d6db95](https://github.com/hyperledger/fabric-samples/commit/5d6db95) Update maintainers for fabric-samples
* [f527815](https://github.com/hyperledger/fabric-samples/commit/f527815) [FAB-15119](https://jira.hyperledger.org/browse/FAB-15119) Fix BYFN with Java chaincode
* [8245252](https://github.com/hyperledger/fabric-samples/commit/8245252) [FABN-1184] Implement fabtoken sample app
* [1bd1c2f](https://github.com/hyperledger/fabric-samples/commit/1bd1c2f) FABCI-284 Update CI Pipeline script
* [c24abf9](https://github.com/hyperledger/fabric-samples/commit/c24abf9) [FAB-15022](https://jira.hyperledger.org/browse/FAB-15022) Basic-network support for new lifecycle
* [b64fd45](https://github.com/hyperledger/fabric-samples/commit/b64fd45) [FAB-15051](https://jira.hyperledger.org/browse/FAB-15051) delStandard() function for high-throughput
* [3e68a7e](https://github.com/hyperledger/fabric-samples/commit/3e68a7e) [FAB-14784](https://jira.hyperledger.org/browse/FAB-14784) Remove balance-transfer
* [eb3fe08](https://github.com/hyperledger/fabric-samples/commit/eb3fe08) [FAB-14779](https://jira.hyperledger.org/browse/FAB-14779) QueryApprovalStatus step in byfn
* [2777429](https://github.com/hyperledger/fabric-samples/commit/2777429) [FAB-14711](https://jira.hyperledger.org/browse/FAB-14711) update byfn with new lifecycle
* [aec3389](https://github.com/hyperledger/fabric-samples/commit/aec3389) [FAB-12215](https://jira.hyperledger.org/browse/FAB-12215)WYFA:Remove chainId in tx proposal request
* [b5d5026](https://github.com/hyperledger/fabric-samples/commit/b5d5026) [FAB-14633](https://jira.hyperledger.org/browse/FAB-14633) Remove apt-get from eyfn.sh
* [efaadd3](https://github.com/hyperledger/fabric-samples/commit/efaadd3) [FAB-14531](https://jira.hyperledger.org/browse/FAB-14531) BYFN Raft with 5 nodes
* [d63047c](https://github.com/hyperledger/fabric-samples/commit/d63047c) [FAB-14444](https://jira.hyperledger.org/browse/FAB-14444)
* [7e3d428](https://github.com/hyperledger/fabric-samples/commit/7e3d428) [FAB-14369](https://jira.hyperledger.org/browse/FAB-14369)Fix dev mode failing to build Chaincode
* [420ba23](https://github.com/hyperledger/fabric-samples/commit/420ba23) [FAB-12762](https://jira.hyperledger.org/browse/FAB-12762) Add etcd/raft consensus option to BYFN
* [2b68c80](https://github.com/hyperledger/fabric-samples/commit/2b68c80) [FAB-14317](https://jira.hyperledger.org/browse/FAB-14317) Add default policies to org3
* [f942010](https://github.com/hyperledger/fabric-samples/commit/f942010) [FAB-14268](https://jira.hyperledger.org/browse/FAB-14268) Make BYFN/EYFN ports match external ports
* [4e2ce23](https://github.com/hyperledger/fabric-samples/commit/4e2ce23) [FAB-14271](https://jira.hyperledger.org/browse/FAB-14271) Add channel policies to channel config
* [f26477c](https://github.com/hyperledger/fabric-samples/commit/f26477c) [FAB-11796](https://jira.hyperledger.org/browse/FAB-11796)high-throughput:Remove unnecessary prunesafe
* [137327a](https://github.com/hyperledger/fabric-samples/commit/137327a) [FAB-14162](https://jira.hyperledger.org/browse/FAB-14162) Pin fabric-samples in master to "unstable"
* [6007c09](https://github.com/hyperledger/fabric-samples/commit/6007c09) [FAB-13862](https://jira.hyperledger.org/browse/FAB-13862) Rename example02 ABstore
* [94cb603](https://github.com/hyperledger/fabric-samples/commit/94cb603) [FAB-13933](https://jira.hyperledger.org/browse/FAB-13933) Fix misspellings
* [a8a5539](https://github.com/hyperledger/fabric-samples/commit/a8a5539) Fix doc link Fix variable error
* [b0cda61](https://github.com/hyperledger/fabric-samples/commit/b0cda61) [FAB-13769](https://jira.hyperledger.org/browse/FAB-13769) Add UT code to ABAC sample Chaincode
* [c7438e1](https://github.com/hyperledger/fabric-samples/commit/c7438e1) [FAB-13668](https://jira.hyperledger.org/browse/FAB-13668) BYFN's container volume mapping is bad
* [e48b2de](https://github.com/hyperledger/fabric-samples/commit/e48b2de) [FAB-13489](https://jira.hyperledger.org/browse/FAB-13489) fabric-samples add error msg
* [8a458b5](https://github.com/hyperledger/fabric-samples/commit/8a458b5) [FAB-12056](https://jira.hyperledger.org/browse/FAB-12056) Private marbles cc use transient data
* [6269941](https://github.com/hyperledger/fabric-samples/commit/6269941) Correct broken link
* [461b6ab](https://github.com/hyperledger/fabric-samples/commit/461b6ab) FABC-781 Remove fabric-ca sample
* [e9b9477](https://github.com/hyperledger/fabric-samples/commit/e9b9477) [FAB-13372](https://jira.hyperledger.org/browse/FAB-13372) Fabric-Samples return error msg
* [e3da220](https://github.com/hyperledger/fabric-samples/commit/e3da220) [FAB-13433](https://jira.hyperledger.org/browse/FAB-13433) - Update Jenkinsfile configuration
* [33db64e](https://github.com/hyperledger/fabric-samples/commit/33db64e) Configure Stale ProBot
* [5cd277f](https://github.com/hyperledger/fabric-samples/commit/5cd277f) [FAB-11951](https://jira.hyperledger.org/browse/FAB-11951) Interest-rate swap example for SBE
* [9567985](https://github.com/hyperledger/fabric-samples/commit/9567985) [FAB-13407](https://jira.hyperledger.org/browse/FAB-13407) Align fabric-samples with 1.4.0-rc2 release
* [c7572aa](https://github.com/hyperledger/fabric-samples/commit/c7572aa) [FAB-13305](https://jira.hyperledger.org/browse/FAB-13305) Update scripts to pull latest artifacts
* [ab46e35](https://github.com/hyperledger/fabric-samples/commit/ab46e35) [FAB-13283](https://jira.hyperledger.org/browse/FAB-13283) Update sample code for commercial paper
* [f677821](https://github.com/hyperledger/fabric-samples/commit/f677821) [FAB-13232](https://jira.hyperledger.org/browse/FAB-13232) fix peer node start command
* [6a7472e](https://github.com/hyperledger/fabric-samples/commit/6a7472e) [FAB-13126](https://jira.hyperledger.org/browse/FAB-13126) Align fabric-samples with 1.4.0-rc1 release
* [445ccbc](https://github.com/hyperledger/fabric-samples/commit/445ccbc) [FAB-12880](https://jira.hyperledger.org/browse/FAB-12880) Move old prog model samples for FabCar
* [5be62b5](https://github.com/hyperledger/fabric-samples/commit/5be62b5) [FAB-13207](https://jira.hyperledger.org/browse/FAB-13207) Remove incorrect discovery options
* [fdbd92d](https://github.com/hyperledger/fabric-samples/commit/fdbd92d) [FAB-13206](https://jira.hyperledger.org/browse/FAB-13206) Remove dependencies on fabric-client
* [eff0046](https://github.com/hyperledger/fabric-samples/commit/eff0046) [FAB-12877](https://jira.hyperledger.org/browse/FAB-12877) Add fabcar app using new prog model (JS)
* [c184196](https://github.com/hyperledger/fabric-samples/commit/c184196) [FAB-12878](https://jira.hyperledger.org/browse/FAB-12878) Add fabcar app using new prog model (TS)
* [e1a39e6](https://github.com/hyperledger/fabric-samples/commit/e1a39e6) [FAB-12724](https://jira.hyperledger.org/browse/FAB-12724) Upgrade from 1.3.x to 1.4.0
* [c21bbba](https://github.com/hyperledger/fabric-samples/commit/c21bbba) Update samples to use new logging env variables
* [7ad9f19](https://github.com/hyperledger/fabric-samples/commit/7ad9f19) [FAB-13011](https://jira.hyperledger.org/browse/FAB-13011) add kafka consensus type to byfn sample
* [33f064f](https://github.com/hyperledger/fabric-samples/commit/33f064f) [FAB-13170](https://jira.hyperledger.org/browse/FAB-13170) Add memberOnlyRead to marbles sample
* [928b72b](https://github.com/hyperledger/fabric-samples/commit/928b72b) [FAB-12875](https://jira.hyperledger.org/browse/FAB-12875) Add automated tests for fabcar sample
* [5c087f1](https://github.com/hyperledger/fabric-samples/commit/5c087f1) [FAB-13046](https://jira.hyperledger.org/browse/FAB-13046) Update TypeScript contract dependencies
* [3748983](https://github.com/hyperledger/fabric-samples/commit/3748983) [FAB-12879](https://jira.hyperledger.org/browse/FAB-12879) Update fabcar script for new contracts
* [4fb3b57](https://github.com/hyperledger/fabric-samples/commit/4fb3b57) [FAB-12852](https://jira.hyperledger.org/browse/FAB-12852) Add fabcar contract w/ new prog model (TS)
* [9facb42](https://github.com/hyperledger/fabric-samples/commit/9facb42) [FAB-12851](https://jira.hyperledger.org/browse/FAB-12851) Add fabcar contract w/ new prog model (JS)
* [e67fcf1](https://github.com/hyperledger/fabric-samples/commit/e67fcf1) [FAB-12322](https://jira.hyperledger.org/browse/FAB-12322) Update commercial-paper sample
* [fd6e2c4](https://github.com/hyperledger/fabric-samples/commit/fd6e2c4) [FAB-12703](https://jira.hyperledger.org/browse/FAB-12703) Fix misspelling "lauches"
* [c05f172](https://github.com/hyperledger/fabric-samples/commit/c05f172) [FAB-12608](https://jira.hyperledger.org/browse/FAB-12608) Update pipeline script
* [286861e](https://github.com/hyperledger/fabric-samples/commit/286861e) [FAB-12371](https://jira.hyperledger.org/browse/FAB-12371)Fix the abac sample to use new cid package
* [24c5e47](https://github.com/hyperledger/fabric-samples/commit/24c5e47) [FAB-12026](https://jira.hyperledger.org/browse/FAB-12026) pagination samples for node marbles02
* [6dc5ce5](https://github.com/hyperledger/fabric-samples/commit/6dc5ce5) [FAB-12587](https://jira.hyperledger.org/browse/FAB-12587) Fix for Query Block by block hash API
* [df311ce](https://github.com/hyperledger/fabric-samples/commit/df311ce) [FAB-12173](https://jira.hyperledger.org/browse/FAB-12173) balance-transfer: Update anchor peers
* [c925148](https://github.com/hyperledger/fabric-samples/commit/c925148) [FAB-12415](https://jira.hyperledger.org/browse/FAB-12415) samples for 1.3.0 (master cleanup)
* [3a12c60](https://github.com/hyperledger/fabric-samples/commit/3a12c60) [FAB-12275](https://jira.hyperledger.org/browse/FAB-12275) Fix the warn in creating genesis block
* [c6f6324](https://github.com/hyperledger/fabric-samples/commit/c6f6324) [FAB-12257](https://jira.hyperledger.org/browse/FAB-12257) allow balance-transfer for doscovery
* [4445e8d](https://github.com/hyperledger/fabric-samples/commit/4445e8d) [FAB-12272](https://jira.hyperledger.org/browse/FAB-12272) Increase MAX_RETRY to 10
* [33d333f](https://github.com/hyperledger/fabric-samples/commit/33d333f) [FAB-12190](https://jira.hyperledger.org/browse/FAB-12190) Update stable version in CI scripts
* [edee638](https://github.com/hyperledger/fabric-samples/commit/edee638) [FAB-12184](https://jira.hyperledger.org/browse/FAB-12184) Prepare fabric-samples for 1.3.0-rc1
* [514d456](https://github.com/hyperledger/fabric-samples/commit/514d456) [FAB-12170](https://jira.hyperledger.org/browse/FAB-12170) Fix dependency check in java chaincode
* [9f80e47](https://github.com/hyperledger/fabric-samples/commit/9f80e47) [FAB-12119](https://jira.hyperledger.org/browse/FAB-12119) Fix groupId in java chaincodes
* [3237229](https://github.com/hyperledger/fabric-samples/commit/3237229) [FAB-12073](https://jira.hyperledger.org/browse/FAB-12073) Fix Org3 peers CouchDB config.
* [eece3d8](https://github.com/hyperledger/fabric-samples/commit/eece3d8) [FAB-12106](https://jira.hyperledger.org/browse/FAB-12106) Update fabric-ca build scripts
* [f62952f](https://github.com/hyperledger/fabric-samples/commit/f62952f) [FABC-131] Change fabric-ca sample to build images
* [4089786](https://github.com/hyperledger/fabric-samples/commit/4089786) [FAB-11867](https://jira.hyperledger.org/browse/FAB-11867) Develop Apps:Sample pt 2 -- application
* [9ee57c6](https://github.com/hyperledger/fabric-samples/commit/9ee57c6) [FAB-11778](https://jira.hyperledger.org/browse/FAB-11778) Upgrade to v1.3.x from v1.2.x in byfn
* [e7a1b76](https://github.com/hyperledger/fabric-samples/commit/e7a1b76) [FAB-9386](https://jira.hyperledger.org/browse/FAB-9386) Remove Marbles sample reference to Fauxton
* [9c6acee](https://github.com/hyperledger/fabric-samples/commit/9c6acee) [FAB-12022](https://jira.hyperledger.org/browse/FAB-12022) Fix CI by increasing couchdb timeout
* [4030ebd](https://github.com/hyperledger/fabric-samples/commit/4030ebd) [FAB-11397](https://jira.hyperledger.org/browse/FAB-11397) Adding java cc
* [d776651](https://github.com/hyperledger/fabric-samples/commit/d776651) [FAB-11723](https://jira.hyperledger.org/browse/FAB-11723) Developing Apps: Sample pt 1 -- contract
* [cbbbc78](https://github.com/hyperledger/fabric-samples/commit/cbbbc78) [FAB-11577](https://jira.hyperledger.org/browse/FAB-11577) Fix balance transfer to install Chaincode
* [bfdc0b6](https://github.com/hyperledger/fabric-samples/commit/bfdc0b6) [FAB-11518](https://jira.hyperledger.org/browse/FAB-11518)
* [5930dfc](https://github.com/hyperledger/fabric-samples/commit/5930dfc) [FAB-11488](https://jira.hyperledger.org/browse/FAB-11488) Update CI script
* [ca6959c](https://github.com/hyperledger/fabric-samples/commit/ca6959c) [FAB-11311](https://jira.hyperledger.org/browse/FAB-11311) Update fabric image version
* [0ca9e6e](https://github.com/hyperledger/fabric-samples/commit/0ca9e6e) FABN-833 Update Jenkinsfile to capture build artifacts
* [a4a15cb](https://github.com/hyperledger/fabric-samples/commit/a4a15cb) [FAB-11220](https://jira.hyperledger.org/browse/FAB-11220) Samples - remove EventHub
* [c4bdc68](https://github.com/hyperledger/fabric-samples/commit/c4bdc68) [FAB-8479](https://jira.hyperledger.org/browse/FAB-8479) Added Endorsement policy
* [6edd320](https://github.com/hyperledger/fabric-samples/commit/6edd320) [FAB-9297](https://jira.hyperledger.org/browse/FAB-9297) fix README links and update bootstrap.sh
* [75e2931](https://github.com/hyperledger/fabric-samples/commit/75e2931) [FAB-10811](https://jira.hyperledger.org/browse/FAB-10811) fabric-ca sample is broken on v1.2

## "v1.2.1"

* [11e4c23](https://github.com/hyperledger/fabric-samples/commit/11e4c23) Update samples to use v2.0 or later dependencies (#161)
* [94beab7](https://github.com/hyperledger/fabric-samples/commit/94beab7) FABN-1531 Use v2.1.0 sdk-node modules
* [8820d2f](https://github.com/hyperledger/fabric-samples/commit/8820d2f) Fix commercial-paper README
* [aa9b577](https://github.com/hyperledger/fabric-samples/commit/aa9b577) Remove TLS enabled switch (#155)
* [381fb46](https://github.com/hyperledger/fabric-samples/commit/381fb46) upgraded abstore golang chaincode to use contract-api package (#154)
* [5e5d2c8](https://github.com/hyperledger/fabric-samples/commit/5e5d2c8) Update java chaincode to be compatible with doc and other implementations (#149)
* [c572c51](https://github.com/hyperledger/fabric-samples/commit/c572c51) Organize and Standardize `ci` Directory Content (#152)
* [aa40963](https://github.com/hyperledger/fabric-samples/commit/aa40963) Perform General Cleanup (#151)
* [da41afa](https://github.com/hyperledger/fabric-samples/commit/da41afa) Remove left over rm -rf command from BYFN (#148)
* [4bb48a9](https://github.com/hyperledger/fabric-samples/commit/4bb48a9) Jenkins no longer used (#145)
* [6f984e1](https://github.com/hyperledger/fabric-samples/commit/6f984e1) Bump acorn from 6.4.0 to 6.4.1 in /fabcar/javascript (#144)
* [b155620](https://github.com/hyperledger/fabric-samples/commit/b155620) Remove redundant invoke command from test network (#142)
* [851933b](https://github.com/hyperledger/fabric-samples/commit/851933b) Add enrollUser files to commercial paper (#140)
* [87600bd](https://github.com/hyperledger/fabric-samples/commit/87600bd) [FAB-17268](https://jira.hyperledger.org/browse/FAB-17268) Move fabcar sample to test network (#103)
* [9397788](https://github.com/hyperledger/fabric-samples/commit/9397788) Wrong groupId on hyperledger fabric dependencies for java-application (#134)
* [92555fb](https://github.com/hyperledger/fabric-samples/commit/92555fb) Update README.md (#133)
* [59c6641](https://github.com/hyperledger/fabric-samples/commit/59c6641) Change Download Location of Fabric Binaries (#143)
* [1f283fc](https://github.com/hyperledger/fabric-samples/commit/1f283fc) init function does not exist on fabcar (#141)
* [defb6bb](https://github.com/hyperledger/fabric-samples/commit/defb6bb) [FAB-17656](https://jira.hyperledger.org/browse/FAB-17656) echo Generating channel.tx (#139)
* [4c7bab0](https://github.com/hyperledger/fabric-samples/commit/4c7bab0) fix: package seletor REGEX (#135)
* [db69c6f](https://github.com/hyperledger/fabric-samples/commit/db69c6f) Add fabcar external service sample (#136)
* [7f5f5e6](https://github.com/hyperledger/fabric-samples/commit/7f5f5e6) [FAB-17504](https://jira.hyperledger.org/browse/FAB-17504) add Organizations.<Org>.OrdererEndpoints and remove Orderer.Addresses (#125)
* [f3fc08d](https://github.com/hyperledger/fabric-samples/commit/f3fc08d) Remove solo and kafka from test net configtx.yaml (#137)
* [e17574d](https://github.com/hyperledger/fabric-samples/commit/e17574d) Add CA's to docker test network (#124)
* [faac18e](https://github.com/hyperledger/fabric-samples/commit/faac18e) [FAB-17461](https://jira.hyperledger.org/browse/FAB-17461) Move off_chain_data sample to test network (#122)
* [121a44a](https://github.com/hyperledger/fabric-samples/commit/121a44a) [FAB-17460](https://jira.hyperledger.org/browse/FAB-17460) Move High Throughput sample to test network (#112)
* [a2f3a66](https://github.com/hyperledger/fabric-samples/commit/a2f3a66) Update docker image version
* [e5b898c](https://github.com/hyperledger/fabric-samples/commit/e5b898c) Revert "first-network/scripts/*: Make Chaincode name configurable (#118)" (#131)
* [9ef61e2](https://github.com/hyperledger/fabric-samples/commit/9ef61e2) first-network/scripts/*: Make Chaincode name configurable (#118)
* [e204ebb](https://github.com/hyperledger/fabric-samples/commit/e204ebb) Remove reference to 2.0 beta (#111)
* [3dbe116](https://github.com/hyperledger/fabric-samples/commit/3dbe116) [FAB-17456](https://jira.hyperledger.org/browse/FAB-17456) fabric-samples read ccp (#117)
* [965ed1f](https://github.com/hyperledger/fabric-samples/commit/965ed1f) [FAB-17498](https://jira.hyperledger.org/browse/FAB-17498) Beta Images removal, test test-network (#121)
* [403019e](https://github.com/hyperledger/fabric-samples/commit/403019e) [FAB-17495](https://jira.hyperledger.org/browse/FAB-17495) Remove Basic Network sample (#120)
* [883ef99](https://github.com/hyperledger/fabric-samples/commit/883ef99) [FAB-17457](https://jira.hyperledger.org/browse/FAB-17457) Script correction (#119)
* [b89ee34](https://github.com/hyperledger/fabric-samples/commit/b89ee34) Update Commercial Paper to v2.0 Lifecycle (#109)
* [4208644](https://github.com/hyperledger/fabric-samples/commit/4208644) [FAB-17478](https://jira.hyperledger.org/browse/FAB-17478) Update commercial paper to use go api v1.0.0 (#115)
* [0df5ed9](https://github.com/hyperledger/fabric-samples/commit/0df5ed9) [FAB-17477](https://jira.hyperledger.org/browse/FAB-17477) Update fabcar to use go api v1.0.0 (#116)
* [571733f](https://github.com/hyperledger/fabric-samples/commit/571733f) [FAB-17447](https://jira.hyperledger.org/browse/FAB-17447) Update to 2.0.0 Libraries
* [67b4ee7](https://github.com/hyperledger/fabric-samples/commit/67b4ee7) Add Org3 bugs in test network (#108)
* [5b93dd0](https://github.com/hyperledger/fabric-samples/commit/5b93dd0) [FAB-17140](https://jira.hyperledger.org/browse/FAB-17140) Add go commercial paper contract (#102)
* [4fe6a25](https://github.com/hyperledger/fabric-samples/commit/4fe6a25) [FABCI-482] Update Nexus URL's to Artifactory (#92)
* [1488fbb](https://github.com/hyperledger/fabric-samples/commit/1488fbb) Add 1.x versions of fabric to blacklisted versions
* [8ca279d](https://github.com/hyperledger/fabric-samples/commit/8ca279d) Add Support for Versioning NodeJS (#106)
* [b3b5267](https://github.com/hyperledger/fabric-samples/commit/b3b5267) [FAB-17243](https://jira.hyperledger.org/browse/FAB-17243) Add support for Fabric CA for Org3 on the (#91)
* [ce41ff7](https://github.com/hyperledger/fabric-samples/commit/ce41ff7) Remove references to vendoring chaincode from your gopath (#96)
* [4235d30](https://github.com/hyperledger/fabric-samples/commit/4235d30) [FAB-17306](https://jira.hyperledger.org/browse/FAB-17306) Fix artifact names in test-network (#97)
* [4c2a0a4](https://github.com/hyperledger/fabric-samples/commit/4c2a0a4) [FAB-16147](https://jira.hyperledger.org/browse/FAB-16147) Update Commercial Paper to work with v2 (#98)
* [6d9fd6f](https://github.com/hyperledger/fabric-samples/commit/6d9fd6f) Remove Gerrit reference
* [a026a4f](https://github.com/hyperledger/fabric-samples/commit/a026a4f) Fixed typo (#90)
* [cdb0e8b](https://github.com/hyperledger/fabric-samples/commit/cdb0e8b) TYPO (#89)
* [94ac8b6](https://github.com/hyperledger/fabric-samples/commit/94ac8b6) Update to use beta levels of modules (#88)
* [d848633](https://github.com/hyperledger/fabric-samples/commit/d848633) [FAB-16844](https://jira.hyperledger.org/browse/FAB-16844) Correct BYFN CC name
* [73267e1](https://github.com/hyperledger/fabric-samples/commit/73267e1) Fix test network bugs for adding org3
* [5d58254](https://github.com/hyperledger/fabric-samples/commit/5d58254) [FAB-17145](https://jira.hyperledger.org/browse/FAB-17145) Add test network to Fabric Samples
* [e9f2957](https://github.com/hyperledger/fabric-samples/commit/e9f2957) [FAB-17062](https://jira.hyperledger.org/browse/FAB-17062) Fix typos in Commercial Paper readme
* [36694d0](https://github.com/hyperledger/fabric-samples/commit/36694d0) [FAB-17121](https://jira.hyperledger.org/browse/FAB-17121) Use new bootstrap config in orderer
* [429f087](https://github.com/hyperledger/fabric-samples/commit/429f087) update fabcar go to new programming model
* [1467086](https://github.com/hyperledger/fabric-samples/commit/1467086) Bump eslint-utils
* [33f349a](https://github.com/hyperledger/fabric-samples/commit/33f349a) Remove Stalebot
* [6af43bf](https://github.com/hyperledger/fabric-samples/commit/6af43bf) Change stalebot settings
* [4880401](https://github.com/hyperledger/fabric-samples/commit/4880401) [FAB-16284](https://jira.hyperledger.org/browse/FAB-16284) Remove E2E file and -f option from BYFN
* [fe96f60](https://github.com/hyperledger/fabric-samples/commit/fe96f60) [FAB-16850](https://jira.hyperledger.org/browse/FAB-16850) Set up CI with Azure Pipelines
* [81aabf4](https://github.com/hyperledger/fabric-samples/commit/81aabf4) [FAB-16849](https://jira.hyperledger.org/browse/FAB-16849) Various updates for Java version of FabCar
* [a42b858](https://github.com/hyperledger/fabric-samples/commit/a42b858) Update FabCar to reflect wallet API changes
* [890f9ea](https://github.com/hyperledger/fabric-samples/commit/890f9ea) [FAB-16713](https://jira.hyperledger.org/browse/FAB-16713) Fix npm audit warnings
* [e48e804](https://github.com/hyperledger/fabric-samples/commit/e48e804) [FAB-16776](https://jira.hyperledger.org/browse/FAB-16776) Move BYFN up to V2_0 capabilities
* [7b65a25](https://github.com/hyperledger/fabric-samples/commit/7b65a25) [IN-68] Add default GitHub SECURITY policy
* [408e0e8](https://github.com/hyperledger/fabric-samples/commit/408e0e8) [FAB-16619](https://jira.hyperledger.org/browse/FAB-16619) Fix the policy warning
* [670d446](https://github.com/hyperledger/fabric-samples/commit/670d446) [FAB-16668](https://jira.hyperledger.org/browse/FAB-16668) fabcar chaincode modify console output
* [f2939e2](https://github.com/hyperledger/fabric-samples/commit/f2939e2) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for Commercial Paper sample
* [3d19014](https://github.com/hyperledger/fabric-samples/commit/3d19014) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for FabCar sample
* [e2b7cb7](https://github.com/hyperledger/fabric-samples/commit/e2b7cb7) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for abstore sample
* [db48612](https://github.com/hyperledger/fabric-samples/commit/db48612) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Increase chaincode execute timeout
* [521a7ff](https://github.com/hyperledger/fabric-samples/commit/521a7ff) [FAB-16607](https://jira.hyperledger.org/browse/FAB-16607) Update FabCar to reflect CC updates
* [c13a5ec](https://github.com/hyperledger/fabric-samples/commit/c13a5ec) [FAB-16528](https://jira.hyperledger.org/browse/FAB-16528) marbles private chaincode sync up
* [3fad853](https://github.com/hyperledger/fabric-samples/commit/3fad853) [FAB-16528](https://jira.hyperledger.org/browse/FAB-16528) marbles private chaincode sync up
* [8b9b82f](https://github.com/hyperledger/fabric-samples/commit/8b9b82f) [FAB-16489](https://jira.hyperledger.org/browse/FAB-16489) Add CODEOWNERS
* [a6ce915](https://github.com/hyperledger/fabric-samples/commit/a6ce915) [FAB-16487](https://jira.hyperledger.org/browse/FAB-16487) Update eslint
* [48082cf](https://github.com/hyperledger/fabric-samples/commit/48082cf) [FAB-16362](https://jira.hyperledger.org/browse/FAB-16362) adding chaincode excution comments
* [1d379f3](https://github.com/hyperledger/fabric-samples/commit/1d379f3) [FAB-16474](https://jira.hyperledger.org/browse/FAB-16474) marbles02 chaincode error
* [18712ca](https://github.com/hyperledger/fabric-samples/commit/18712ca) [FAB-16133](https://jira.hyperledger.org/browse/FAB-16133) Remove Solo consensus from BYFN
* [91c720a](https://github.com/hyperledger/fabric-samples/commit/91c720a) [FAB-16390](https://jira.hyperledger.org/browse/FAB-16390) Added filter for invalid transactions
* [1d3e267](https://github.com/hyperledger/fabric-samples/commit/1d3e267) Redirect samples to fabric-{chaincode,protos}-go
* [398a5b1](https://github.com/hyperledger/fabric-samples/commit/398a5b1) [FABCI-394] Remove AnsiColor Wrapper
* [ce154e0](https://github.com/hyperledger/fabric-samples/commit/ce154e0) [FAB-16310](https://jira.hyperledger.org/browse/FAB-16310) Vendor Go dependencies in all samples
* [6ea7c71](https://github.com/hyperledger/fabric-samples/commit/6ea7c71) [FAB-16285](https://jira.hyperledger.org/browse/FAB-16285) Update blacklisted versions in BYFN
* [86cd831](https://github.com/hyperledger/fabric-samples/commit/86cd831) [FAB-16284](https://jira.hyperledger.org/browse/FAB-16284) Remove E2E file and -f option from BYFN
* [0063abe](https://github.com/hyperledger/fabric-samples/commit/0063abe) Update stale script name in interest rate swaps
* [3907507](https://github.com/hyperledger/fabric-samples/commit/3907507) [FAB-16277](https://jira.hyperledger.org/browse/FAB-16277) Update BYFN w/ Raft ports in Docker network
* [33b0065](https://github.com/hyperledger/fabric-samples/commit/33b0065) [FAB-14813](https://jira.hyperledger.org/browse/FAB-14813) Channel event sample in fabric-samples
* [b62d5bd](https://github.com/hyperledger/fabric-samples/commit/b62d5bd) [FAB-16132](https://jira.hyperledger.org/browse/FAB-16132) Remove Kafka consensus from BYFN
* [9b14525](https://github.com/hyperledger/fabric-samples/commit/9b14525) [FAB-15213](https://jira.hyperledger.org/browse/FAB-15213) Update Commercial Paper for Java
* [4158877](https://github.com/hyperledger/fabric-samples/commit/4158877) [FAB-16232](https://jira.hyperledger.org/browse/FAB-16232) Remove FabToken sample
* [b6380cc](https://github.com/hyperledger/fabric-samples/commit/b6380cc) [FAB-16198](https://jira.hyperledger.org/browse/FAB-16198) Run "go mod vendor" for FabCar Go contract
* [639848a](https://github.com/hyperledger/fabric-samples/commit/639848a) [FAB-16197](https://jira.hyperledger.org/browse/FAB-16197) Add connection profiles to .gitignore
* [3996db5](https://github.com/hyperledger/fabric-samples/commit/3996db5) [FAB-12219](https://jira.hyperledger.org/browse/FAB-12219) abstore node -> javascript
* [14ac271](https://github.com/hyperledger/fabric-samples/commit/14ac271) [FAB-12219](https://jira.hyperledger.org/browse/FAB-12219) marbles02 node -> javascript
* [13f16e5](https://github.com/hyperledger/fabric-samples/commit/13f16e5) [FGJ-4] CI tests for FabCar Java sample
* [171a7d2](https://github.com/hyperledger/fabric-samples/commit/171a7d2) FGJ-4 Fabcar sample
* [868f9d0](https://github.com/hyperledger/fabric-samples/commit/868f9d0) [FAB-15625](https://jira.hyperledger.org/browse/FAB-15625) Add UT for Simple Asset Chaincode
* [597d150](https://github.com/hyperledger/fabric-samples/commit/597d150) [FAB-14532](https://jira.hyperledger.org/browse/FAB-14532) Remove LL FabCar sample
* [583ff8f](https://github.com/hyperledger/fabric-samples/commit/583ff8f) Use renamed CheckCommitReadiness function
* [750f937](https://github.com/hyperledger/fabric-samples/commit/750f937) [FAB-15213](https://jira.hyperledger.org/browse/FAB-15213) Add Java FabCar sample contract
* [abbda95](https://github.com/hyperledger/fabric-samples/commit/abbda95) [FAB-15897](https://jira.hyperledger.org/browse/FAB-15897) Improve FabCar test logging
* [dd8150a](https://github.com/hyperledger/fabric-samples/commit/dd8150a) [FAB-15104](https://jira.hyperledger.org/browse/FAB-15104) Remove versions from fabric-samples readme
* [1387aa8](https://github.com/hyperledger/fabric-samples/commit/1387aa8) [FAB-15927](https://jira.hyperledger.org/browse/FAB-15927)  Better expression for golang
* [61c33d3](https://github.com/hyperledger/fabric-samples/commit/61c33d3) [FAB-15973](https://jira.hyperledger.org/browse/FAB-15973) use --output json on simulatecommit
* [8bbdd0f](https://github.com/hyperledger/fabric-samples/commit/8bbdd0f) [FAB-15716](https://jira.hyperledger.org/browse/FAB-15716) Fix instructions for dev-mode
* [0254d67](https://github.com/hyperledger/fabric-samples/commit/0254d67) QueryApprovalStatus -> SimulateCommitChaincodeDef
* [c57d67c](https://github.com/hyperledger/fabric-samples/commit/c57d67c) [FAB-15782](https://jira.hyperledger.org/browse/FAB-15782) Sample Go CC should include deps
* [6ba5a19](https://github.com/hyperledger/fabric-samples/commit/6ba5a19) Update to Go 1.12.5 in ci.properties
* [1774a25](https://github.com/hyperledger/fabric-samples/commit/1774a25) [FAB-15723](https://jira.hyperledger.org/browse/FAB-15723) Fix script and instruction with ccenv
* [6ae711c](https://github.com/hyperledger/fabric-samples/commit/6ae711c) [FAB-15717](https://jira.hyperledger.org/browse/FAB-15717) fix Error Unexpected end of JSON input
* [5be56d3](https://github.com/hyperledger/fabric-samples/commit/5be56d3) [FAB-15104](https://jira.hyperledger.org/browse/FAB-15104) Remove scripts/bootstrap.sh
* [779f8f3](https://github.com/hyperledger/fabric-samples/commit/779f8f3) [FAB-15649](https://jira.hyperledger.org/browse/FAB-15649)Fix Fabcar to install Chaincode on all peers
* [7c5f5d3](https://github.com/hyperledger/fabric-samples/commit/7c5f5d3) [FAB-15199](https://jira.hyperledger.org/browse/FAB-15199) Update interest rate sample
* [f0dca20](https://github.com/hyperledger/fabric-samples/commit/f0dca20) [FAB-14532](https://jira.hyperledger.org/browse/FAB-14532) Remove LL FabCar sample
* [1ed1a10](https://github.com/hyperledger/fabric-samples/commit/1ed1a10) [FAB-15573](https://jira.hyperledger.org/browse/FAB-15573) Fix typo in fabric-samples-ci.md
* [2e7fec9](https://github.com/hyperledger/fabric-samples/commit/2e7fec9) [FAB-15601](https://jira.hyperledger.org/browse/FAB-15601) BYFN: Fix MAX_RETRY for couchdb
* [1e9e4c4](https://github.com/hyperledger/fabric-samples/commit/1e9e4c4) [FAB-9329](https://jira.hyperledger.org/browse/FAB-9329) Remove the unused variable in BYFN/EYFN
* [964c09f](https://github.com/hyperledger/fabric-samples/commit/964c09f) [FAB-15601](https://jira.hyperledger.org/browse/FAB-15601) BYFN: Fix MAX_RETRY for couchdb
* [41dca99](https://github.com/hyperledger/fabric-samples/commit/41dca99) [FAB-15127](https://jira.hyperledger.org/browse/FAB-15127) Update high throughput sample
* [3fe014a](https://github.com/hyperledger/fabric-samples/commit/3fe014a) Use official CouchDB image
* [f2d0fa0](https://github.com/hyperledger/fabric-samples/commit/f2d0fa0) [FAB-14487](https://jira.hyperledger.org/browse/FAB-14487) Make FabCar use BYFN, not basic-network
* [e9c3649](https://github.com/hyperledger/fabric-samples/commit/e9c3649) [FAB-15276](https://jira.hyperledger.org/browse/FAB-15276) Fix license statements
* [fbe4036](https://github.com/hyperledger/fabric-samples/commit/fbe4036) [FAB-14486](https://jira.hyperledger.org/browse/FAB-14486) Extend BYFN to opt skip chaincode deploy
* [0c4141f](https://github.com/hyperledger/fabric-samples/commit/0c4141f) [FAB-14485](https://jira.hyperledger.org/browse/FAB-14485) Extend BYFN to opt inc cert authorities
* [529b83b](https://github.com/hyperledger/fabric-samples/commit/529b83b) [FAB-14330](https://jira.hyperledger.org/browse/FAB-14330) Add connection profiles for BYFN and EYFN
* [2c21c83](https://github.com/hyperledger/fabric-samples/commit/2c21c83) [FABN-1184] Update fabtoken/README.md
* [5056a23](https://github.com/hyperledger/fabric-samples/commit/5056a23) [FABN-1184] Add CI script for fabtoken sample app
* [5d6db95](https://github.com/hyperledger/fabric-samples/commit/5d6db95) Update maintainers for fabric-samples
* [f527815](https://github.com/hyperledger/fabric-samples/commit/f527815) [FAB-15119](https://jira.hyperledger.org/browse/FAB-15119) Fix BYFN with Java chaincode
* [8245252](https://github.com/hyperledger/fabric-samples/commit/8245252) [FABN-1184] Implement fabtoken sample app
* [1bd1c2f](https://github.com/hyperledger/fabric-samples/commit/1bd1c2f) FABCI-284 Update CI Pipeline script
* [c24abf9](https://github.com/hyperledger/fabric-samples/commit/c24abf9) [FAB-15022](https://jira.hyperledger.org/browse/FAB-15022) Basic-network support for new lifecycle
* [b64fd45](https://github.com/hyperledger/fabric-samples/commit/b64fd45) [FAB-15051](https://jira.hyperledger.org/browse/FAB-15051) delStandard() function for high-throughput
* [3e68a7e](https://github.com/hyperledger/fabric-samples/commit/3e68a7e) [FAB-14784](https://jira.hyperledger.org/browse/FAB-14784) Remove balance-transfer
* [eb3fe08](https://github.com/hyperledger/fabric-samples/commit/eb3fe08) [FAB-14779](https://jira.hyperledger.org/browse/FAB-14779) QueryApprovalStatus step in byfn
* [2777429](https://github.com/hyperledger/fabric-samples/commit/2777429) [FAB-14711](https://jira.hyperledger.org/browse/FAB-14711) update byfn with new lifecycle
* [aec3389](https://github.com/hyperledger/fabric-samples/commit/aec3389) [FAB-12215](https://jira.hyperledger.org/browse/FAB-12215)WYFA:Remove chainId in tx proposal request
* [b5d5026](https://github.com/hyperledger/fabric-samples/commit/b5d5026) [FAB-14633](https://jira.hyperledger.org/browse/FAB-14633) Remove apt-get from eyfn.sh
* [efaadd3](https://github.com/hyperledger/fabric-samples/commit/efaadd3) [FAB-14531](https://jira.hyperledger.org/browse/FAB-14531) BYFN Raft with 5 nodes
* [d63047c](https://github.com/hyperledger/fabric-samples/commit/d63047c) [FAB-14444](https://jira.hyperledger.org/browse/FAB-14444)
* [7e3d428](https://github.com/hyperledger/fabric-samples/commit/7e3d428) [FAB-14369](https://jira.hyperledger.org/browse/FAB-14369)Fix dev mode failing to build Chaincode
* [420ba23](https://github.com/hyperledger/fabric-samples/commit/420ba23) [FAB-12762](https://jira.hyperledger.org/browse/FAB-12762) Add etcd/raft consensus option to BYFN
* [2b68c80](https://github.com/hyperledger/fabric-samples/commit/2b68c80) [FAB-14317](https://jira.hyperledger.org/browse/FAB-14317) Add default policies to org3
* [f942010](https://github.com/hyperledger/fabric-samples/commit/f942010) [FAB-14268](https://jira.hyperledger.org/browse/FAB-14268) Make BYFN/EYFN ports match external ports
* [4e2ce23](https://github.com/hyperledger/fabric-samples/commit/4e2ce23) [FAB-14271](https://jira.hyperledger.org/browse/FAB-14271) Add channel policies to channel config
* [f26477c](https://github.com/hyperledger/fabric-samples/commit/f26477c) [FAB-11796](https://jira.hyperledger.org/browse/FAB-11796)high-throughput:Remove unnecessary prunesafe
* [137327a](https://github.com/hyperledger/fabric-samples/commit/137327a) [FAB-14162](https://jira.hyperledger.org/browse/FAB-14162) Pin fabric-samples in master to "unstable"
* [6007c09](https://github.com/hyperledger/fabric-samples/commit/6007c09) [FAB-13862](https://jira.hyperledger.org/browse/FAB-13862) Rename example02 ABstore
* [94cb603](https://github.com/hyperledger/fabric-samples/commit/94cb603) [FAB-13933](https://jira.hyperledger.org/browse/FAB-13933) Fix misspellings
* [a8a5539](https://github.com/hyperledger/fabric-samples/commit/a8a5539) Fix doc link Fix variable error
* [b0cda61](https://github.com/hyperledger/fabric-samples/commit/b0cda61) [FAB-13769](https://jira.hyperledger.org/browse/FAB-13769) Add UT code to ABAC sample Chaincode
* [c7438e1](https://github.com/hyperledger/fabric-samples/commit/c7438e1) [FAB-13668](https://jira.hyperledger.org/browse/FAB-13668) BYFN's container volume mapping is bad
* [e48b2de](https://github.com/hyperledger/fabric-samples/commit/e48b2de) [FAB-13489](https://jira.hyperledger.org/browse/FAB-13489) fabric-samples add error msg
* [8a458b5](https://github.com/hyperledger/fabric-samples/commit/8a458b5) [FAB-12056](https://jira.hyperledger.org/browse/FAB-12056) Private marbles cc use transient data
* [6269941](https://github.com/hyperledger/fabric-samples/commit/6269941) Correct broken link
* [461b6ab](https://github.com/hyperledger/fabric-samples/commit/461b6ab) FABC-781 Remove fabric-ca sample
* [e9b9477](https://github.com/hyperledger/fabric-samples/commit/e9b9477) [FAB-13372](https://jira.hyperledger.org/browse/FAB-13372) Fabric-Samples return error msg
* [e3da220](https://github.com/hyperledger/fabric-samples/commit/e3da220) [FAB-13433](https://jira.hyperledger.org/browse/FAB-13433) - Update Jenkinsfile configuration
* [33db64e](https://github.com/hyperledger/fabric-samples/commit/33db64e) Configure Stale ProBot
* [5cd277f](https://github.com/hyperledger/fabric-samples/commit/5cd277f) [FAB-11951](https://jira.hyperledger.org/browse/FAB-11951) Interest-rate swap example for SBE
* [9567985](https://github.com/hyperledger/fabric-samples/commit/9567985) [FAB-13407](https://jira.hyperledger.org/browse/FAB-13407) Align fabric-samples with 1.4.0-rc2 release
* [c7572aa](https://github.com/hyperledger/fabric-samples/commit/c7572aa) [FAB-13305](https://jira.hyperledger.org/browse/FAB-13305) Update scripts to pull latest artifacts
* [ab46e35](https://github.com/hyperledger/fabric-samples/commit/ab46e35) [FAB-13283](https://jira.hyperledger.org/browse/FAB-13283) Update sample code for commercial paper
* [f677821](https://github.com/hyperledger/fabric-samples/commit/f677821) [FAB-13232](https://jira.hyperledger.org/browse/FAB-13232) fix peer node start command
* [6a7472e](https://github.com/hyperledger/fabric-samples/commit/6a7472e) [FAB-13126](https://jira.hyperledger.org/browse/FAB-13126) Align fabric-samples with 1.4.0-rc1 release
* [445ccbc](https://github.com/hyperledger/fabric-samples/commit/445ccbc) [FAB-12880](https://jira.hyperledger.org/browse/FAB-12880) Move old prog model samples for FabCar
* [5be62b5](https://github.com/hyperledger/fabric-samples/commit/5be62b5) [FAB-13207](https://jira.hyperledger.org/browse/FAB-13207) Remove incorrect discovery options
* [fdbd92d](https://github.com/hyperledger/fabric-samples/commit/fdbd92d) [FAB-13206](https://jira.hyperledger.org/browse/FAB-13206) Remove dependencies on fabric-client
* [eff0046](https://github.com/hyperledger/fabric-samples/commit/eff0046) [FAB-12877](https://jira.hyperledger.org/browse/FAB-12877) Add fabcar app using new prog model (JS)
* [c184196](https://github.com/hyperledger/fabric-samples/commit/c184196) [FAB-12878](https://jira.hyperledger.org/browse/FAB-12878) Add fabcar app using new prog model (TS)
* [e1a39e6](https://github.com/hyperledger/fabric-samples/commit/e1a39e6) [FAB-12724](https://jira.hyperledger.org/browse/FAB-12724) Upgrade from 1.3.x to 1.4.0
* [c21bbba](https://github.com/hyperledger/fabric-samples/commit/c21bbba) Update samples to use new logging env variables
* [7ad9f19](https://github.com/hyperledger/fabric-samples/commit/7ad9f19) [FAB-13011](https://jira.hyperledger.org/browse/FAB-13011) add kafka consensus type to byfn sample
* [33f064f](https://github.com/hyperledger/fabric-samples/commit/33f064f) [FAB-13170](https://jira.hyperledger.org/browse/FAB-13170) Add memberOnlyRead to marbles sample
* [928b72b](https://github.com/hyperledger/fabric-samples/commit/928b72b) [FAB-12875](https://jira.hyperledger.org/browse/FAB-12875) Add automated tests for fabcar sample
* [5c087f1](https://github.com/hyperledger/fabric-samples/commit/5c087f1) [FAB-13046](https://jira.hyperledger.org/browse/FAB-13046) Update TypeScript contract dependencies
* [3748983](https://github.com/hyperledger/fabric-samples/commit/3748983) [FAB-12879](https://jira.hyperledger.org/browse/FAB-12879) Update fabcar script for new contracts
* [4fb3b57](https://github.com/hyperledger/fabric-samples/commit/4fb3b57) [FAB-12852](https://jira.hyperledger.org/browse/FAB-12852) Add fabcar contract w/ new prog model (TS)
* [9facb42](https://github.com/hyperledger/fabric-samples/commit/9facb42) [FAB-12851](https://jira.hyperledger.org/browse/FAB-12851) Add fabcar contract w/ new prog model (JS)
* [e67fcf1](https://github.com/hyperledger/fabric-samples/commit/e67fcf1) [FAB-12322](https://jira.hyperledger.org/browse/FAB-12322) Update commercial-paper sample
* [fd6e2c4](https://github.com/hyperledger/fabric-samples/commit/fd6e2c4) [FAB-12703](https://jira.hyperledger.org/browse/FAB-12703) Fix misspelling "lauches"
* [c05f172](https://github.com/hyperledger/fabric-samples/commit/c05f172) [FAB-12608](https://jira.hyperledger.org/browse/FAB-12608) Update pipeline script
* [286861e](https://github.com/hyperledger/fabric-samples/commit/286861e) [FAB-12371](https://jira.hyperledger.org/browse/FAB-12371)Fix the abac sample to use new cid package
* [24c5e47](https://github.com/hyperledger/fabric-samples/commit/24c5e47) [FAB-12026](https://jira.hyperledger.org/browse/FAB-12026) pagination samples for node marbles02
* [6dc5ce5](https://github.com/hyperledger/fabric-samples/commit/6dc5ce5) [FAB-12587](https://jira.hyperledger.org/browse/FAB-12587) Fix for Query Block by block hash API
* [df311ce](https://github.com/hyperledger/fabric-samples/commit/df311ce) [FAB-12173](https://jira.hyperledger.org/browse/FAB-12173) balance-transfer: Update anchor peers
* [c925148](https://github.com/hyperledger/fabric-samples/commit/c925148) [FAB-12415](https://jira.hyperledger.org/browse/FAB-12415) samples for 1.3.0 (master cleanup)
* [3a12c60](https://github.com/hyperledger/fabric-samples/commit/3a12c60) [FAB-12275](https://jira.hyperledger.org/browse/FAB-12275) Fix the warn in creating genesis block
* [c6f6324](https://github.com/hyperledger/fabric-samples/commit/c6f6324) [FAB-12257](https://jira.hyperledger.org/browse/FAB-12257) allow balance-transfer for doscovery
* [4445e8d](https://github.com/hyperledger/fabric-samples/commit/4445e8d) [FAB-12272](https://jira.hyperledger.org/browse/FAB-12272) Increase MAX_RETRY to 10
* [33d333f](https://github.com/hyperledger/fabric-samples/commit/33d333f) [FAB-12190](https://jira.hyperledger.org/browse/FAB-12190) Update stable version in CI scripts
* [edee638](https://github.com/hyperledger/fabric-samples/commit/edee638) [FAB-12184](https://jira.hyperledger.org/browse/FAB-12184) Prepare fabric-samples for 1.3.0-rc1
* [514d456](https://github.com/hyperledger/fabric-samples/commit/514d456) [FAB-12170](https://jira.hyperledger.org/browse/FAB-12170) Fix dependency check in java chaincode
* [9f80e47](https://github.com/hyperledger/fabric-samples/commit/9f80e47) [FAB-12119](https://jira.hyperledger.org/browse/FAB-12119) Fix groupId in java chaincodes
* [3237229](https://github.com/hyperledger/fabric-samples/commit/3237229) [FAB-12073](https://jira.hyperledger.org/browse/FAB-12073) Fix Org3 peers CouchDB config.
* [eece3d8](https://github.com/hyperledger/fabric-samples/commit/eece3d8) [FAB-12106](https://jira.hyperledger.org/browse/FAB-12106) Update fabric-ca build scripts
* [f62952f](https://github.com/hyperledger/fabric-samples/commit/f62952f) [FABC-131] Change fabric-ca sample to build images
* [4089786](https://github.com/hyperledger/fabric-samples/commit/4089786) [FAB-11867](https://jira.hyperledger.org/browse/FAB-11867) Develop Apps:Sample pt 2 -- application
* [9ee57c6](https://github.com/hyperledger/fabric-samples/commit/9ee57c6) [FAB-11778](https://jira.hyperledger.org/browse/FAB-11778) Upgrade to v1.3.x from v1.2.x in byfn
* [e7a1b76](https://github.com/hyperledger/fabric-samples/commit/e7a1b76) [FAB-9386](https://jira.hyperledger.org/browse/FAB-9386) Remove Marbles sample reference to Fauxton
* [9c6acee](https://github.com/hyperledger/fabric-samples/commit/9c6acee) [FAB-12022](https://jira.hyperledger.org/browse/FAB-12022) Fix CI by increasing couchdb timeout
* [4030ebd](https://github.com/hyperledger/fabric-samples/commit/4030ebd) [FAB-11397](https://jira.hyperledger.org/browse/FAB-11397) Adding java cc
* [d776651](https://github.com/hyperledger/fabric-samples/commit/d776651) [FAB-11723](https://jira.hyperledger.org/browse/FAB-11723) Developing Apps: Sample pt 1 -- contract
* [cbbbc78](https://github.com/hyperledger/fabric-samples/commit/cbbbc78) [FAB-11577](https://jira.hyperledger.org/browse/FAB-11577) Fix balance transfer to install Chaincode
* [bfdc0b6](https://github.com/hyperledger/fabric-samples/commit/bfdc0b6) [FAB-11518](https://jira.hyperledger.org/browse/FAB-11518)
* [5930dfc](https://github.com/hyperledger/fabric-samples/commit/5930dfc) [FAB-11488](https://jira.hyperledger.org/browse/FAB-11488) Update CI script
* [ca6959c](https://github.com/hyperledger/fabric-samples/commit/ca6959c) [FAB-11311](https://jira.hyperledger.org/browse/FAB-11311) Update fabric image version
* [0ca9e6e](https://github.com/hyperledger/fabric-samples/commit/0ca9e6e) FABN-833 Update Jenkinsfile to capture build artifacts
* [a4a15cb](https://github.com/hyperledger/fabric-samples/commit/a4a15cb) [FAB-11220](https://jira.hyperledger.org/browse/FAB-11220) Samples - remove EventHub
* [c4bdc68](https://github.com/hyperledger/fabric-samples/commit/c4bdc68) [FAB-8479](https://jira.hyperledger.org/browse/FAB-8479) Added Endorsement policy
* [6edd320](https://github.com/hyperledger/fabric-samples/commit/6edd320) [FAB-9297](https://jira.hyperledger.org/browse/FAB-9297) fix README links and update bootstrap.sh
* [75e2931](https://github.com/hyperledger/fabric-samples/commit/75e2931) [FAB-10811](https://jira.hyperledger.org/browse/FAB-10811) fabric-ca sample is broken on v1.2

## "v1.2.0"

* [11e4c23](https://github.com/hyperledger/fabric-samples/commit/11e4c23) Update samples to use v2.0 or later dependencies (#161)
* [94beab7](https://github.com/hyperledger/fabric-samples/commit/94beab7) FABN-1531 Use v2.1.0 sdk-node modules
* [8820d2f](https://github.com/hyperledger/fabric-samples/commit/8820d2f) Fix commercial-paper README
* [aa9b577](https://github.com/hyperledger/fabric-samples/commit/aa9b577) Remove TLS enabled switch (#155)
* [381fb46](https://github.com/hyperledger/fabric-samples/commit/381fb46) upgraded abstore golang chaincode to use contract-api package (#154)
* [5e5d2c8](https://github.com/hyperledger/fabric-samples/commit/5e5d2c8) Update java chaincode to be compatible with doc and other implementations (#149)
* [c572c51](https://github.com/hyperledger/fabric-samples/commit/c572c51) Organize and Standardize `ci` Directory Content (#152)
* [aa40963](https://github.com/hyperledger/fabric-samples/commit/aa40963) Perform General Cleanup (#151)
* [da41afa](https://github.com/hyperledger/fabric-samples/commit/da41afa) Remove left over rm -rf command from BYFN (#148)
* [4bb48a9](https://github.com/hyperledger/fabric-samples/commit/4bb48a9) Jenkins no longer used (#145)
* [6f984e1](https://github.com/hyperledger/fabric-samples/commit/6f984e1) Bump acorn from 6.4.0 to 6.4.1 in /fabcar/javascript (#144)
* [b155620](https://github.com/hyperledger/fabric-samples/commit/b155620) Remove redundant invoke command from test network (#142)
* [851933b](https://github.com/hyperledger/fabric-samples/commit/851933b) Add enrollUser files to commercial paper (#140)
* [87600bd](https://github.com/hyperledger/fabric-samples/commit/87600bd) [FAB-17268](https://jira.hyperledger.org/browse/FAB-17268) Move fabcar sample to test network (#103)
* [9397788](https://github.com/hyperledger/fabric-samples/commit/9397788) Wrong groupId on hyperledger fabric dependencies for java-application (#134)
* [92555fb](https://github.com/hyperledger/fabric-samples/commit/92555fb) Update README.md (#133)
* [59c6641](https://github.com/hyperledger/fabric-samples/commit/59c6641) Change Download Location of Fabric Binaries (#143)
* [1f283fc](https://github.com/hyperledger/fabric-samples/commit/1f283fc) init function does not exist on fabcar (#141)
* [defb6bb](https://github.com/hyperledger/fabric-samples/commit/defb6bb) [FAB-17656](https://jira.hyperledger.org/browse/FAB-17656) echo Generating channel.tx (#139)
* [4c7bab0](https://github.com/hyperledger/fabric-samples/commit/4c7bab0) fix: package seletor REGEX (#135)
* [db69c6f](https://github.com/hyperledger/fabric-samples/commit/db69c6f) Add fabcar external service sample (#136)
* [7f5f5e6](https://github.com/hyperledger/fabric-samples/commit/7f5f5e6) [FAB-17504](https://jira.hyperledger.org/browse/FAB-17504) add Organizations.<Org>.OrdererEndpoints and remove Orderer.Addresses (#125)
* [f3fc08d](https://github.com/hyperledger/fabric-samples/commit/f3fc08d) Remove solo and kafka from test net configtx.yaml (#137)
* [e17574d](https://github.com/hyperledger/fabric-samples/commit/e17574d) Add CA's to docker test network (#124)
* [faac18e](https://github.com/hyperledger/fabric-samples/commit/faac18e) [FAB-17461](https://jira.hyperledger.org/browse/FAB-17461) Move off_chain_data sample to test network (#122)
* [121a44a](https://github.com/hyperledger/fabric-samples/commit/121a44a) [FAB-17460](https://jira.hyperledger.org/browse/FAB-17460) Move High Throughput sample to test network (#112)
* [a2f3a66](https://github.com/hyperledger/fabric-samples/commit/a2f3a66) Update docker image version
* [e5b898c](https://github.com/hyperledger/fabric-samples/commit/e5b898c) Revert "first-network/scripts/*: Make Chaincode name configurable (#118)" (#131)
* [9ef61e2](https://github.com/hyperledger/fabric-samples/commit/9ef61e2) first-network/scripts/*: Make Chaincode name configurable (#118)
* [e204ebb](https://github.com/hyperledger/fabric-samples/commit/e204ebb) Remove reference to 2.0 beta (#111)
* [3dbe116](https://github.com/hyperledger/fabric-samples/commit/3dbe116) [FAB-17456](https://jira.hyperledger.org/browse/FAB-17456) fabric-samples read ccp (#117)
* [965ed1f](https://github.com/hyperledger/fabric-samples/commit/965ed1f) [FAB-17498](https://jira.hyperledger.org/browse/FAB-17498) Beta Images removal, test test-network (#121)
* [403019e](https://github.com/hyperledger/fabric-samples/commit/403019e) [FAB-17495](https://jira.hyperledger.org/browse/FAB-17495) Remove Basic Network sample (#120)
* [883ef99](https://github.com/hyperledger/fabric-samples/commit/883ef99) [FAB-17457](https://jira.hyperledger.org/browse/FAB-17457) Script correction (#119)
* [b89ee34](https://github.com/hyperledger/fabric-samples/commit/b89ee34) Update Commercial Paper to v2.0 Lifecycle (#109)
* [4208644](https://github.com/hyperledger/fabric-samples/commit/4208644) [FAB-17478](https://jira.hyperledger.org/browse/FAB-17478) Update commercial paper to use go api v1.0.0 (#115)
* [0df5ed9](https://github.com/hyperledger/fabric-samples/commit/0df5ed9) [FAB-17477](https://jira.hyperledger.org/browse/FAB-17477) Update fabcar to use go api v1.0.0 (#116)
* [571733f](https://github.com/hyperledger/fabric-samples/commit/571733f) [FAB-17447](https://jira.hyperledger.org/browse/FAB-17447) Update to 2.0.0 Libraries
* [67b4ee7](https://github.com/hyperledger/fabric-samples/commit/67b4ee7) Add Org3 bugs in test network (#108)
* [5b93dd0](https://github.com/hyperledger/fabric-samples/commit/5b93dd0) [FAB-17140](https://jira.hyperledger.org/browse/FAB-17140) Add go commercial paper contract (#102)
* [4fe6a25](https://github.com/hyperledger/fabric-samples/commit/4fe6a25) [FABCI-482] Update Nexus URL's to Artifactory (#92)
* [1488fbb](https://github.com/hyperledger/fabric-samples/commit/1488fbb) Add 1.x versions of fabric to blacklisted versions
* [8ca279d](https://github.com/hyperledger/fabric-samples/commit/8ca279d) Add Support for Versioning NodeJS (#106)
* [b3b5267](https://github.com/hyperledger/fabric-samples/commit/b3b5267) [FAB-17243](https://jira.hyperledger.org/browse/FAB-17243) Add support for Fabric CA for Org3 on the (#91)
* [ce41ff7](https://github.com/hyperledger/fabric-samples/commit/ce41ff7) Remove references to vendoring chaincode from your gopath (#96)
* [4235d30](https://github.com/hyperledger/fabric-samples/commit/4235d30) [FAB-17306](https://jira.hyperledger.org/browse/FAB-17306) Fix artifact names in test-network (#97)
* [4c2a0a4](https://github.com/hyperledger/fabric-samples/commit/4c2a0a4) [FAB-16147](https://jira.hyperledger.org/browse/FAB-16147) Update Commercial Paper to work with v2 (#98)
* [6d9fd6f](https://github.com/hyperledger/fabric-samples/commit/6d9fd6f) Remove Gerrit reference
* [a026a4f](https://github.com/hyperledger/fabric-samples/commit/a026a4f) Fixed typo (#90)
* [cdb0e8b](https://github.com/hyperledger/fabric-samples/commit/cdb0e8b) TYPO (#89)
* [94ac8b6](https://github.com/hyperledger/fabric-samples/commit/94ac8b6) Update to use beta levels of modules (#88)
* [d848633](https://github.com/hyperledger/fabric-samples/commit/d848633) [FAB-16844](https://jira.hyperledger.org/browse/FAB-16844) Correct BYFN CC name
* [73267e1](https://github.com/hyperledger/fabric-samples/commit/73267e1) Fix test network bugs for adding org3
* [5d58254](https://github.com/hyperledger/fabric-samples/commit/5d58254) [FAB-17145](https://jira.hyperledger.org/browse/FAB-17145) Add test network to Fabric Samples
* [e9f2957](https://github.com/hyperledger/fabric-samples/commit/e9f2957) [FAB-17062](https://jira.hyperledger.org/browse/FAB-17062) Fix typos in Commercial Paper readme
* [36694d0](https://github.com/hyperledger/fabric-samples/commit/36694d0) [FAB-17121](https://jira.hyperledger.org/browse/FAB-17121) Use new bootstrap config in orderer
* [429f087](https://github.com/hyperledger/fabric-samples/commit/429f087) update fabcar go to new programming model
* [1467086](https://github.com/hyperledger/fabric-samples/commit/1467086) Bump eslint-utils
* [33f349a](https://github.com/hyperledger/fabric-samples/commit/33f349a) Remove Stalebot
* [6af43bf](https://github.com/hyperledger/fabric-samples/commit/6af43bf) Change stalebot settings
* [4880401](https://github.com/hyperledger/fabric-samples/commit/4880401) [FAB-16284](https://jira.hyperledger.org/browse/FAB-16284) Remove E2E file and -f option from BYFN
* [fe96f60](https://github.com/hyperledger/fabric-samples/commit/fe96f60) [FAB-16850](https://jira.hyperledger.org/browse/FAB-16850) Set up CI with Azure Pipelines
* [81aabf4](https://github.com/hyperledger/fabric-samples/commit/81aabf4) [FAB-16849](https://jira.hyperledger.org/browse/FAB-16849) Various updates for Java version of FabCar
* [a42b858](https://github.com/hyperledger/fabric-samples/commit/a42b858) Update FabCar to reflect wallet API changes
* [890f9ea](https://github.com/hyperledger/fabric-samples/commit/890f9ea) [FAB-16713](https://jira.hyperledger.org/browse/FAB-16713) Fix npm audit warnings
* [e48e804](https://github.com/hyperledger/fabric-samples/commit/e48e804) [FAB-16776](https://jira.hyperledger.org/browse/FAB-16776) Move BYFN up to V2_0 capabilities
* [7b65a25](https://github.com/hyperledger/fabric-samples/commit/7b65a25) [IN-68] Add default GitHub SECURITY policy
* [408e0e8](https://github.com/hyperledger/fabric-samples/commit/408e0e8) [FAB-16619](https://jira.hyperledger.org/browse/FAB-16619) Fix the policy warning
* [670d446](https://github.com/hyperledger/fabric-samples/commit/670d446) [FAB-16668](https://jira.hyperledger.org/browse/FAB-16668) fabcar chaincode modify console output
* [f2939e2](https://github.com/hyperledger/fabric-samples/commit/f2939e2) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for Commercial Paper sample
* [3d19014](https://github.com/hyperledger/fabric-samples/commit/3d19014) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for FabCar sample
* [e2b7cb7](https://github.com/hyperledger/fabric-samples/commit/e2b7cb7) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for abstore sample
* [db48612](https://github.com/hyperledger/fabric-samples/commit/db48612) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Increase chaincode execute timeout
* [521a7ff](https://github.com/hyperledger/fabric-samples/commit/521a7ff) [FAB-16607](https://jira.hyperledger.org/browse/FAB-16607) Update FabCar to reflect CC updates
* [c13a5ec](https://github.com/hyperledger/fabric-samples/commit/c13a5ec) [FAB-16528](https://jira.hyperledger.org/browse/FAB-16528) marbles private chaincode sync up
* [3fad853](https://github.com/hyperledger/fabric-samples/commit/3fad853) [FAB-16528](https://jira.hyperledger.org/browse/FAB-16528) marbles private chaincode sync up
* [8b9b82f](https://github.com/hyperledger/fabric-samples/commit/8b9b82f) [FAB-16489](https://jira.hyperledger.org/browse/FAB-16489) Add CODEOWNERS
* [a6ce915](https://github.com/hyperledger/fabric-samples/commit/a6ce915) [FAB-16487](https://jira.hyperledger.org/browse/FAB-16487) Update eslint
* [48082cf](https://github.com/hyperledger/fabric-samples/commit/48082cf) [FAB-16362](https://jira.hyperledger.org/browse/FAB-16362) adding chaincode excution comments
* [1d379f3](https://github.com/hyperledger/fabric-samples/commit/1d379f3) [FAB-16474](https://jira.hyperledger.org/browse/FAB-16474) marbles02 chaincode error
* [18712ca](https://github.com/hyperledger/fabric-samples/commit/18712ca) [FAB-16133](https://jira.hyperledger.org/browse/FAB-16133) Remove Solo consensus from BYFN
* [91c720a](https://github.com/hyperledger/fabric-samples/commit/91c720a) [FAB-16390](https://jira.hyperledger.org/browse/FAB-16390) Added filter for invalid transactions
* [1d3e267](https://github.com/hyperledger/fabric-samples/commit/1d3e267) Redirect samples to fabric-{chaincode,protos}-go
* [398a5b1](https://github.com/hyperledger/fabric-samples/commit/398a5b1) [FABCI-394] Remove AnsiColor Wrapper
* [ce154e0](https://github.com/hyperledger/fabric-samples/commit/ce154e0) [FAB-16310](https://jira.hyperledger.org/browse/FAB-16310) Vendor Go dependencies in all samples
* [6ea7c71](https://github.com/hyperledger/fabric-samples/commit/6ea7c71) [FAB-16285](https://jira.hyperledger.org/browse/FAB-16285) Update blacklisted versions in BYFN
* [86cd831](https://github.com/hyperledger/fabric-samples/commit/86cd831) [FAB-16284](https://jira.hyperledger.org/browse/FAB-16284) Remove E2E file and -f option from BYFN
* [0063abe](https://github.com/hyperledger/fabric-samples/commit/0063abe) Update stale script name in interest rate swaps
* [3907507](https://github.com/hyperledger/fabric-samples/commit/3907507) [FAB-16277](https://jira.hyperledger.org/browse/FAB-16277) Update BYFN w/ Raft ports in Docker network
* [33b0065](https://github.com/hyperledger/fabric-samples/commit/33b0065) [FAB-14813](https://jira.hyperledger.org/browse/FAB-14813) Channel event sample in fabric-samples
* [b62d5bd](https://github.com/hyperledger/fabric-samples/commit/b62d5bd) [FAB-16132](https://jira.hyperledger.org/browse/FAB-16132) Remove Kafka consensus from BYFN
* [9b14525](https://github.com/hyperledger/fabric-samples/commit/9b14525) [FAB-15213](https://jira.hyperledger.org/browse/FAB-15213) Update Commercial Paper for Java
* [4158877](https://github.com/hyperledger/fabric-samples/commit/4158877) [FAB-16232](https://jira.hyperledger.org/browse/FAB-16232) Remove FabToken sample
* [b6380cc](https://github.com/hyperledger/fabric-samples/commit/b6380cc) [FAB-16198](https://jira.hyperledger.org/browse/FAB-16198) Run "go mod vendor" for FabCar Go contract
* [639848a](https://github.com/hyperledger/fabric-samples/commit/639848a) [FAB-16197](https://jira.hyperledger.org/browse/FAB-16197) Add connection profiles to .gitignore
* [3996db5](https://github.com/hyperledger/fabric-samples/commit/3996db5) [FAB-12219](https://jira.hyperledger.org/browse/FAB-12219) abstore node -> javascript
* [14ac271](https://github.com/hyperledger/fabric-samples/commit/14ac271) [FAB-12219](https://jira.hyperledger.org/browse/FAB-12219) marbles02 node -> javascript
* [13f16e5](https://github.com/hyperledger/fabric-samples/commit/13f16e5) [FGJ-4] CI tests for FabCar Java sample
* [171a7d2](https://github.com/hyperledger/fabric-samples/commit/171a7d2) FGJ-4 Fabcar sample
* [868f9d0](https://github.com/hyperledger/fabric-samples/commit/868f9d0) [FAB-15625](https://jira.hyperledger.org/browse/FAB-15625) Add UT for Simple Asset Chaincode
* [597d150](https://github.com/hyperledger/fabric-samples/commit/597d150) [FAB-14532](https://jira.hyperledger.org/browse/FAB-14532) Remove LL FabCar sample
* [583ff8f](https://github.com/hyperledger/fabric-samples/commit/583ff8f) Use renamed CheckCommitReadiness function
* [750f937](https://github.com/hyperledger/fabric-samples/commit/750f937) [FAB-15213](https://jira.hyperledger.org/browse/FAB-15213) Add Java FabCar sample contract
* [abbda95](https://github.com/hyperledger/fabric-samples/commit/abbda95) [FAB-15897](https://jira.hyperledger.org/browse/FAB-15897) Improve FabCar test logging
* [dd8150a](https://github.com/hyperledger/fabric-samples/commit/dd8150a) [FAB-15104](https://jira.hyperledger.org/browse/FAB-15104) Remove versions from fabric-samples readme
* [1387aa8](https://github.com/hyperledger/fabric-samples/commit/1387aa8) [FAB-15927](https://jira.hyperledger.org/browse/FAB-15927)  Better expression for golang
* [61c33d3](https://github.com/hyperledger/fabric-samples/commit/61c33d3) [FAB-15973](https://jira.hyperledger.org/browse/FAB-15973) use --output json on simulatecommit
* [8bbdd0f](https://github.com/hyperledger/fabric-samples/commit/8bbdd0f) [FAB-15716](https://jira.hyperledger.org/browse/FAB-15716) Fix instructions for dev-mode
* [0254d67](https://github.com/hyperledger/fabric-samples/commit/0254d67) QueryApprovalStatus -> SimulateCommitChaincodeDef
* [c57d67c](https://github.com/hyperledger/fabric-samples/commit/c57d67c) [FAB-15782](https://jira.hyperledger.org/browse/FAB-15782) Sample Go CC should include deps
* [6ba5a19](https://github.com/hyperledger/fabric-samples/commit/6ba5a19) Update to Go 1.12.5 in ci.properties
* [1774a25](https://github.com/hyperledger/fabric-samples/commit/1774a25) [FAB-15723](https://jira.hyperledger.org/browse/FAB-15723) Fix script and instruction with ccenv
* [6ae711c](https://github.com/hyperledger/fabric-samples/commit/6ae711c) [FAB-15717](https://jira.hyperledger.org/browse/FAB-15717) fix Error Unexpected end of JSON input
* [5be56d3](https://github.com/hyperledger/fabric-samples/commit/5be56d3) [FAB-15104](https://jira.hyperledger.org/browse/FAB-15104) Remove scripts/bootstrap.sh
* [779f8f3](https://github.com/hyperledger/fabric-samples/commit/779f8f3) [FAB-15649](https://jira.hyperledger.org/browse/FAB-15649)Fix Fabcar to install Chaincode on all peers
* [7c5f5d3](https://github.com/hyperledger/fabric-samples/commit/7c5f5d3) [FAB-15199](https://jira.hyperledger.org/browse/FAB-15199) Update interest rate sample
* [f0dca20](https://github.com/hyperledger/fabric-samples/commit/f0dca20) [FAB-14532](https://jira.hyperledger.org/browse/FAB-14532) Remove LL FabCar sample
* [1ed1a10](https://github.com/hyperledger/fabric-samples/commit/1ed1a10) [FAB-15573](https://jira.hyperledger.org/browse/FAB-15573) Fix typo in fabric-samples-ci.md
* [2e7fec9](https://github.com/hyperledger/fabric-samples/commit/2e7fec9) [FAB-15601](https://jira.hyperledger.org/browse/FAB-15601) BYFN: Fix MAX_RETRY for couchdb
* [1e9e4c4](https://github.com/hyperledger/fabric-samples/commit/1e9e4c4) [FAB-9329](https://jira.hyperledger.org/browse/FAB-9329) Remove the unused variable in BYFN/EYFN
* [964c09f](https://github.com/hyperledger/fabric-samples/commit/964c09f) [FAB-15601](https://jira.hyperledger.org/browse/FAB-15601) BYFN: Fix MAX_RETRY for couchdb
* [41dca99](https://github.com/hyperledger/fabric-samples/commit/41dca99) [FAB-15127](https://jira.hyperledger.org/browse/FAB-15127) Update high throughput sample
* [3fe014a](https://github.com/hyperledger/fabric-samples/commit/3fe014a) Use official CouchDB image
* [f2d0fa0](https://github.com/hyperledger/fabric-samples/commit/f2d0fa0) [FAB-14487](https://jira.hyperledger.org/browse/FAB-14487) Make FabCar use BYFN, not basic-network
* [e9c3649](https://github.com/hyperledger/fabric-samples/commit/e9c3649) [FAB-15276](https://jira.hyperledger.org/browse/FAB-15276) Fix license statements
* [fbe4036](https://github.com/hyperledger/fabric-samples/commit/fbe4036) [FAB-14486](https://jira.hyperledger.org/browse/FAB-14486) Extend BYFN to opt skip chaincode deploy
* [0c4141f](https://github.com/hyperledger/fabric-samples/commit/0c4141f) [FAB-14485](https://jira.hyperledger.org/browse/FAB-14485) Extend BYFN to opt inc cert authorities
* [529b83b](https://github.com/hyperledger/fabric-samples/commit/529b83b) [FAB-14330](https://jira.hyperledger.org/browse/FAB-14330) Add connection profiles for BYFN and EYFN
* [2c21c83](https://github.com/hyperledger/fabric-samples/commit/2c21c83) [FABN-1184] Update fabtoken/README.md
* [5056a23](https://github.com/hyperledger/fabric-samples/commit/5056a23) [FABN-1184] Add CI script for fabtoken sample app
* [5d6db95](https://github.com/hyperledger/fabric-samples/commit/5d6db95) Update maintainers for fabric-samples
* [f527815](https://github.com/hyperledger/fabric-samples/commit/f527815) [FAB-15119](https://jira.hyperledger.org/browse/FAB-15119) Fix BYFN with Java chaincode
* [8245252](https://github.com/hyperledger/fabric-samples/commit/8245252) [FABN-1184] Implement fabtoken sample app
* [1bd1c2f](https://github.com/hyperledger/fabric-samples/commit/1bd1c2f) FABCI-284 Update CI Pipeline script
* [c24abf9](https://github.com/hyperledger/fabric-samples/commit/c24abf9) [FAB-15022](https://jira.hyperledger.org/browse/FAB-15022) Basic-network support for new lifecycle
* [b64fd45](https://github.com/hyperledger/fabric-samples/commit/b64fd45) [FAB-15051](https://jira.hyperledger.org/browse/FAB-15051) delStandard() function for high-throughput
* [3e68a7e](https://github.com/hyperledger/fabric-samples/commit/3e68a7e) [FAB-14784](https://jira.hyperledger.org/browse/FAB-14784) Remove balance-transfer
* [eb3fe08](https://github.com/hyperledger/fabric-samples/commit/eb3fe08) [FAB-14779](https://jira.hyperledger.org/browse/FAB-14779) QueryApprovalStatus step in byfn
* [2777429](https://github.com/hyperledger/fabric-samples/commit/2777429) [FAB-14711](https://jira.hyperledger.org/browse/FAB-14711) update byfn with new lifecycle
* [aec3389](https://github.com/hyperledger/fabric-samples/commit/aec3389) [FAB-12215](https://jira.hyperledger.org/browse/FAB-12215)WYFA:Remove chainId in tx proposal request
* [b5d5026](https://github.com/hyperledger/fabric-samples/commit/b5d5026) [FAB-14633](https://jira.hyperledger.org/browse/FAB-14633) Remove apt-get from eyfn.sh
* [efaadd3](https://github.com/hyperledger/fabric-samples/commit/efaadd3) [FAB-14531](https://jira.hyperledger.org/browse/FAB-14531) BYFN Raft with 5 nodes
* [d63047c](https://github.com/hyperledger/fabric-samples/commit/d63047c) [FAB-14444](https://jira.hyperledger.org/browse/FAB-14444)
* [7e3d428](https://github.com/hyperledger/fabric-samples/commit/7e3d428) [FAB-14369](https://jira.hyperledger.org/browse/FAB-14369)Fix dev mode failing to build Chaincode
* [420ba23](https://github.com/hyperledger/fabric-samples/commit/420ba23) [FAB-12762](https://jira.hyperledger.org/browse/FAB-12762) Add etcd/raft consensus option to BYFN
* [2b68c80](https://github.com/hyperledger/fabric-samples/commit/2b68c80) [FAB-14317](https://jira.hyperledger.org/browse/FAB-14317) Add default policies to org3
* [f942010](https://github.com/hyperledger/fabric-samples/commit/f942010) [FAB-14268](https://jira.hyperledger.org/browse/FAB-14268) Make BYFN/EYFN ports match external ports
* [4e2ce23](https://github.com/hyperledger/fabric-samples/commit/4e2ce23) [FAB-14271](https://jira.hyperledger.org/browse/FAB-14271) Add channel policies to channel config
* [f26477c](https://github.com/hyperledger/fabric-samples/commit/f26477c) [FAB-11796](https://jira.hyperledger.org/browse/FAB-11796)high-throughput:Remove unnecessary prunesafe
* [137327a](https://github.com/hyperledger/fabric-samples/commit/137327a) [FAB-14162](https://jira.hyperledger.org/browse/FAB-14162) Pin fabric-samples in master to "unstable"
* [6007c09](https://github.com/hyperledger/fabric-samples/commit/6007c09) [FAB-13862](https://jira.hyperledger.org/browse/FAB-13862) Rename example02 ABstore
* [94cb603](https://github.com/hyperledger/fabric-samples/commit/94cb603) [FAB-13933](https://jira.hyperledger.org/browse/FAB-13933) Fix misspellings
* [a8a5539](https://github.com/hyperledger/fabric-samples/commit/a8a5539) Fix doc link Fix variable error
* [b0cda61](https://github.com/hyperledger/fabric-samples/commit/b0cda61) [FAB-13769](https://jira.hyperledger.org/browse/FAB-13769) Add UT code to ABAC sample Chaincode
* [c7438e1](https://github.com/hyperledger/fabric-samples/commit/c7438e1) [FAB-13668](https://jira.hyperledger.org/browse/FAB-13668) BYFN's container volume mapping is bad
* [e48b2de](https://github.com/hyperledger/fabric-samples/commit/e48b2de) [FAB-13489](https://jira.hyperledger.org/browse/FAB-13489) fabric-samples add error msg
* [8a458b5](https://github.com/hyperledger/fabric-samples/commit/8a458b5) [FAB-12056](https://jira.hyperledger.org/browse/FAB-12056) Private marbles cc use transient data
* [6269941](https://github.com/hyperledger/fabric-samples/commit/6269941) Correct broken link
* [461b6ab](https://github.com/hyperledger/fabric-samples/commit/461b6ab) FABC-781 Remove fabric-ca sample
* [e9b9477](https://github.com/hyperledger/fabric-samples/commit/e9b9477) [FAB-13372](https://jira.hyperledger.org/browse/FAB-13372) Fabric-Samples return error msg
* [e3da220](https://github.com/hyperledger/fabric-samples/commit/e3da220) [FAB-13433](https://jira.hyperledger.org/browse/FAB-13433) - Update Jenkinsfile configuration
* [33db64e](https://github.com/hyperledger/fabric-samples/commit/33db64e) Configure Stale ProBot
* [5cd277f](https://github.com/hyperledger/fabric-samples/commit/5cd277f) [FAB-11951](https://jira.hyperledger.org/browse/FAB-11951) Interest-rate swap example for SBE
* [9567985](https://github.com/hyperledger/fabric-samples/commit/9567985) [FAB-13407](https://jira.hyperledger.org/browse/FAB-13407) Align fabric-samples with 1.4.0-rc2 release
* [c7572aa](https://github.com/hyperledger/fabric-samples/commit/c7572aa) [FAB-13305](https://jira.hyperledger.org/browse/FAB-13305) Update scripts to pull latest artifacts
* [ab46e35](https://github.com/hyperledger/fabric-samples/commit/ab46e35) [FAB-13283](https://jira.hyperledger.org/browse/FAB-13283) Update sample code for commercial paper
* [f677821](https://github.com/hyperledger/fabric-samples/commit/f677821) [FAB-13232](https://jira.hyperledger.org/browse/FAB-13232) fix peer node start command
* [6a7472e](https://github.com/hyperledger/fabric-samples/commit/6a7472e) [FAB-13126](https://jira.hyperledger.org/browse/FAB-13126) Align fabric-samples with 1.4.0-rc1 release
* [445ccbc](https://github.com/hyperledger/fabric-samples/commit/445ccbc) [FAB-12880](https://jira.hyperledger.org/browse/FAB-12880) Move old prog model samples for FabCar
* [5be62b5](https://github.com/hyperledger/fabric-samples/commit/5be62b5) [FAB-13207](https://jira.hyperledger.org/browse/FAB-13207) Remove incorrect discovery options
* [fdbd92d](https://github.com/hyperledger/fabric-samples/commit/fdbd92d) [FAB-13206](https://jira.hyperledger.org/browse/FAB-13206) Remove dependencies on fabric-client
* [eff0046](https://github.com/hyperledger/fabric-samples/commit/eff0046) [FAB-12877](https://jira.hyperledger.org/browse/FAB-12877) Add fabcar app using new prog model (JS)
* [c184196](https://github.com/hyperledger/fabric-samples/commit/c184196) [FAB-12878](https://jira.hyperledger.org/browse/FAB-12878) Add fabcar app using new prog model (TS)
* [e1a39e6](https://github.com/hyperledger/fabric-samples/commit/e1a39e6) [FAB-12724](https://jira.hyperledger.org/browse/FAB-12724) Upgrade from 1.3.x to 1.4.0
* [c21bbba](https://github.com/hyperledger/fabric-samples/commit/c21bbba) Update samples to use new logging env variables
* [7ad9f19](https://github.com/hyperledger/fabric-samples/commit/7ad9f19) [FAB-13011](https://jira.hyperledger.org/browse/FAB-13011) add kafka consensus type to byfn sample
* [33f064f](https://github.com/hyperledger/fabric-samples/commit/33f064f) [FAB-13170](https://jira.hyperledger.org/browse/FAB-13170) Add memberOnlyRead to marbles sample
* [928b72b](https://github.com/hyperledger/fabric-samples/commit/928b72b) [FAB-12875](https://jira.hyperledger.org/browse/FAB-12875) Add automated tests for fabcar sample
* [5c087f1](https://github.com/hyperledger/fabric-samples/commit/5c087f1) [FAB-13046](https://jira.hyperledger.org/browse/FAB-13046) Update TypeScript contract dependencies
* [3748983](https://github.com/hyperledger/fabric-samples/commit/3748983) [FAB-12879](https://jira.hyperledger.org/browse/FAB-12879) Update fabcar script for new contracts
* [4fb3b57](https://github.com/hyperledger/fabric-samples/commit/4fb3b57) [FAB-12852](https://jira.hyperledger.org/browse/FAB-12852) Add fabcar contract w/ new prog model (TS)
* [9facb42](https://github.com/hyperledger/fabric-samples/commit/9facb42) [FAB-12851](https://jira.hyperledger.org/browse/FAB-12851) Add fabcar contract w/ new prog model (JS)
* [e67fcf1](https://github.com/hyperledger/fabric-samples/commit/e67fcf1) [FAB-12322](https://jira.hyperledger.org/browse/FAB-12322) Update commercial-paper sample
* [fd6e2c4](https://github.com/hyperledger/fabric-samples/commit/fd6e2c4) [FAB-12703](https://jira.hyperledger.org/browse/FAB-12703) Fix misspelling "lauches"
* [c05f172](https://github.com/hyperledger/fabric-samples/commit/c05f172) [FAB-12608](https://jira.hyperledger.org/browse/FAB-12608) Update pipeline script
* [286861e](https://github.com/hyperledger/fabric-samples/commit/286861e) [FAB-12371](https://jira.hyperledger.org/browse/FAB-12371)Fix the abac sample to use new cid package
* [24c5e47](https://github.com/hyperledger/fabric-samples/commit/24c5e47) [FAB-12026](https://jira.hyperledger.org/browse/FAB-12026) pagination samples for node marbles02
* [6dc5ce5](https://github.com/hyperledger/fabric-samples/commit/6dc5ce5) [FAB-12587](https://jira.hyperledger.org/browse/FAB-12587) Fix for Query Block by block hash API
* [df311ce](https://github.com/hyperledger/fabric-samples/commit/df311ce) [FAB-12173](https://jira.hyperledger.org/browse/FAB-12173) balance-transfer: Update anchor peers
* [c925148](https://github.com/hyperledger/fabric-samples/commit/c925148) [FAB-12415](https://jira.hyperledger.org/browse/FAB-12415) samples for 1.3.0 (master cleanup)
* [3a12c60](https://github.com/hyperledger/fabric-samples/commit/3a12c60) [FAB-12275](https://jira.hyperledger.org/browse/FAB-12275) Fix the warn in creating genesis block
* [c6f6324](https://github.com/hyperledger/fabric-samples/commit/c6f6324) [FAB-12257](https://jira.hyperledger.org/browse/FAB-12257) allow balance-transfer for doscovery
* [4445e8d](https://github.com/hyperledger/fabric-samples/commit/4445e8d) [FAB-12272](https://jira.hyperledger.org/browse/FAB-12272) Increase MAX_RETRY to 10
* [33d333f](https://github.com/hyperledger/fabric-samples/commit/33d333f) [FAB-12190](https://jira.hyperledger.org/browse/FAB-12190) Update stable version in CI scripts
* [edee638](https://github.com/hyperledger/fabric-samples/commit/edee638) [FAB-12184](https://jira.hyperledger.org/browse/FAB-12184) Prepare fabric-samples for 1.3.0-rc1
* [514d456](https://github.com/hyperledger/fabric-samples/commit/514d456) [FAB-12170](https://jira.hyperledger.org/browse/FAB-12170) Fix dependency check in java chaincode
* [9f80e47](https://github.com/hyperledger/fabric-samples/commit/9f80e47) [FAB-12119](https://jira.hyperledger.org/browse/FAB-12119) Fix groupId in java chaincodes
* [3237229](https://github.com/hyperledger/fabric-samples/commit/3237229) [FAB-12073](https://jira.hyperledger.org/browse/FAB-12073) Fix Org3 peers CouchDB config.
* [eece3d8](https://github.com/hyperledger/fabric-samples/commit/eece3d8) [FAB-12106](https://jira.hyperledger.org/browse/FAB-12106) Update fabric-ca build scripts
* [f62952f](https://github.com/hyperledger/fabric-samples/commit/f62952f) [FABC-131] Change fabric-ca sample to build images
* [4089786](https://github.com/hyperledger/fabric-samples/commit/4089786) [FAB-11867](https://jira.hyperledger.org/browse/FAB-11867) Develop Apps:Sample pt 2 -- application
* [9ee57c6](https://github.com/hyperledger/fabric-samples/commit/9ee57c6) [FAB-11778](https://jira.hyperledger.org/browse/FAB-11778) Upgrade to v1.3.x from v1.2.x in byfn
* [e7a1b76](https://github.com/hyperledger/fabric-samples/commit/e7a1b76) [FAB-9386](https://jira.hyperledger.org/browse/FAB-9386) Remove Marbles sample reference to Fauxton
* [9c6acee](https://github.com/hyperledger/fabric-samples/commit/9c6acee) [FAB-12022](https://jira.hyperledger.org/browse/FAB-12022) Fix CI by increasing couchdb timeout
* [4030ebd](https://github.com/hyperledger/fabric-samples/commit/4030ebd) [FAB-11397](https://jira.hyperledger.org/browse/FAB-11397) Adding java cc
* [d776651](https://github.com/hyperledger/fabric-samples/commit/d776651) [FAB-11723](https://jira.hyperledger.org/browse/FAB-11723) Developing Apps: Sample pt 1 -- contract
* [cbbbc78](https://github.com/hyperledger/fabric-samples/commit/cbbbc78) [FAB-11577](https://jira.hyperledger.org/browse/FAB-11577) Fix balance transfer to install Chaincode
* [bfdc0b6](https://github.com/hyperledger/fabric-samples/commit/bfdc0b6) [FAB-11518](https://jira.hyperledger.org/browse/FAB-11518)
* [5930dfc](https://github.com/hyperledger/fabric-samples/commit/5930dfc) [FAB-11488](https://jira.hyperledger.org/browse/FAB-11488) Update CI script
* [ca6959c](https://github.com/hyperledger/fabric-samples/commit/ca6959c) [FAB-11311](https://jira.hyperledger.org/browse/FAB-11311) Update fabric image version
* [0ca9e6e](https://github.com/hyperledger/fabric-samples/commit/0ca9e6e) FABN-833 Update Jenkinsfile to capture build artifacts
* [a4a15cb](https://github.com/hyperledger/fabric-samples/commit/a4a15cb) [FAB-11220](https://jira.hyperledger.org/browse/FAB-11220) Samples - remove EventHub
* [c4bdc68](https://github.com/hyperledger/fabric-samples/commit/c4bdc68) [FAB-8479](https://jira.hyperledger.org/browse/FAB-8479) Added Endorsement policy
* [6edd320](https://github.com/hyperledger/fabric-samples/commit/6edd320) [FAB-9297](https://jira.hyperledger.org/browse/FAB-9297) fix README links and update bootstrap.sh
* [75e2931](https://github.com/hyperledger/fabric-samples/commit/75e2931) [FAB-10811](https://jira.hyperledger.org/browse/FAB-10811) fabric-ca sample is broken on v1.2
* [ad40e29](https://github.com/hyperledger/fabric-samples/commit/ad40e29) [FAB-10732](https://jira.hyperledger.org/browse/FAB-10732) BYFN upgrade to v1.2
* [20ad472](https://github.com/hyperledger/fabric-samples/commit/20ad472) [FAB-10801](https://jira.hyperledger.org/browse/FAB-10801) format and styling for shell artifacts
* [e95210e](https://github.com/hyperledger/fabric-samples/commit/e95210e) [FAB-10074](https://jira.hyperledger.org/browse/FAB-10074) CI Script for pipeline project type
* [5956178](https://github.com/hyperledger/fabric-samples/commit/5956178) [FAB-6600](https://jira.hyperledger.org/browse/FAB-6600) Sample chaincode for private data
* [21444ab](https://github.com/hyperledger/fabric-samples/commit/21444ab) [FAB-10297](https://jira.hyperledger.org/browse/FAB-10297) replace fabric-preload.sh
* [3c32c52](https://github.com/hyperledger/fabric-samples/commit/3c32c52) [FAB-10346](https://jira.hyperledger.org/browse/FAB-10346) Ensure peers are in sync in eyfn
* [2f30504](https://github.com/hyperledger/fabric-samples/commit/2f30504) [FAB-10340](https://jira.hyperledger.org/browse/FAB-10340) Fix broken link in chaincode-docker-devmode
* [a603655](https://github.com/hyperledger/fabric-samples/commit/a603655) [FAB-10306](https://jira.hyperledger.org/browse/FAB-10306) Fix config in balance-transfer
* [1cd059e](https://github.com/hyperledger/fabric-samples/commit/1cd059e) [FAB-8612](https://jira.hyperledger.org/browse/FAB-8612) Cleanup eyfn/byfn with --remove-orphans
* [3031a8c](https://github.com/hyperledger/fabric-samples/commit/3031a8c) [FAB-10235](https://jira.hyperledger.org/browse/FAB-10235) Update BYFN to use V1_2 capability
* [5cf1944](https://github.com/hyperledger/fabric-samples/commit/5cf1944) [FAB-10185](https://jira.hyperledger.org/browse/FAB-10185) Reorder for logical consistency
* [e2f4f20](https://github.com/hyperledger/fabric-samples/commit/e2f4f20) [FAB-10176](https://jira.hyperledger.org/browse/FAB-10176) Fix invalid configtx.yaml documents
* [cad2b98](https://github.com/hyperledger/fabric-samples/commit/cad2b98) [FAB-10073](https://jira.hyperledger.org/browse/FAB-10073) Fix broken link in samples/fabric-ca/README
* [ace2e5a](https://github.com/hyperledger/fabric-samples/commit/ace2e5a) [FAB-10021](https://jira.hyperledger.org/browse/FAB-10021) Typo in balance-transfer/testAPIs.sh
* [fa1d899](https://github.com/hyperledger/fabric-samples/commit/fa1d899) [FAB-9893](https://jira.hyperledger.org/browse/FAB-9893) Add .gitreview file for quicker Gerrit setup
* [98561e0](https://github.com/hyperledger/fabric-samples/commit/98561e0) [FAB-9552](https://jira.hyperledger.org/browse/FAB-9552) Fix access of an un-declared variable
* [b5dad8c](https://github.com/hyperledger/fabric-samples/commit/b5dad8c) [FAB-9317](https://jira.hyperledger.org/browse/FAB-9317) Update first-network with multi endorse
* [146f7bc](https://github.com/hyperledger/fabric-samples/commit/146f7bc) [FAB-9572](https://jira.hyperledger.org/browse/FAB-9572) Fix README to match new version of BYFN
* [010fcb5](https://github.com/hyperledger/fabric-samples/commit/010fcb5) [FAB-9326](https://jira.hyperledger.org/browse/FAB-9326) Clean up byfn.sh, drop "-m" option
* [2d6386c](https://github.com/hyperledger/fabric-samples/commit/2d6386c) [FAB-8245](https://jira.hyperledger.org/browse/FAB-8245) change first network according to the fix
* [3a5108f](https://github.com/hyperledger/fabric-samples/commit/3a5108f) [FAB-9475](https://jira.hyperledger.org/browse/FAB-9475) Fix a dead link in README
* [77a6568](https://github.com/hyperledger/fabric-samples/commit/77a6568) [FAB-9294](https://jira.hyperledger.org/browse/FAB-9294) eliminate excess noise in BYFN
* [41f5ab8](https://github.com/hyperledger/fabric-samples/commit/41f5ab8) [FAB-9406](https://jira.hyperledger.org/browse/FAB-9406) Typo in byfn.sh
* [f5c2eb8](https://github.com/hyperledger/fabric-samples/commit/f5c2eb8) [FAB-9362](https://jira.hyperledger.org/browse/FAB-9362) add CONTRIBUTING.md and CODE_OF_CONDUCT.md
* [9d518fb](https://github.com/hyperledger/fabric-samples/commit/9d518fb) [FAB-9330](https://jira.hyperledger.org/browse/FAB-9330) Refactor top-level .gitignore into subdirs
* [a080da3](https://github.com/hyperledger/fabric-samples/commit/a080da3) [FAB-8958](https://jira.hyperledger.org/browse/FAB-8958) Add org3 removal to byfn.sh
* [8fc0865](https://github.com/hyperledger/fabric-samples/commit/8fc0865) [FAB-9185](https://jira.hyperledger.org/browse/FAB-9185) add /config to .gitignore
* [680ff01](https://github.com/hyperledger/fabric-samples/commit/680ff01) [FAB-8600](https://jira.hyperledger.org/browse/FAB-8600)Clear hyperledger-related containers only
* [4f97717](https://github.com/hyperledger/fabric-samples/commit/4f97717) [FAB-8947](https://jira.hyperledger.org/browse/FAB-8947) Fabric-Samples remove package lock
* [fcf62ad](https://github.com/hyperledger/fabric-samples/commit/fcf62ad) [FAB-8265](https://jira.hyperledger.org/browse/FAB-8265) Fixed spelling error in registerUser
* [e4d7760](https://github.com/hyperledger/fabric-samples/commit/e4d7760) [ [FAB-8730](https://jira.hyperledger.org/browse/FAB-8730) ] hyphen breaks fabric-samples
* [cf79cd1](https://github.com/hyperledger/fabric-samples/commit/cf79cd1) [FAB-7584](https://jira.hyperledger.org/browse/FAB-7584) Removes copy of creds to keystore

## "v1.1.0"

* [11e4c23](https://github.com/hyperledger/fabric-samples/commit/11e4c23) Update samples to use v2.0 or later dependencies (#161)
* [94beab7](https://github.com/hyperledger/fabric-samples/commit/94beab7) FABN-1531 Use v2.1.0 sdk-node modules
* [8820d2f](https://github.com/hyperledger/fabric-samples/commit/8820d2f) Fix commercial-paper README
* [aa9b577](https://github.com/hyperledger/fabric-samples/commit/aa9b577) Remove TLS enabled switch (#155)
* [381fb46](https://github.com/hyperledger/fabric-samples/commit/381fb46) upgraded abstore golang chaincode to use contract-api package (#154)
* [5e5d2c8](https://github.com/hyperledger/fabric-samples/commit/5e5d2c8) Update java chaincode to be compatible with doc and other implementations (#149)
* [c572c51](https://github.com/hyperledger/fabric-samples/commit/c572c51) Organize and Standardize `ci` Directory Content (#152)
* [aa40963](https://github.com/hyperledger/fabric-samples/commit/aa40963) Perform General Cleanup (#151)
* [da41afa](https://github.com/hyperledger/fabric-samples/commit/da41afa) Remove left over rm -rf command from BYFN (#148)
* [4bb48a9](https://github.com/hyperledger/fabric-samples/commit/4bb48a9) Jenkins no longer used (#145)
* [6f984e1](https://github.com/hyperledger/fabric-samples/commit/6f984e1) Bump acorn from 6.4.0 to 6.4.1 in /fabcar/javascript (#144)
* [b155620](https://github.com/hyperledger/fabric-samples/commit/b155620) Remove redundant invoke command from test network (#142)
* [851933b](https://github.com/hyperledger/fabric-samples/commit/851933b) Add enrollUser files to commercial paper (#140)
* [87600bd](https://github.com/hyperledger/fabric-samples/commit/87600bd) [FAB-17268](https://jira.hyperledger.org/browse/FAB-17268) Move fabcar sample to test network (#103)
* [9397788](https://github.com/hyperledger/fabric-samples/commit/9397788) Wrong groupId on hyperledger fabric dependencies for java-application (#134)
* [92555fb](https://github.com/hyperledger/fabric-samples/commit/92555fb) Update README.md (#133)
* [59c6641](https://github.com/hyperledger/fabric-samples/commit/59c6641) Change Download Location of Fabric Binaries (#143)
* [1f283fc](https://github.com/hyperledger/fabric-samples/commit/1f283fc) init function does not exist on fabcar (#141)
* [defb6bb](https://github.com/hyperledger/fabric-samples/commit/defb6bb) [FAB-17656](https://jira.hyperledger.org/browse/FAB-17656) echo Generating channel.tx (#139)
* [4c7bab0](https://github.com/hyperledger/fabric-samples/commit/4c7bab0) fix: package seletor REGEX (#135)
* [db69c6f](https://github.com/hyperledger/fabric-samples/commit/db69c6f) Add fabcar external service sample (#136)
* [7f5f5e6](https://github.com/hyperledger/fabric-samples/commit/7f5f5e6) [FAB-17504](https://jira.hyperledger.org/browse/FAB-17504) add Organizations.<Org>.OrdererEndpoints and remove Orderer.Addresses (#125)
* [f3fc08d](https://github.com/hyperledger/fabric-samples/commit/f3fc08d) Remove solo and kafka from test net configtx.yaml (#137)
* [e17574d](https://github.com/hyperledger/fabric-samples/commit/e17574d) Add CA's to docker test network (#124)
* [faac18e](https://github.com/hyperledger/fabric-samples/commit/faac18e) [FAB-17461](https://jira.hyperledger.org/browse/FAB-17461) Move off_chain_data sample to test network (#122)
* [121a44a](https://github.com/hyperledger/fabric-samples/commit/121a44a) [FAB-17460](https://jira.hyperledger.org/browse/FAB-17460) Move High Throughput sample to test network (#112)
* [a2f3a66](https://github.com/hyperledger/fabric-samples/commit/a2f3a66) Update docker image version
* [e5b898c](https://github.com/hyperledger/fabric-samples/commit/e5b898c) Revert "first-network/scripts/*: Make Chaincode name configurable (#118)" (#131)
* [9ef61e2](https://github.com/hyperledger/fabric-samples/commit/9ef61e2) first-network/scripts/*: Make Chaincode name configurable (#118)
* [e204ebb](https://github.com/hyperledger/fabric-samples/commit/e204ebb) Remove reference to 2.0 beta (#111)
* [3dbe116](https://github.com/hyperledger/fabric-samples/commit/3dbe116) [FAB-17456](https://jira.hyperledger.org/browse/FAB-17456) fabric-samples read ccp (#117)
* [965ed1f](https://github.com/hyperledger/fabric-samples/commit/965ed1f) [FAB-17498](https://jira.hyperledger.org/browse/FAB-17498) Beta Images removal, test test-network (#121)
* [403019e](https://github.com/hyperledger/fabric-samples/commit/403019e) [FAB-17495](https://jira.hyperledger.org/browse/FAB-17495) Remove Basic Network sample (#120)
* [883ef99](https://github.com/hyperledger/fabric-samples/commit/883ef99) [FAB-17457](https://jira.hyperledger.org/browse/FAB-17457) Script correction (#119)
* [b89ee34](https://github.com/hyperledger/fabric-samples/commit/b89ee34) Update Commercial Paper to v2.0 Lifecycle (#109)
* [4208644](https://github.com/hyperledger/fabric-samples/commit/4208644) [FAB-17478](https://jira.hyperledger.org/browse/FAB-17478) Update commercial paper to use go api v1.0.0 (#115)
* [0df5ed9](https://github.com/hyperledger/fabric-samples/commit/0df5ed9) [FAB-17477](https://jira.hyperledger.org/browse/FAB-17477) Update fabcar to use go api v1.0.0 (#116)
* [571733f](https://github.com/hyperledger/fabric-samples/commit/571733f) [FAB-17447](https://jira.hyperledger.org/browse/FAB-17447) Update to 2.0.0 Libraries
* [67b4ee7](https://github.com/hyperledger/fabric-samples/commit/67b4ee7) Add Org3 bugs in test network (#108)
* [5b93dd0](https://github.com/hyperledger/fabric-samples/commit/5b93dd0) [FAB-17140](https://jira.hyperledger.org/browse/FAB-17140) Add go commercial paper contract (#102)
* [4fe6a25](https://github.com/hyperledger/fabric-samples/commit/4fe6a25) [FABCI-482] Update Nexus URL's to Artifactory (#92)
* [1488fbb](https://github.com/hyperledger/fabric-samples/commit/1488fbb) Add 1.x versions of fabric to blacklisted versions
* [8ca279d](https://github.com/hyperledger/fabric-samples/commit/8ca279d) Add Support for Versioning NodeJS (#106)
* [b3b5267](https://github.com/hyperledger/fabric-samples/commit/b3b5267) [FAB-17243](https://jira.hyperledger.org/browse/FAB-17243) Add support for Fabric CA for Org3 on the (#91)
* [ce41ff7](https://github.com/hyperledger/fabric-samples/commit/ce41ff7) Remove references to vendoring chaincode from your gopath (#96)
* [4235d30](https://github.com/hyperledger/fabric-samples/commit/4235d30) [FAB-17306](https://jira.hyperledger.org/browse/FAB-17306) Fix artifact names in test-network (#97)
* [4c2a0a4](https://github.com/hyperledger/fabric-samples/commit/4c2a0a4) [FAB-16147](https://jira.hyperledger.org/browse/FAB-16147) Update Commercial Paper to work with v2 (#98)
* [6d9fd6f](https://github.com/hyperledger/fabric-samples/commit/6d9fd6f) Remove Gerrit reference
* [a026a4f](https://github.com/hyperledger/fabric-samples/commit/a026a4f) Fixed typo (#90)
* [cdb0e8b](https://github.com/hyperledger/fabric-samples/commit/cdb0e8b) TYPO (#89)
* [94ac8b6](https://github.com/hyperledger/fabric-samples/commit/94ac8b6) Update to use beta levels of modules (#88)
* [d848633](https://github.com/hyperledger/fabric-samples/commit/d848633) [FAB-16844](https://jira.hyperledger.org/browse/FAB-16844) Correct BYFN CC name
* [73267e1](https://github.com/hyperledger/fabric-samples/commit/73267e1) Fix test network bugs for adding org3
* [5d58254](https://github.com/hyperledger/fabric-samples/commit/5d58254) [FAB-17145](https://jira.hyperledger.org/browse/FAB-17145) Add test network to Fabric Samples
* [e9f2957](https://github.com/hyperledger/fabric-samples/commit/e9f2957) [FAB-17062](https://jira.hyperledger.org/browse/FAB-17062) Fix typos in Commercial Paper readme
* [36694d0](https://github.com/hyperledger/fabric-samples/commit/36694d0) [FAB-17121](https://jira.hyperledger.org/browse/FAB-17121) Use new bootstrap config in orderer
* [429f087](https://github.com/hyperledger/fabric-samples/commit/429f087) update fabcar go to new programming model
* [1467086](https://github.com/hyperledger/fabric-samples/commit/1467086) Bump eslint-utils
* [33f349a](https://github.com/hyperledger/fabric-samples/commit/33f349a) Remove Stalebot
* [6af43bf](https://github.com/hyperledger/fabric-samples/commit/6af43bf) Change stalebot settings
* [4880401](https://github.com/hyperledger/fabric-samples/commit/4880401) [FAB-16284](https://jira.hyperledger.org/browse/FAB-16284) Remove E2E file and -f option from BYFN
* [fe96f60](https://github.com/hyperledger/fabric-samples/commit/fe96f60) [FAB-16850](https://jira.hyperledger.org/browse/FAB-16850) Set up CI with Azure Pipelines
* [81aabf4](https://github.com/hyperledger/fabric-samples/commit/81aabf4) [FAB-16849](https://jira.hyperledger.org/browse/FAB-16849) Various updates for Java version of FabCar
* [a42b858](https://github.com/hyperledger/fabric-samples/commit/a42b858) Update FabCar to reflect wallet API changes
* [890f9ea](https://github.com/hyperledger/fabric-samples/commit/890f9ea) [FAB-16713](https://jira.hyperledger.org/browse/FAB-16713) Fix npm audit warnings
* [e48e804](https://github.com/hyperledger/fabric-samples/commit/e48e804) [FAB-16776](https://jira.hyperledger.org/browse/FAB-16776) Move BYFN up to V2_0 capabilities
* [7b65a25](https://github.com/hyperledger/fabric-samples/commit/7b65a25) [IN-68] Add default GitHub SECURITY policy
* [408e0e8](https://github.com/hyperledger/fabric-samples/commit/408e0e8) [FAB-16619](https://jira.hyperledger.org/browse/FAB-16619) Fix the policy warning
* [670d446](https://github.com/hyperledger/fabric-samples/commit/670d446) [FAB-16668](https://jira.hyperledger.org/browse/FAB-16668) fabcar chaincode modify console output
* [f2939e2](https://github.com/hyperledger/fabric-samples/commit/f2939e2) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for Commercial Paper sample
* [3d19014](https://github.com/hyperledger/fabric-samples/commit/3d19014) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for FabCar sample
* [e2b7cb7](https://github.com/hyperledger/fabric-samples/commit/e2b7cb7) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for abstore sample
* [db48612](https://github.com/hyperledger/fabric-samples/commit/db48612) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Increase chaincode execute timeout
* [521a7ff](https://github.com/hyperledger/fabric-samples/commit/521a7ff) [FAB-16607](https://jira.hyperledger.org/browse/FAB-16607) Update FabCar to reflect CC updates
* [c13a5ec](https://github.com/hyperledger/fabric-samples/commit/c13a5ec) [FAB-16528](https://jira.hyperledger.org/browse/FAB-16528) marbles private chaincode sync up
* [3fad853](https://github.com/hyperledger/fabric-samples/commit/3fad853) [FAB-16528](https://jira.hyperledger.org/browse/FAB-16528) marbles private chaincode sync up
* [8b9b82f](https://github.com/hyperledger/fabric-samples/commit/8b9b82f) [FAB-16489](https://jira.hyperledger.org/browse/FAB-16489) Add CODEOWNERS
* [a6ce915](https://github.com/hyperledger/fabric-samples/commit/a6ce915) [FAB-16487](https://jira.hyperledger.org/browse/FAB-16487) Update eslint
* [48082cf](https://github.com/hyperledger/fabric-samples/commit/48082cf) [FAB-16362](https://jira.hyperledger.org/browse/FAB-16362) adding chaincode excution comments
* [1d379f3](https://github.com/hyperledger/fabric-samples/commit/1d379f3) [FAB-16474](https://jira.hyperledger.org/browse/FAB-16474) marbles02 chaincode error
* [18712ca](https://github.com/hyperledger/fabric-samples/commit/18712ca) [FAB-16133](https://jira.hyperledger.org/browse/FAB-16133) Remove Solo consensus from BYFN
* [91c720a](https://github.com/hyperledger/fabric-samples/commit/91c720a) [FAB-16390](https://jira.hyperledger.org/browse/FAB-16390) Added filter for invalid transactions
* [1d3e267](https://github.com/hyperledger/fabric-samples/commit/1d3e267) Redirect samples to fabric-{chaincode,protos}-go
* [398a5b1](https://github.com/hyperledger/fabric-samples/commit/398a5b1) [FABCI-394] Remove AnsiColor Wrapper
* [ce154e0](https://github.com/hyperledger/fabric-samples/commit/ce154e0) [FAB-16310](https://jira.hyperledger.org/browse/FAB-16310) Vendor Go dependencies in all samples
* [6ea7c71](https://github.com/hyperledger/fabric-samples/commit/6ea7c71) [FAB-16285](https://jira.hyperledger.org/browse/FAB-16285) Update blacklisted versions in BYFN
* [86cd831](https://github.com/hyperledger/fabric-samples/commit/86cd831) [FAB-16284](https://jira.hyperledger.org/browse/FAB-16284) Remove E2E file and -f option from BYFN
* [0063abe](https://github.com/hyperledger/fabric-samples/commit/0063abe) Update stale script name in interest rate swaps
* [3907507](https://github.com/hyperledger/fabric-samples/commit/3907507) [FAB-16277](https://jira.hyperledger.org/browse/FAB-16277) Update BYFN w/ Raft ports in Docker network
* [33b0065](https://github.com/hyperledger/fabric-samples/commit/33b0065) [FAB-14813](https://jira.hyperledger.org/browse/FAB-14813) Channel event sample in fabric-samples
* [b62d5bd](https://github.com/hyperledger/fabric-samples/commit/b62d5bd) [FAB-16132](https://jira.hyperledger.org/browse/FAB-16132) Remove Kafka consensus from BYFN
* [9b14525](https://github.com/hyperledger/fabric-samples/commit/9b14525) [FAB-15213](https://jira.hyperledger.org/browse/FAB-15213) Update Commercial Paper for Java
* [4158877](https://github.com/hyperledger/fabric-samples/commit/4158877) [FAB-16232](https://jira.hyperledger.org/browse/FAB-16232) Remove FabToken sample
* [b6380cc](https://github.com/hyperledger/fabric-samples/commit/b6380cc) [FAB-16198](https://jira.hyperledger.org/browse/FAB-16198) Run "go mod vendor" for FabCar Go contract
* [639848a](https://github.com/hyperledger/fabric-samples/commit/639848a) [FAB-16197](https://jira.hyperledger.org/browse/FAB-16197) Add connection profiles to .gitignore
* [3996db5](https://github.com/hyperledger/fabric-samples/commit/3996db5) [FAB-12219](https://jira.hyperledger.org/browse/FAB-12219) abstore node -> javascript
* [14ac271](https://github.com/hyperledger/fabric-samples/commit/14ac271) [FAB-12219](https://jira.hyperledger.org/browse/FAB-12219) marbles02 node -> javascript
* [13f16e5](https://github.com/hyperledger/fabric-samples/commit/13f16e5) [FGJ-4] CI tests for FabCar Java sample
* [171a7d2](https://github.com/hyperledger/fabric-samples/commit/171a7d2) FGJ-4 Fabcar sample
* [868f9d0](https://github.com/hyperledger/fabric-samples/commit/868f9d0) [FAB-15625](https://jira.hyperledger.org/browse/FAB-15625) Add UT for Simple Asset Chaincode
* [597d150](https://github.com/hyperledger/fabric-samples/commit/597d150) [FAB-14532](https://jira.hyperledger.org/browse/FAB-14532) Remove LL FabCar sample
* [583ff8f](https://github.com/hyperledger/fabric-samples/commit/583ff8f) Use renamed CheckCommitReadiness function
* [750f937](https://github.com/hyperledger/fabric-samples/commit/750f937) [FAB-15213](https://jira.hyperledger.org/browse/FAB-15213) Add Java FabCar sample contract
* [abbda95](https://github.com/hyperledger/fabric-samples/commit/abbda95) [FAB-15897](https://jira.hyperledger.org/browse/FAB-15897) Improve FabCar test logging
* [dd8150a](https://github.com/hyperledger/fabric-samples/commit/dd8150a) [FAB-15104](https://jira.hyperledger.org/browse/FAB-15104) Remove versions from fabric-samples readme
* [1387aa8](https://github.com/hyperledger/fabric-samples/commit/1387aa8) [FAB-15927](https://jira.hyperledger.org/browse/FAB-15927)  Better expression for golang
* [61c33d3](https://github.com/hyperledger/fabric-samples/commit/61c33d3) [FAB-15973](https://jira.hyperledger.org/browse/FAB-15973) use --output json on simulatecommit
* [8bbdd0f](https://github.com/hyperledger/fabric-samples/commit/8bbdd0f) [FAB-15716](https://jira.hyperledger.org/browse/FAB-15716) Fix instructions for dev-mode
* [0254d67](https://github.com/hyperledger/fabric-samples/commit/0254d67) QueryApprovalStatus -> SimulateCommitChaincodeDef
* [c57d67c](https://github.com/hyperledger/fabric-samples/commit/c57d67c) [FAB-15782](https://jira.hyperledger.org/browse/FAB-15782) Sample Go CC should include deps
* [6ba5a19](https://github.com/hyperledger/fabric-samples/commit/6ba5a19) Update to Go 1.12.5 in ci.properties
* [1774a25](https://github.com/hyperledger/fabric-samples/commit/1774a25) [FAB-15723](https://jira.hyperledger.org/browse/FAB-15723) Fix script and instruction with ccenv
* [6ae711c](https://github.com/hyperledger/fabric-samples/commit/6ae711c) [FAB-15717](https://jira.hyperledger.org/browse/FAB-15717) fix Error Unexpected end of JSON input
* [5be56d3](https://github.com/hyperledger/fabric-samples/commit/5be56d3) [FAB-15104](https://jira.hyperledger.org/browse/FAB-15104) Remove scripts/bootstrap.sh
* [779f8f3](https://github.com/hyperledger/fabric-samples/commit/779f8f3) [FAB-15649](https://jira.hyperledger.org/browse/FAB-15649)Fix Fabcar to install Chaincode on all peers
* [7c5f5d3](https://github.com/hyperledger/fabric-samples/commit/7c5f5d3) [FAB-15199](https://jira.hyperledger.org/browse/FAB-15199) Update interest rate sample
* [f0dca20](https://github.com/hyperledger/fabric-samples/commit/f0dca20) [FAB-14532](https://jira.hyperledger.org/browse/FAB-14532) Remove LL FabCar sample
* [1ed1a10](https://github.com/hyperledger/fabric-samples/commit/1ed1a10) [FAB-15573](https://jira.hyperledger.org/browse/FAB-15573) Fix typo in fabric-samples-ci.md
* [2e7fec9](https://github.com/hyperledger/fabric-samples/commit/2e7fec9) [FAB-15601](https://jira.hyperledger.org/browse/FAB-15601) BYFN: Fix MAX_RETRY for couchdb
* [1e9e4c4](https://github.com/hyperledger/fabric-samples/commit/1e9e4c4) [FAB-9329](https://jira.hyperledger.org/browse/FAB-9329) Remove the unused variable in BYFN/EYFN
* [964c09f](https://github.com/hyperledger/fabric-samples/commit/964c09f) [FAB-15601](https://jira.hyperledger.org/browse/FAB-15601) BYFN: Fix MAX_RETRY for couchdb
* [41dca99](https://github.com/hyperledger/fabric-samples/commit/41dca99) [FAB-15127](https://jira.hyperledger.org/browse/FAB-15127) Update high throughput sample
* [3fe014a](https://github.com/hyperledger/fabric-samples/commit/3fe014a) Use official CouchDB image
* [f2d0fa0](https://github.com/hyperledger/fabric-samples/commit/f2d0fa0) [FAB-14487](https://jira.hyperledger.org/browse/FAB-14487) Make FabCar use BYFN, not basic-network
* [e9c3649](https://github.com/hyperledger/fabric-samples/commit/e9c3649) [FAB-15276](https://jira.hyperledger.org/browse/FAB-15276) Fix license statements
* [fbe4036](https://github.com/hyperledger/fabric-samples/commit/fbe4036) [FAB-14486](https://jira.hyperledger.org/browse/FAB-14486) Extend BYFN to opt skip chaincode deploy
* [0c4141f](https://github.com/hyperledger/fabric-samples/commit/0c4141f) [FAB-14485](https://jira.hyperledger.org/browse/FAB-14485) Extend BYFN to opt inc cert authorities
* [529b83b](https://github.com/hyperledger/fabric-samples/commit/529b83b) [FAB-14330](https://jira.hyperledger.org/browse/FAB-14330) Add connection profiles for BYFN and EYFN
* [2c21c83](https://github.com/hyperledger/fabric-samples/commit/2c21c83) [FABN-1184] Update fabtoken/README.md
* [5056a23](https://github.com/hyperledger/fabric-samples/commit/5056a23) [FABN-1184] Add CI script for fabtoken sample app
* [5d6db95](https://github.com/hyperledger/fabric-samples/commit/5d6db95) Update maintainers for fabric-samples
* [f527815](https://github.com/hyperledger/fabric-samples/commit/f527815) [FAB-15119](https://jira.hyperledger.org/browse/FAB-15119) Fix BYFN with Java chaincode
* [8245252](https://github.com/hyperledger/fabric-samples/commit/8245252) [FABN-1184] Implement fabtoken sample app
* [1bd1c2f](https://github.com/hyperledger/fabric-samples/commit/1bd1c2f) FABCI-284 Update CI Pipeline script
* [c24abf9](https://github.com/hyperledger/fabric-samples/commit/c24abf9) [FAB-15022](https://jira.hyperledger.org/browse/FAB-15022) Basic-network support for new lifecycle
* [b64fd45](https://github.com/hyperledger/fabric-samples/commit/b64fd45) [FAB-15051](https://jira.hyperledger.org/browse/FAB-15051) delStandard() function for high-throughput
* [3e68a7e](https://github.com/hyperledger/fabric-samples/commit/3e68a7e) [FAB-14784](https://jira.hyperledger.org/browse/FAB-14784) Remove balance-transfer
* [eb3fe08](https://github.com/hyperledger/fabric-samples/commit/eb3fe08) [FAB-14779](https://jira.hyperledger.org/browse/FAB-14779) QueryApprovalStatus step in byfn
* [2777429](https://github.com/hyperledger/fabric-samples/commit/2777429) [FAB-14711](https://jira.hyperledger.org/browse/FAB-14711) update byfn with new lifecycle
* [aec3389](https://github.com/hyperledger/fabric-samples/commit/aec3389) [FAB-12215](https://jira.hyperledger.org/browse/FAB-12215)WYFA:Remove chainId in tx proposal request
* [b5d5026](https://github.com/hyperledger/fabric-samples/commit/b5d5026) [FAB-14633](https://jira.hyperledger.org/browse/FAB-14633) Remove apt-get from eyfn.sh
* [efaadd3](https://github.com/hyperledger/fabric-samples/commit/efaadd3) [FAB-14531](https://jira.hyperledger.org/browse/FAB-14531) BYFN Raft with 5 nodes
* [d63047c](https://github.com/hyperledger/fabric-samples/commit/d63047c) [FAB-14444](https://jira.hyperledger.org/browse/FAB-14444)
* [7e3d428](https://github.com/hyperledger/fabric-samples/commit/7e3d428) [FAB-14369](https://jira.hyperledger.org/browse/FAB-14369)Fix dev mode failing to build Chaincode
* [420ba23](https://github.com/hyperledger/fabric-samples/commit/420ba23) [FAB-12762](https://jira.hyperledger.org/browse/FAB-12762) Add etcd/raft consensus option to BYFN
* [2b68c80](https://github.com/hyperledger/fabric-samples/commit/2b68c80) [FAB-14317](https://jira.hyperledger.org/browse/FAB-14317) Add default policies to org3
* [f942010](https://github.com/hyperledger/fabric-samples/commit/f942010) [FAB-14268](https://jira.hyperledger.org/browse/FAB-14268) Make BYFN/EYFN ports match external ports
* [4e2ce23](https://github.com/hyperledger/fabric-samples/commit/4e2ce23) [FAB-14271](https://jira.hyperledger.org/browse/FAB-14271) Add channel policies to channel config
* [f26477c](https://github.com/hyperledger/fabric-samples/commit/f26477c) [FAB-11796](https://jira.hyperledger.org/browse/FAB-11796)high-throughput:Remove unnecessary prunesafe
* [137327a](https://github.com/hyperledger/fabric-samples/commit/137327a) [FAB-14162](https://jira.hyperledger.org/browse/FAB-14162) Pin fabric-samples in master to "unstable"
* [6007c09](https://github.com/hyperledger/fabric-samples/commit/6007c09) [FAB-13862](https://jira.hyperledger.org/browse/FAB-13862) Rename example02 ABstore
* [94cb603](https://github.com/hyperledger/fabric-samples/commit/94cb603) [FAB-13933](https://jira.hyperledger.org/browse/FAB-13933) Fix misspellings
* [a8a5539](https://github.com/hyperledger/fabric-samples/commit/a8a5539) Fix doc link Fix variable error
* [b0cda61](https://github.com/hyperledger/fabric-samples/commit/b0cda61) [FAB-13769](https://jira.hyperledger.org/browse/FAB-13769) Add UT code to ABAC sample Chaincode
* [c7438e1](https://github.com/hyperledger/fabric-samples/commit/c7438e1) [FAB-13668](https://jira.hyperledger.org/browse/FAB-13668) BYFN's container volume mapping is bad
* [e48b2de](https://github.com/hyperledger/fabric-samples/commit/e48b2de) [FAB-13489](https://jira.hyperledger.org/browse/FAB-13489) fabric-samples add error msg
* [8a458b5](https://github.com/hyperledger/fabric-samples/commit/8a458b5) [FAB-12056](https://jira.hyperledger.org/browse/FAB-12056) Private marbles cc use transient data
* [6269941](https://github.com/hyperledger/fabric-samples/commit/6269941) Correct broken link
* [461b6ab](https://github.com/hyperledger/fabric-samples/commit/461b6ab) FABC-781 Remove fabric-ca sample
* [e9b9477](https://github.com/hyperledger/fabric-samples/commit/e9b9477) [FAB-13372](https://jira.hyperledger.org/browse/FAB-13372) Fabric-Samples return error msg
* [e3da220](https://github.com/hyperledger/fabric-samples/commit/e3da220) [FAB-13433](https://jira.hyperledger.org/browse/FAB-13433) - Update Jenkinsfile configuration
* [33db64e](https://github.com/hyperledger/fabric-samples/commit/33db64e) Configure Stale ProBot
* [5cd277f](https://github.com/hyperledger/fabric-samples/commit/5cd277f) [FAB-11951](https://jira.hyperledger.org/browse/FAB-11951) Interest-rate swap example for SBE
* [9567985](https://github.com/hyperledger/fabric-samples/commit/9567985) [FAB-13407](https://jira.hyperledger.org/browse/FAB-13407) Align fabric-samples with 1.4.0-rc2 release
* [c7572aa](https://github.com/hyperledger/fabric-samples/commit/c7572aa) [FAB-13305](https://jira.hyperledger.org/browse/FAB-13305) Update scripts to pull latest artifacts
* [ab46e35](https://github.com/hyperledger/fabric-samples/commit/ab46e35) [FAB-13283](https://jira.hyperledger.org/browse/FAB-13283) Update sample code for commercial paper
* [f677821](https://github.com/hyperledger/fabric-samples/commit/f677821) [FAB-13232](https://jira.hyperledger.org/browse/FAB-13232) fix peer node start command
* [6a7472e](https://github.com/hyperledger/fabric-samples/commit/6a7472e) [FAB-13126](https://jira.hyperledger.org/browse/FAB-13126) Align fabric-samples with 1.4.0-rc1 release
* [445ccbc](https://github.com/hyperledger/fabric-samples/commit/445ccbc) [FAB-12880](https://jira.hyperledger.org/browse/FAB-12880) Move old prog model samples for FabCar
* [5be62b5](https://github.com/hyperledger/fabric-samples/commit/5be62b5) [FAB-13207](https://jira.hyperledger.org/browse/FAB-13207) Remove incorrect discovery options
* [fdbd92d](https://github.com/hyperledger/fabric-samples/commit/fdbd92d) [FAB-13206](https://jira.hyperledger.org/browse/FAB-13206) Remove dependencies on fabric-client
* [eff0046](https://github.com/hyperledger/fabric-samples/commit/eff0046) [FAB-12877](https://jira.hyperledger.org/browse/FAB-12877) Add fabcar app using new prog model (JS)
* [c184196](https://github.com/hyperledger/fabric-samples/commit/c184196) [FAB-12878](https://jira.hyperledger.org/browse/FAB-12878) Add fabcar app using new prog model (TS)
* [e1a39e6](https://github.com/hyperledger/fabric-samples/commit/e1a39e6) [FAB-12724](https://jira.hyperledger.org/browse/FAB-12724) Upgrade from 1.3.x to 1.4.0
* [c21bbba](https://github.com/hyperledger/fabric-samples/commit/c21bbba) Update samples to use new logging env variables
* [7ad9f19](https://github.com/hyperledger/fabric-samples/commit/7ad9f19) [FAB-13011](https://jira.hyperledger.org/browse/FAB-13011) add kafka consensus type to byfn sample
* [33f064f](https://github.com/hyperledger/fabric-samples/commit/33f064f) [FAB-13170](https://jira.hyperledger.org/browse/FAB-13170) Add memberOnlyRead to marbles sample
* [928b72b](https://github.com/hyperledger/fabric-samples/commit/928b72b) [FAB-12875](https://jira.hyperledger.org/browse/FAB-12875) Add automated tests for fabcar sample
* [5c087f1](https://github.com/hyperledger/fabric-samples/commit/5c087f1) [FAB-13046](https://jira.hyperledger.org/browse/FAB-13046) Update TypeScript contract dependencies
* [3748983](https://github.com/hyperledger/fabric-samples/commit/3748983) [FAB-12879](https://jira.hyperledger.org/browse/FAB-12879) Update fabcar script for new contracts
* [4fb3b57](https://github.com/hyperledger/fabric-samples/commit/4fb3b57) [FAB-12852](https://jira.hyperledger.org/browse/FAB-12852) Add fabcar contract w/ new prog model (TS)
* [9facb42](https://github.com/hyperledger/fabric-samples/commit/9facb42) [FAB-12851](https://jira.hyperledger.org/browse/FAB-12851) Add fabcar contract w/ new prog model (JS)
* [e67fcf1](https://github.com/hyperledger/fabric-samples/commit/e67fcf1) [FAB-12322](https://jira.hyperledger.org/browse/FAB-12322) Update commercial-paper sample
* [fd6e2c4](https://github.com/hyperledger/fabric-samples/commit/fd6e2c4) [FAB-12703](https://jira.hyperledger.org/browse/FAB-12703) Fix misspelling "lauches"
* [c05f172](https://github.com/hyperledger/fabric-samples/commit/c05f172) [FAB-12608](https://jira.hyperledger.org/browse/FAB-12608) Update pipeline script
* [286861e](https://github.com/hyperledger/fabric-samples/commit/286861e) [FAB-12371](https://jira.hyperledger.org/browse/FAB-12371)Fix the abac sample to use new cid package
* [24c5e47](https://github.com/hyperledger/fabric-samples/commit/24c5e47) [FAB-12026](https://jira.hyperledger.org/browse/FAB-12026) pagination samples for node marbles02
* [6dc5ce5](https://github.com/hyperledger/fabric-samples/commit/6dc5ce5) [FAB-12587](https://jira.hyperledger.org/browse/FAB-12587) Fix for Query Block by block hash API
* [df311ce](https://github.com/hyperledger/fabric-samples/commit/df311ce) [FAB-12173](https://jira.hyperledger.org/browse/FAB-12173) balance-transfer: Update anchor peers
* [c925148](https://github.com/hyperledger/fabric-samples/commit/c925148) [FAB-12415](https://jira.hyperledger.org/browse/FAB-12415) samples for 1.3.0 (master cleanup)
* [3a12c60](https://github.com/hyperledger/fabric-samples/commit/3a12c60) [FAB-12275](https://jira.hyperledger.org/browse/FAB-12275) Fix the warn in creating genesis block
* [c6f6324](https://github.com/hyperledger/fabric-samples/commit/c6f6324) [FAB-12257](https://jira.hyperledger.org/browse/FAB-12257) allow balance-transfer for doscovery
* [4445e8d](https://github.com/hyperledger/fabric-samples/commit/4445e8d) [FAB-12272](https://jira.hyperledger.org/browse/FAB-12272) Increase MAX_RETRY to 10
* [33d333f](https://github.com/hyperledger/fabric-samples/commit/33d333f) [FAB-12190](https://jira.hyperledger.org/browse/FAB-12190) Update stable version in CI scripts
* [edee638](https://github.com/hyperledger/fabric-samples/commit/edee638) [FAB-12184](https://jira.hyperledger.org/browse/FAB-12184) Prepare fabric-samples for 1.3.0-rc1
* [514d456](https://github.com/hyperledger/fabric-samples/commit/514d456) [FAB-12170](https://jira.hyperledger.org/browse/FAB-12170) Fix dependency check in java chaincode
* [9f80e47](https://github.com/hyperledger/fabric-samples/commit/9f80e47) [FAB-12119](https://jira.hyperledger.org/browse/FAB-12119) Fix groupId in java chaincodes
* [3237229](https://github.com/hyperledger/fabric-samples/commit/3237229) [FAB-12073](https://jira.hyperledger.org/browse/FAB-12073) Fix Org3 peers CouchDB config.
* [eece3d8](https://github.com/hyperledger/fabric-samples/commit/eece3d8) [FAB-12106](https://jira.hyperledger.org/browse/FAB-12106) Update fabric-ca build scripts
* [f62952f](https://github.com/hyperledger/fabric-samples/commit/f62952f) [FABC-131] Change fabric-ca sample to build images
* [4089786](https://github.com/hyperledger/fabric-samples/commit/4089786) [FAB-11867](https://jira.hyperledger.org/browse/FAB-11867) Develop Apps:Sample pt 2 -- application
* [9ee57c6](https://github.com/hyperledger/fabric-samples/commit/9ee57c6) [FAB-11778](https://jira.hyperledger.org/browse/FAB-11778) Upgrade to v1.3.x from v1.2.x in byfn
* [e7a1b76](https://github.com/hyperledger/fabric-samples/commit/e7a1b76) [FAB-9386](https://jira.hyperledger.org/browse/FAB-9386) Remove Marbles sample reference to Fauxton
* [9c6acee](https://github.com/hyperledger/fabric-samples/commit/9c6acee) [FAB-12022](https://jira.hyperledger.org/browse/FAB-12022) Fix CI by increasing couchdb timeout
* [4030ebd](https://github.com/hyperledger/fabric-samples/commit/4030ebd) [FAB-11397](https://jira.hyperledger.org/browse/FAB-11397) Adding java cc
* [d776651](https://github.com/hyperledger/fabric-samples/commit/d776651) [FAB-11723](https://jira.hyperledger.org/browse/FAB-11723) Developing Apps: Sample pt 1 -- contract
* [cbbbc78](https://github.com/hyperledger/fabric-samples/commit/cbbbc78) [FAB-11577](https://jira.hyperledger.org/browse/FAB-11577) Fix balance transfer to install Chaincode
* [bfdc0b6](https://github.com/hyperledger/fabric-samples/commit/bfdc0b6) [FAB-11518](https://jira.hyperledger.org/browse/FAB-11518)
* [5930dfc](https://github.com/hyperledger/fabric-samples/commit/5930dfc) [FAB-11488](https://jira.hyperledger.org/browse/FAB-11488) Update CI script
* [ca6959c](https://github.com/hyperledger/fabric-samples/commit/ca6959c) [FAB-11311](https://jira.hyperledger.org/browse/FAB-11311) Update fabric image version
* [0ca9e6e](https://github.com/hyperledger/fabric-samples/commit/0ca9e6e) FABN-833 Update Jenkinsfile to capture build artifacts
* [a4a15cb](https://github.com/hyperledger/fabric-samples/commit/a4a15cb) [FAB-11220](https://jira.hyperledger.org/browse/FAB-11220) Samples - remove EventHub
* [c4bdc68](https://github.com/hyperledger/fabric-samples/commit/c4bdc68) [FAB-8479](https://jira.hyperledger.org/browse/FAB-8479) Added Endorsement policy
* [6edd320](https://github.com/hyperledger/fabric-samples/commit/6edd320) [FAB-9297](https://jira.hyperledger.org/browse/FAB-9297) fix README links and update bootstrap.sh
* [75e2931](https://github.com/hyperledger/fabric-samples/commit/75e2931) [FAB-10811](https://jira.hyperledger.org/browse/FAB-10811) fabric-ca sample is broken on v1.2
* [ad40e29](https://github.com/hyperledger/fabric-samples/commit/ad40e29) [FAB-10732](https://jira.hyperledger.org/browse/FAB-10732) BYFN upgrade to v1.2
* [20ad472](https://github.com/hyperledger/fabric-samples/commit/20ad472) [FAB-10801](https://jira.hyperledger.org/browse/FAB-10801) format and styling for shell artifacts
* [e95210e](https://github.com/hyperledger/fabric-samples/commit/e95210e) [FAB-10074](https://jira.hyperledger.org/browse/FAB-10074) CI Script for pipeline project type
* [5956178](https://github.com/hyperledger/fabric-samples/commit/5956178) [FAB-6600](https://jira.hyperledger.org/browse/FAB-6600) Sample chaincode for private data
* [21444ab](https://github.com/hyperledger/fabric-samples/commit/21444ab) [FAB-10297](https://jira.hyperledger.org/browse/FAB-10297) replace fabric-preload.sh
* [3c32c52](https://github.com/hyperledger/fabric-samples/commit/3c32c52) [FAB-10346](https://jira.hyperledger.org/browse/FAB-10346) Ensure peers are in sync in eyfn
* [2f30504](https://github.com/hyperledger/fabric-samples/commit/2f30504) [FAB-10340](https://jira.hyperledger.org/browse/FAB-10340) Fix broken link in chaincode-docker-devmode
* [a603655](https://github.com/hyperledger/fabric-samples/commit/a603655) [FAB-10306](https://jira.hyperledger.org/browse/FAB-10306) Fix config in balance-transfer
* [1cd059e](https://github.com/hyperledger/fabric-samples/commit/1cd059e) [FAB-8612](https://jira.hyperledger.org/browse/FAB-8612) Cleanup eyfn/byfn with --remove-orphans
* [3031a8c](https://github.com/hyperledger/fabric-samples/commit/3031a8c) [FAB-10235](https://jira.hyperledger.org/browse/FAB-10235) Update BYFN to use V1_2 capability
* [5cf1944](https://github.com/hyperledger/fabric-samples/commit/5cf1944) [FAB-10185](https://jira.hyperledger.org/browse/FAB-10185) Reorder for logical consistency
* [e2f4f20](https://github.com/hyperledger/fabric-samples/commit/e2f4f20) [FAB-10176](https://jira.hyperledger.org/browse/FAB-10176) Fix invalid configtx.yaml documents
* [cad2b98](https://github.com/hyperledger/fabric-samples/commit/cad2b98) [FAB-10073](https://jira.hyperledger.org/browse/FAB-10073) Fix broken link in samples/fabric-ca/README
* [ace2e5a](https://github.com/hyperledger/fabric-samples/commit/ace2e5a) [FAB-10021](https://jira.hyperledger.org/browse/FAB-10021) Typo in balance-transfer/testAPIs.sh
* [fa1d899](https://github.com/hyperledger/fabric-samples/commit/fa1d899) [FAB-9893](https://jira.hyperledger.org/browse/FAB-9893) Add .gitreview file for quicker Gerrit setup
* [98561e0](https://github.com/hyperledger/fabric-samples/commit/98561e0) [FAB-9552](https://jira.hyperledger.org/browse/FAB-9552) Fix access of an un-declared variable
* [b5dad8c](https://github.com/hyperledger/fabric-samples/commit/b5dad8c) [FAB-9317](https://jira.hyperledger.org/browse/FAB-9317) Update first-network with multi endorse
* [146f7bc](https://github.com/hyperledger/fabric-samples/commit/146f7bc) [FAB-9572](https://jira.hyperledger.org/browse/FAB-9572) Fix README to match new version of BYFN
* [010fcb5](https://github.com/hyperledger/fabric-samples/commit/010fcb5) [FAB-9326](https://jira.hyperledger.org/browse/FAB-9326) Clean up byfn.sh, drop "-m" option
* [2d6386c](https://github.com/hyperledger/fabric-samples/commit/2d6386c) [FAB-8245](https://jira.hyperledger.org/browse/FAB-8245) change first network according to the fix
* [3a5108f](https://github.com/hyperledger/fabric-samples/commit/3a5108f) [FAB-9475](https://jira.hyperledger.org/browse/FAB-9475) Fix a dead link in README
* [77a6568](https://github.com/hyperledger/fabric-samples/commit/77a6568) [FAB-9294](https://jira.hyperledger.org/browse/FAB-9294) eliminate excess noise in BYFN
* [41f5ab8](https://github.com/hyperledger/fabric-samples/commit/41f5ab8) [FAB-9406](https://jira.hyperledger.org/browse/FAB-9406) Typo in byfn.sh
* [f5c2eb8](https://github.com/hyperledger/fabric-samples/commit/f5c2eb8) [FAB-9362](https://jira.hyperledger.org/browse/FAB-9362) add CONTRIBUTING.md and CODE_OF_CONDUCT.md
* [9d518fb](https://github.com/hyperledger/fabric-samples/commit/9d518fb) [FAB-9330](https://jira.hyperledger.org/browse/FAB-9330) Refactor top-level .gitignore into subdirs
* [a080da3](https://github.com/hyperledger/fabric-samples/commit/a080da3) [FAB-8958](https://jira.hyperledger.org/browse/FAB-8958) Add org3 removal to byfn.sh
* [8fc0865](https://github.com/hyperledger/fabric-samples/commit/8fc0865) [FAB-9185](https://jira.hyperledger.org/browse/FAB-9185) add /config to .gitignore
* [680ff01](https://github.com/hyperledger/fabric-samples/commit/680ff01) [FAB-8600](https://jira.hyperledger.org/browse/FAB-8600)Clear hyperledger-related containers only
* [4f97717](https://github.com/hyperledger/fabric-samples/commit/4f97717) [FAB-8947](https://jira.hyperledger.org/browse/FAB-8947) Fabric-Samples remove package lock
* [fcf62ad](https://github.com/hyperledger/fabric-samples/commit/fcf62ad) [FAB-8265](https://jira.hyperledger.org/browse/FAB-8265) Fixed spelling error in registerUser
* [e4d7760](https://github.com/hyperledger/fabric-samples/commit/e4d7760) [ [FAB-8730](https://jira.hyperledger.org/browse/FAB-8730) ] hyphen breaks fabric-samples
* [2bbb0a8](https://github.com/hyperledger/fabric-samples/commit/2bbb0a8) [FAB-8630](https://jira.hyperledger.org/browse/FAB-8630) byfn failing intermittently in CI
* [823fb6b](https://github.com/hyperledger/fabric-samples/commit/823fb6b) [ [FAB-8679](https://jira.hyperledger.org/browse/FAB-8679) ] Permit samples to use RootCAs
* [9f9fc7e](https://github.com/hyperledger/fabric-samples/commit/9f9fc7e) [FAB-8633](https://jira.hyperledger.org/browse/FAB-8633) Correct revoked error check
* [f3b55c9](https://github.com/hyperledger/fabric-samples/commit/f3b55c9) [FAB-8621](https://jira.hyperledger.org/browse/FAB-8621) Remove Marbles index json data wrapper
* [f110a6e](https://github.com/hyperledger/fabric-samples/commit/f110a6e) [FAB-8602](https://jira.hyperledger.org/browse/FAB-8602) Add volumes to first-network e2e yaml
* [7362928](https://github.com/hyperledger/fabric-samples/commit/7362928) [FAB-8567](https://jira.hyperledger.org/browse/FAB-8567) Alt: Always use volumes for ledger (m)
* [afb3d62](https://github.com/hyperledger/fabric-samples/commit/afb3d62) [FAB-8561](https://jira.hyperledger.org/browse/FAB-8561) Add note to readthedocs link in README
* [10526d5](https://github.com/hyperledger/fabric-samples/commit/10526d5) [FAB-8564](https://jira.hyperledger.org/browse/FAB-8564) add debug commands to byfn
* [e73a481](https://github.com/hyperledger/fabric-samples/commit/e73a481) [FAB-8568](https://jira.hyperledger.org/browse/FAB-8568) BYFN: Fix IMAGE_TAG for couchdb
* [ffd7a25](https://github.com/hyperledger/fabric-samples/commit/ffd7a25) [FAB-6400](https://jira.hyperledger.org/browse/FAB-6400) Balance-transfer filtered events
* [cba57da](https://github.com/hyperledger/fabric-samples/commit/cba57da) [FAB-8165](https://jira.hyperledger.org/browse/FAB-8165) Adding upgrade function to byfn
* [c6166d6](https://github.com/hyperledger/fabric-samples/commit/c6166d6) [FAB-8540](https://jira.hyperledger.org/browse/FAB-8540) Add ledger persistance to first-network
* [77e74b7](https://github.com/hyperledger/fabric-samples/commit/77e74b7) [FAB-8497](https://jira.hyperledger.org/browse/FAB-8497) Download images required for fabric-ca
* [2bed1ef](https://github.com/hyperledger/fabric-samples/commit/2bed1ef) [FAB-8539](https://jira.hyperledger.org/browse/FAB-8539) Add version checking to first-network
* [7b7fc09](https://github.com/hyperledger/fabric-samples/commit/7b7fc09) [FAB-8496](https://jira.hyperledger.org/browse/FAB-8496) allow modification of affiliations
* [981efba](https://github.com/hyperledger/fabric-samples/commit/981efba) [FAB-8503](https://jira.hyperledger.org/browse/FAB-8503) Prevent CLI container from exiting
* [c93268f](https://github.com/hyperledger/fabric-samples/commit/c93268f) [FAB-8518](https://jira.hyperledger.org/browse/FAB-8518) Not all compose files have IMAGE_TAG
* [4f2cd8d](https://github.com/hyperledger/fabric-samples/commit/4f2cd8d) [FAB-8445](https://jira.hyperledger.org/browse/FAB-8445) Adding IMAGE_TAG option to byfn
* [ca80163](https://github.com/hyperledger/fabric-samples/commit/ca80163) [FAB-8387](https://jira.hyperledger.org/browse/FAB-8387) Add gencrl to revoke command
* [e379ac5](https://github.com/hyperledger/fabric-samples/commit/e379ac5) [FAB-8407](https://jira.hyperledger.org/browse/FAB-8407) Fix TLS bad cert error
* [02ca1dc](https://github.com/hyperledger/fabric-samples/commit/02ca1dc) [FAB-7494](https://jira.hyperledger.org/browse/FAB-7494) Fix bootstrap peer config
* [41e144f](https://github.com/hyperledger/fabric-samples/commit/41e144f) [FAB-8386](https://jira.hyperledger.org/browse/FAB-8386) eyfn fails to execute invoke on org3
* [4ab098f](https://github.com/hyperledger/fabric-samples/commit/4ab098f) [FAB-8327](https://jira.hyperledger.org/browse/FAB-8327) Change eyfn.sh to use configtxlator cli
* [24f35c1](https://github.com/hyperledger/fabric-samples/commit/24f35c1) [FAB-7750](https://jira.hyperledger.org/browse/FAB-7750) first network with support to [FAB-5664](https://jira.hyperledger.org/browse/FAB-5664)
* [305d6f4](https://github.com/hyperledger/fabric-samples/commit/305d6f4) [FAB-8238](https://jira.hyperledger.org/browse/FAB-8238)wrong orderer/peer type in fabric-ca sam
* [1d69e9e](https://github.com/hyperledger/fabric-samples/commit/1d69e9e) [FAB-7540](https://jira.hyperledger.org/browse/FAB-7540) Simplifies Reconfigure Your Network tutorial
* [0daa8bc](https://github.com/hyperledger/fabric-samples/commit/0daa8bc) [FAB-8122](https://jira.hyperledger.org/browse/FAB-8122) Updated README.md file- balancetransfer
* [652f074](https://github.com/hyperledger/fabric-samples/commit/652f074) [FAB-7342](https://jira.hyperledger.org/browse/FAB-7342) Enable client auth in fabric-ca sample
* [90c2bfd](https://github.com/hyperledger/fabric-samples/commit/90c2bfd) [FAB-6934](https://jira.hyperledger.org/browse/FAB-6934) fix devmode sample for v1.1
* [2c0c2c7](https://github.com/hyperledger/fabric-samples/commit/2c0c2c7) [FAB-7910](https://jira.hyperledger.org/browse/FAB-7910) Clean up chaincode containers in fabric-ca
* [bbee1b2](https://github.com/hyperledger/fabric-samples/commit/bbee1b2) [FAB-7908](https://jira.hyperledger.org/browse/FAB-7908) Change hf.admin attr to admin
* [dd12f88](https://github.com/hyperledger/fabric-samples/commit/dd12f88) [FAB-7834](https://jira.hyperledger.org/browse/FAB-7834) Add couchdb index to marbles02 sample
* [25f6091](https://github.com/hyperledger/fabric-samples/commit/25f6091) [FAB-7836](https://jira.hyperledger.org/browse/FAB-7836) Fix "No identity type provided" Error
* [5eb2fb2](https://github.com/hyperledger/fabric-samples/commit/5eb2fb2) [FAB-7653](https://jira.hyperledger.org/browse/FAB-7653) Fix incorrect chaincode location
* [5a974a4](https://github.com/hyperledger/fabric-samples/commit/5a974a4) [FAB-7533](https://jira.hyperledger.org/browse/FAB-7533) Fix typo in balance-transfer sample
* [038c496](https://github.com/hyperledger/fabric-samples/commit/038c496) [FAB-7592](https://jira.hyperledger.org/browse/FAB-7592) Give hf.Registrar attrs to admins
* [cf79cd1](https://github.com/hyperledger/fabric-samples/commit/cf79cd1) [FAB-7584](https://jira.hyperledger.org/browse/FAB-7584) Removes copy of creds to keystore
* [a0c1687](https://github.com/hyperledger/fabric-samples/commit/a0c1687) [FAB-7487](https://jira.hyperledger.org/browse/FAB-7487) Fix typo in node/fabcar.js
* [1883ae9](https://github.com/hyperledger/fabric-samples/commit/1883ae9) [FAB-7527](https://jira.hyperledger.org/browse/FAB-7527) Improves BYFN
* [e848216](https://github.com/hyperledger/fabric-samples/commit/e848216) [FAB-7511](https://jira.hyperledger.org/browse/FAB-7511) clear crypto material after clearing network
* [54ffa5f](https://github.com/hyperledger/fabric-samples/commit/54ffa5f) [FAB-7241](https://jira.hyperledger.org/browse/FAB-7241) Fix chaincode-devmode
* [c446510](https://github.com/hyperledger/fabric-samples/commit/c446510) [FAB-6550](https://jira.hyperledger.org/browse/FAB-6550) Sample app written in typescript
* [69a127e](https://github.com/hyperledger/fabric-samples/commit/69a127e) [FAB-6254](https://jira.hyperledger.org/browse/FAB-6254) Fix the default CLI timeout
* [7428f64](https://github.com/hyperledger/fabric-samples/commit/7428f64) [FAB-7160](https://jira.hyperledger.org/browse/FAB-7160) Samples - readme sample commands
* [2474704](https://github.com/hyperledger/fabric-samples/commit/2474704) [FAB-6967](https://jira.hyperledger.org/browse/FAB-6967) Added steps to query by a revoked user
* [948e237](https://github.com/hyperledger/fabric-samples/commit/948e237) [FAB-5913](https://jira.hyperledger.org/browse/FAB-5913)balance-transfer:Fix query response message
* [7b76a7a](https://github.com/hyperledger/fabric-samples/commit/7b76a7a) [FAB-6632](https://jira.hyperledger.org/browse/FAB-6632) - Artifacts for BYFN reconfigure
* [24ef9da](https://github.com/hyperledger/fabric-samples/commit/24ef9da) [FAB-6902](https://jira.hyperledger.org/browse/FAB-6902) [FAB-6904](https://jira.hyperledger.org/browse/FAB-6904) correct the sample for FAB-6904
* [fd795d2](https://github.com/hyperledger/fabric-samples/commit/fd795d2) [FAB-6745](https://jira.hyperledger.org/browse/FAB-6745) Fix timing issue in sample
* [a7be462](https://github.com/hyperledger/fabric-samples/commit/a7be462) [FAB-6895](https://jira.hyperledger.org/browse/FAB-6895) chaincode mounting issue
* [7c23985](https://github.com/hyperledger/fabric-samples/commit/7c23985) [FAB-5221](https://jira.hyperledger.org/browse/FAB-5221) generateCerts should delete crypto
* [1961835](https://github.com/hyperledger/fabric-samples/commit/1961835) [FAB-6870](https://jira.hyperledger.org/browse/FAB-6870) Update node modules versions
* [bb3ac84](https://github.com/hyperledger/fabric-samples/commit/bb3ac84) [FAB-5363](https://jira.hyperledger.org/browse/FAB-5363) fabric-samples update balance
* [6b2799e](https://github.com/hyperledger/fabric-samples/commit/6b2799e) [FAB-6568](https://jira.hyperledger.org/browse/FAB-6568) Fabric-Samples - update fabcar
* [fafae55](https://github.com/hyperledger/fabric-samples/commit/fafae55) [FAB-6779](https://jira.hyperledger.org/browse/FAB-6779) Fix the error in fabric-ca
* [caf5c33](https://github.com/hyperledger/fabric-samples/commit/caf5c33) [FAB-6050](https://jira.hyperledger.org/browse/FAB-6050) Adding fabric-ca sample
* [44c204d](https://github.com/hyperledger/fabric-samples/commit/44c204d) [FAB-5898](https://jira.hyperledger.org/browse/FAB-5898) porting samples to node.js chaincode
* [07a07d5](https://github.com/hyperledger/fabric-samples/commit/07a07d5) [FAB-6361](https://jira.hyperledger.org/browse/FAB-6361) Update license text in README
* [77b4090](https://github.com/hyperledger/fabric-samples/commit/77b4090) [FAB-5992](https://jira.hyperledger.org/browse/FAB-5992) Fix error in first-network dir

## "v1.0.6"

* [11e4c23](https://github.com/hyperledger/fabric-samples/commit/11e4c23) Update samples to use v2.0 or later dependencies (#161)
* [94beab7](https://github.com/hyperledger/fabric-samples/commit/94beab7) FABN-1531 Use v2.1.0 sdk-node modules
* [8820d2f](https://github.com/hyperledger/fabric-samples/commit/8820d2f) Fix commercial-paper README
* [aa9b577](https://github.com/hyperledger/fabric-samples/commit/aa9b577) Remove TLS enabled switch (#155)
* [381fb46](https://github.com/hyperledger/fabric-samples/commit/381fb46) upgraded abstore golang chaincode to use contract-api package (#154)
* [5e5d2c8](https://github.com/hyperledger/fabric-samples/commit/5e5d2c8) Update java chaincode to be compatible with doc and other implementations (#149)
* [c572c51](https://github.com/hyperledger/fabric-samples/commit/c572c51) Organize and Standardize `ci` Directory Content (#152)
* [aa40963](https://github.com/hyperledger/fabric-samples/commit/aa40963) Perform General Cleanup (#151)
* [da41afa](https://github.com/hyperledger/fabric-samples/commit/da41afa) Remove left over rm -rf command from BYFN (#148)
* [4bb48a9](https://github.com/hyperledger/fabric-samples/commit/4bb48a9) Jenkins no longer used (#145)
* [6f984e1](https://github.com/hyperledger/fabric-samples/commit/6f984e1) Bump acorn from 6.4.0 to 6.4.1 in /fabcar/javascript (#144)
* [b155620](https://github.com/hyperledger/fabric-samples/commit/b155620) Remove redundant invoke command from test network (#142)
* [851933b](https://github.com/hyperledger/fabric-samples/commit/851933b) Add enrollUser files to commercial paper (#140)
* [87600bd](https://github.com/hyperledger/fabric-samples/commit/87600bd) [FAB-17268](https://jira.hyperledger.org/browse/FAB-17268) Move fabcar sample to test network (#103)
* [9397788](https://github.com/hyperledger/fabric-samples/commit/9397788) Wrong groupId on hyperledger fabric dependencies for java-application (#134)
* [92555fb](https://github.com/hyperledger/fabric-samples/commit/92555fb) Update README.md (#133)
* [59c6641](https://github.com/hyperledger/fabric-samples/commit/59c6641) Change Download Location of Fabric Binaries (#143)
* [1f283fc](https://github.com/hyperledger/fabric-samples/commit/1f283fc) init function does not exist on fabcar (#141)
* [defb6bb](https://github.com/hyperledger/fabric-samples/commit/defb6bb) [FAB-17656](https://jira.hyperledger.org/browse/FAB-17656) echo Generating channel.tx (#139)
* [4c7bab0](https://github.com/hyperledger/fabric-samples/commit/4c7bab0) fix: package seletor REGEX (#135)
* [db69c6f](https://github.com/hyperledger/fabric-samples/commit/db69c6f) Add fabcar external service sample (#136)
* [7f5f5e6](https://github.com/hyperledger/fabric-samples/commit/7f5f5e6) [FAB-17504](https://jira.hyperledger.org/browse/FAB-17504) add Organizations.<Org>.OrdererEndpoints and remove Orderer.Addresses (#125)
* [f3fc08d](https://github.com/hyperledger/fabric-samples/commit/f3fc08d) Remove solo and kafka from test net configtx.yaml (#137)
* [e17574d](https://github.com/hyperledger/fabric-samples/commit/e17574d) Add CA's to docker test network (#124)
* [faac18e](https://github.com/hyperledger/fabric-samples/commit/faac18e) [FAB-17461](https://jira.hyperledger.org/browse/FAB-17461) Move off_chain_data sample to test network (#122)
* [121a44a](https://github.com/hyperledger/fabric-samples/commit/121a44a) [FAB-17460](https://jira.hyperledger.org/browse/FAB-17460) Move High Throughput sample to test network (#112)
* [a2f3a66](https://github.com/hyperledger/fabric-samples/commit/a2f3a66) Update docker image version
* [e5b898c](https://github.com/hyperledger/fabric-samples/commit/e5b898c) Revert "first-network/scripts/*: Make Chaincode name configurable (#118)" (#131)
* [9ef61e2](https://github.com/hyperledger/fabric-samples/commit/9ef61e2) first-network/scripts/*: Make Chaincode name configurable (#118)
* [e204ebb](https://github.com/hyperledger/fabric-samples/commit/e204ebb) Remove reference to 2.0 beta (#111)
* [3dbe116](https://github.com/hyperledger/fabric-samples/commit/3dbe116) [FAB-17456](https://jira.hyperledger.org/browse/FAB-17456) fabric-samples read ccp (#117)
* [965ed1f](https://github.com/hyperledger/fabric-samples/commit/965ed1f) [FAB-17498](https://jira.hyperledger.org/browse/FAB-17498) Beta Images removal, test test-network (#121)
* [403019e](https://github.com/hyperledger/fabric-samples/commit/403019e) [FAB-17495](https://jira.hyperledger.org/browse/FAB-17495) Remove Basic Network sample (#120)
* [883ef99](https://github.com/hyperledger/fabric-samples/commit/883ef99) [FAB-17457](https://jira.hyperledger.org/browse/FAB-17457) Script correction (#119)
* [b89ee34](https://github.com/hyperledger/fabric-samples/commit/b89ee34) Update Commercial Paper to v2.0 Lifecycle (#109)
* [4208644](https://github.com/hyperledger/fabric-samples/commit/4208644) [FAB-17478](https://jira.hyperledger.org/browse/FAB-17478) Update commercial paper to use go api v1.0.0 (#115)
* [0df5ed9](https://github.com/hyperledger/fabric-samples/commit/0df5ed9) [FAB-17477](https://jira.hyperledger.org/browse/FAB-17477) Update fabcar to use go api v1.0.0 (#116)
* [571733f](https://github.com/hyperledger/fabric-samples/commit/571733f) [FAB-17447](https://jira.hyperledger.org/browse/FAB-17447) Update to 2.0.0 Libraries
* [67b4ee7](https://github.com/hyperledger/fabric-samples/commit/67b4ee7) Add Org3 bugs in test network (#108)
* [5b93dd0](https://github.com/hyperledger/fabric-samples/commit/5b93dd0) [FAB-17140](https://jira.hyperledger.org/browse/FAB-17140) Add go commercial paper contract (#102)
* [4fe6a25](https://github.com/hyperledger/fabric-samples/commit/4fe6a25) [FABCI-482] Update Nexus URL's to Artifactory (#92)
* [1488fbb](https://github.com/hyperledger/fabric-samples/commit/1488fbb) Add 1.x versions of fabric to blacklisted versions
* [8ca279d](https://github.com/hyperledger/fabric-samples/commit/8ca279d) Add Support for Versioning NodeJS (#106)
* [b3b5267](https://github.com/hyperledger/fabric-samples/commit/b3b5267) [FAB-17243](https://jira.hyperledger.org/browse/FAB-17243) Add support for Fabric CA for Org3 on the (#91)
* [ce41ff7](https://github.com/hyperledger/fabric-samples/commit/ce41ff7) Remove references to vendoring chaincode from your gopath (#96)
* [4235d30](https://github.com/hyperledger/fabric-samples/commit/4235d30) [FAB-17306](https://jira.hyperledger.org/browse/FAB-17306) Fix artifact names in test-network (#97)
* [4c2a0a4](https://github.com/hyperledger/fabric-samples/commit/4c2a0a4) [FAB-16147](https://jira.hyperledger.org/browse/FAB-16147) Update Commercial Paper to work with v2 (#98)
* [6d9fd6f](https://github.com/hyperledger/fabric-samples/commit/6d9fd6f) Remove Gerrit reference
* [a026a4f](https://github.com/hyperledger/fabric-samples/commit/a026a4f) Fixed typo (#90)
* [cdb0e8b](https://github.com/hyperledger/fabric-samples/commit/cdb0e8b) TYPO (#89)
* [94ac8b6](https://github.com/hyperledger/fabric-samples/commit/94ac8b6) Update to use beta levels of modules (#88)
* [d848633](https://github.com/hyperledger/fabric-samples/commit/d848633) [FAB-16844](https://jira.hyperledger.org/browse/FAB-16844) Correct BYFN CC name
* [73267e1](https://github.com/hyperledger/fabric-samples/commit/73267e1) Fix test network bugs for adding org3
* [5d58254](https://github.com/hyperledger/fabric-samples/commit/5d58254) [FAB-17145](https://jira.hyperledger.org/browse/FAB-17145) Add test network to Fabric Samples
* [e9f2957](https://github.com/hyperledger/fabric-samples/commit/e9f2957) [FAB-17062](https://jira.hyperledger.org/browse/FAB-17062) Fix typos in Commercial Paper readme
* [36694d0](https://github.com/hyperledger/fabric-samples/commit/36694d0) [FAB-17121](https://jira.hyperledger.org/browse/FAB-17121) Use new bootstrap config in orderer
* [429f087](https://github.com/hyperledger/fabric-samples/commit/429f087) update fabcar go to new programming model
* [1467086](https://github.com/hyperledger/fabric-samples/commit/1467086) Bump eslint-utils
* [33f349a](https://github.com/hyperledger/fabric-samples/commit/33f349a) Remove Stalebot
* [6af43bf](https://github.com/hyperledger/fabric-samples/commit/6af43bf) Change stalebot settings
* [4880401](https://github.com/hyperledger/fabric-samples/commit/4880401) [FAB-16284](https://jira.hyperledger.org/browse/FAB-16284) Remove E2E file and -f option from BYFN
* [fe96f60](https://github.com/hyperledger/fabric-samples/commit/fe96f60) [FAB-16850](https://jira.hyperledger.org/browse/FAB-16850) Set up CI with Azure Pipelines
* [81aabf4](https://github.com/hyperledger/fabric-samples/commit/81aabf4) [FAB-16849](https://jira.hyperledger.org/browse/FAB-16849) Various updates for Java version of FabCar
* [a42b858](https://github.com/hyperledger/fabric-samples/commit/a42b858) Update FabCar to reflect wallet API changes
* [890f9ea](https://github.com/hyperledger/fabric-samples/commit/890f9ea) [FAB-16713](https://jira.hyperledger.org/browse/FAB-16713) Fix npm audit warnings
* [e48e804](https://github.com/hyperledger/fabric-samples/commit/e48e804) [FAB-16776](https://jira.hyperledger.org/browse/FAB-16776) Move BYFN up to V2_0 capabilities
* [7b65a25](https://github.com/hyperledger/fabric-samples/commit/7b65a25) [IN-68] Add default GitHub SECURITY policy
* [408e0e8](https://github.com/hyperledger/fabric-samples/commit/408e0e8) [FAB-16619](https://jira.hyperledger.org/browse/FAB-16619) Fix the policy warning
* [670d446](https://github.com/hyperledger/fabric-samples/commit/670d446) [FAB-16668](https://jira.hyperledger.org/browse/FAB-16668) fabcar chaincode modify console output
* [f2939e2](https://github.com/hyperledger/fabric-samples/commit/f2939e2) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for Commercial Paper sample
* [3d19014](https://github.com/hyperledger/fabric-samples/commit/3d19014) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for FabCar sample
* [e2b7cb7](https://github.com/hyperledger/fabric-samples/commit/e2b7cb7) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Java 11 support for abstore sample
* [db48612](https://github.com/hyperledger/fabric-samples/commit/db48612) [FAB-6415](https://jira.hyperledger.org/browse/FAB-6415) Increase chaincode execute timeout
* [521a7ff](https://github.com/hyperledger/fabric-samples/commit/521a7ff) [FAB-16607](https://jira.hyperledger.org/browse/FAB-16607) Update FabCar to reflect CC updates
* [c13a5ec](https://github.com/hyperledger/fabric-samples/commit/c13a5ec) [FAB-16528](https://jira.hyperledger.org/browse/FAB-16528) marbles private chaincode sync up
* [3fad853](https://github.com/hyperledger/fabric-samples/commit/3fad853) [FAB-16528](https://jira.hyperledger.org/browse/FAB-16528) marbles private chaincode sync up
* [8b9b82f](https://github.com/hyperledger/fabric-samples/commit/8b9b82f) [FAB-16489](https://jira.hyperledger.org/browse/FAB-16489) Add CODEOWNERS
* [a6ce915](https://github.com/hyperledger/fabric-samples/commit/a6ce915) [FAB-16487](https://jira.hyperledger.org/browse/FAB-16487) Update eslint
* [48082cf](https://github.com/hyperledger/fabric-samples/commit/48082cf) [FAB-16362](https://jira.hyperledger.org/browse/FAB-16362) adding chaincode excution comments
* [1d379f3](https://github.com/hyperledger/fabric-samples/commit/1d379f3) [FAB-16474](https://jira.hyperledger.org/browse/FAB-16474) marbles02 chaincode error
* [18712ca](https://github.com/hyperledger/fabric-samples/commit/18712ca) [FAB-16133](https://jira.hyperledger.org/browse/FAB-16133) Remove Solo consensus from BYFN
* [91c720a](https://github.com/hyperledger/fabric-samples/commit/91c720a) [FAB-16390](https://jira.hyperledger.org/browse/FAB-16390) Added filter for invalid transactions
* [1d3e267](https://github.com/hyperledger/fabric-samples/commit/1d3e267) Redirect samples to fabric-{chaincode,protos}-go
* [398a5b1](https://github.com/hyperledger/fabric-samples/commit/398a5b1) [FABCI-394] Remove AnsiColor Wrapper
* [ce154e0](https://github.com/hyperledger/fabric-samples/commit/ce154e0) [FAB-16310](https://jira.hyperledger.org/browse/FAB-16310) Vendor Go dependencies in all samples
* [6ea7c71](https://github.com/hyperledger/fabric-samples/commit/6ea7c71) [FAB-16285](https://jira.hyperledger.org/browse/FAB-16285) Update blacklisted versions in BYFN
* [86cd831](https://github.com/hyperledger/fabric-samples/commit/86cd831) [FAB-16284](https://jira.hyperledger.org/browse/FAB-16284) Remove E2E file and -f option from BYFN
* [0063abe](https://github.com/hyperledger/fabric-samples/commit/0063abe) Update stale script name in interest rate swaps
* [3907507](https://github.com/hyperledger/fabric-samples/commit/3907507) [FAB-16277](https://jira.hyperledger.org/browse/FAB-16277) Update BYFN w/ Raft ports in Docker network
* [33b0065](https://github.com/hyperledger/fabric-samples/commit/33b0065) [FAB-14813](https://jira.hyperledger.org/browse/FAB-14813) Channel event sample in fabric-samples
* [b62d5bd](https://github.com/hyperledger/fabric-samples/commit/b62d5bd) [FAB-16132](https://jira.hyperledger.org/browse/FAB-16132) Remove Kafka consensus from BYFN
* [9b14525](https://github.com/hyperledger/fabric-samples/commit/9b14525) [FAB-15213](https://jira.hyperledger.org/browse/FAB-15213) Update Commercial Paper for Java
* [4158877](https://github.com/hyperledger/fabric-samples/commit/4158877) [FAB-16232](https://jira.hyperledger.org/browse/FAB-16232) Remove FabToken sample
* [b6380cc](https://github.com/hyperledger/fabric-samples/commit/b6380cc) [FAB-16198](https://jira.hyperledger.org/browse/FAB-16198) Run "go mod vendor" for FabCar Go contract
* [639848a](https://github.com/hyperledger/fabric-samples/commit/639848a) [FAB-16197](https://jira.hyperledger.org/browse/FAB-16197) Add connection profiles to .gitignore
* [3996db5](https://github.com/hyperledger/fabric-samples/commit/3996db5) [FAB-12219](https://jira.hyperledger.org/browse/FAB-12219) abstore node -> javascript
* [14ac271](https://github.com/hyperledger/fabric-samples/commit/14ac271) [FAB-12219](https://jira.hyperledger.org/browse/FAB-12219) marbles02 node -> javascript
* [13f16e5](https://github.com/hyperledger/fabric-samples/commit/13f16e5) [FGJ-4] CI tests for FabCar Java sample
* [171a7d2](https://github.com/hyperledger/fabric-samples/commit/171a7d2) FGJ-4 Fabcar sample
* [868f9d0](https://github.com/hyperledger/fabric-samples/commit/868f9d0) [FAB-15625](https://jira.hyperledger.org/browse/FAB-15625) Add UT for Simple Asset Chaincode
* [597d150](https://github.com/hyperledger/fabric-samples/commit/597d150) [FAB-14532](https://jira.hyperledger.org/browse/FAB-14532) Remove LL FabCar sample
* [583ff8f](https://github.com/hyperledger/fabric-samples/commit/583ff8f) Use renamed CheckCommitReadiness function
* [750f937](https://github.com/hyperledger/fabric-samples/commit/750f937) [FAB-15213](https://jira.hyperledger.org/browse/FAB-15213) Add Java FabCar sample contract
* [abbda95](https://github.com/hyperledger/fabric-samples/commit/abbda95) [FAB-15897](https://jira.hyperledger.org/browse/FAB-15897) Improve FabCar test logging
* [dd8150a](https://github.com/hyperledger/fabric-samples/commit/dd8150a) [FAB-15104](https://jira.hyperledger.org/browse/FAB-15104) Remove versions from fabric-samples readme
* [1387aa8](https://github.com/hyperledger/fabric-samples/commit/1387aa8) [FAB-15927](https://jira.hyperledger.org/browse/FAB-15927)  Better expression for golang
* [61c33d3](https://github.com/hyperledger/fabric-samples/commit/61c33d3) [FAB-15973](https://jira.hyperledger.org/browse/FAB-15973) use --output json on simulatecommit
* [8bbdd0f](https://github.com/hyperledger/fabric-samples/commit/8bbdd0f) [FAB-15716](https://jira.hyperledger.org/browse/FAB-15716) Fix instructions for dev-mode
* [0254d67](https://github.com/hyperledger/fabric-samples/commit/0254d67) QueryApprovalStatus -> SimulateCommitChaincodeDef
* [c57d67c](https://github.com/hyperledger/fabric-samples/commit/c57d67c) [FAB-15782](https://jira.hyperledger.org/browse/FAB-15782) Sample Go CC should include deps
* [6ba5a19](https://github.com/hyperledger/fabric-samples/commit/6ba5a19) Update to Go 1.12.5 in ci.properties
* [1774a25](https://github.com/hyperledger/fabric-samples/commit/1774a25) [FAB-15723](https://jira.hyperledger.org/browse/FAB-15723) Fix script and instruction with ccenv
* [6ae711c](https://github.com/hyperledger/fabric-samples/commit/6ae711c) [FAB-15717](https://jira.hyperledger.org/browse/FAB-15717) fix Error Unexpected end of JSON input
* [5be56d3](https://github.com/hyperledger/fabric-samples/commit/5be56d3) [FAB-15104](https://jira.hyperledger.org/browse/FAB-15104) Remove scripts/bootstrap.sh
* [779f8f3](https://github.com/hyperledger/fabric-samples/commit/779f8f3) [FAB-15649](https://jira.hyperledger.org/browse/FAB-15649)Fix Fabcar to install Chaincode on all peers
* [7c5f5d3](https://github.com/hyperledger/fabric-samples/commit/7c5f5d3) [FAB-15199](https://jira.hyperledger.org/browse/FAB-15199) Update interest rate sample
* [f0dca20](https://github.com/hyperledger/fabric-samples/commit/f0dca20) [FAB-14532](https://jira.hyperledger.org/browse/FAB-14532) Remove LL FabCar sample
* [1ed1a10](https://github.com/hyperledger/fabric-samples/commit/1ed1a10) [FAB-15573](https://jira.hyperledger.org/browse/FAB-15573) Fix typo in fabric-samples-ci.md
* [2e7fec9](https://github.com/hyperledger/fabric-samples/commit/2e7fec9) [FAB-15601](https://jira.hyperledger.org/browse/FAB-15601) BYFN: Fix MAX_RETRY for couchdb
* [1e9e4c4](https://github.com/hyperledger/fabric-samples/commit/1e9e4c4) [FAB-9329](https://jira.hyperledger.org/browse/FAB-9329) Remove the unused variable in BYFN/EYFN
* [964c09f](https://github.com/hyperledger/fabric-samples/commit/964c09f) [FAB-15601](https://jira.hyperledger.org/browse/FAB-15601) BYFN: Fix MAX_RETRY for couchdb
* [41dca99](https://github.com/hyperledger/fabric-samples/commit/41dca99) [FAB-15127](https://jira.hyperledger.org/browse/FAB-15127) Update high throughput sample
* [3fe014a](https://github.com/hyperledger/fabric-samples/commit/3fe014a) Use official CouchDB image
* [f2d0fa0](https://github.com/hyperledger/fabric-samples/commit/f2d0fa0) [FAB-14487](https://jira.hyperledger.org/browse/FAB-14487) Make FabCar use BYFN, not basic-network
* [e9c3649](https://github.com/hyperledger/fabric-samples/commit/e9c3649) [FAB-15276](https://jira.hyperledger.org/browse/FAB-15276) Fix license statements
* [fbe4036](https://github.com/hyperledger/fabric-samples/commit/fbe4036) [FAB-14486](https://jira.hyperledger.org/browse/FAB-14486) Extend BYFN to opt skip chaincode deploy
* [0c4141f](https://github.com/hyperledger/fabric-samples/commit/0c4141f) [FAB-14485](https://jira.hyperledger.org/browse/FAB-14485) Extend BYFN to opt inc cert authorities
* [529b83b](https://github.com/hyperledger/fabric-samples/commit/529b83b) [FAB-14330](https://jira.hyperledger.org/browse/FAB-14330) Add connection profiles for BYFN and EYFN
* [2c21c83](https://github.com/hyperledger/fabric-samples/commit/2c21c83) [FABN-1184] Update fabtoken/README.md
* [5056a23](https://github.com/hyperledger/fabric-samples/commit/5056a23) [FABN-1184] Add CI script for fabtoken sample app
* [5d6db95](https://github.com/hyperledger/fabric-samples/commit/5d6db95) Update maintainers for fabric-samples
* [f527815](https://github.com/hyperledger/fabric-samples/commit/f527815) [FAB-15119](https://jira.hyperledger.org/browse/FAB-15119) Fix BYFN with Java chaincode
* [8245252](https://github.com/hyperledger/fabric-samples/commit/8245252) [FABN-1184] Implement fabtoken sample app
* [1bd1c2f](https://github.com/hyperledger/fabric-samples/commit/1bd1c2f) FABCI-284 Update CI Pipeline script
* [c24abf9](https://github.com/hyperledger/fabric-samples/commit/c24abf9) [FAB-15022](https://jira.hyperledger.org/browse/FAB-15022) Basic-network support for new lifecycle
* [b64fd45](https://github.com/hyperledger/fabric-samples/commit/b64fd45) [FAB-15051](https://jira.hyperledger.org/browse/FAB-15051) delStandard() function for high-throughput
* [3e68a7e](https://github.com/hyperledger/fabric-samples/commit/3e68a7e) [FAB-14784](https://jira.hyperledger.org/browse/FAB-14784) Remove balance-transfer
* [eb3fe08](https://github.com/hyperledger/fabric-samples/commit/eb3fe08) [FAB-14779](https://jira.hyperledger.org/browse/FAB-14779) QueryApprovalStatus step in byfn
* [2777429](https://github.com/hyperledger/fabric-samples/commit/2777429) [FAB-14711](https://jira.hyperledger.org/browse/FAB-14711) update byfn with new lifecycle
* [aec3389](https://github.com/hyperledger/fabric-samples/commit/aec3389) [FAB-12215](https://jira.hyperledger.org/browse/FAB-12215)WYFA:Remove chainId in tx proposal request
* [b5d5026](https://github.com/hyperledger/fabric-samples/commit/b5d5026) [FAB-14633](https://jira.hyperledger.org/browse/FAB-14633) Remove apt-get from eyfn.sh
* [efaadd3](https://github.com/hyperledger/fabric-samples/commit/efaadd3) [FAB-14531](https://jira.hyperledger.org/browse/FAB-14531) BYFN Raft with 5 nodes
* [d63047c](https://github.com/hyperledger/fabric-samples/commit/d63047c) [FAB-14444](https://jira.hyperledger.org/browse/FAB-14444)
* [7e3d428](https://github.com/hyperledger/fabric-samples/commit/7e3d428) [FAB-14369](https://jira.hyperledger.org/browse/FAB-14369)Fix dev mode failing to build Chaincode
* [420ba23](https://github.com/hyperledger/fabric-samples/commit/420ba23) [FAB-12762](https://jira.hyperledger.org/browse/FAB-12762) Add etcd/raft consensus option to BYFN
* [2b68c80](https://github.com/hyperledger/fabric-samples/commit/2b68c80) [FAB-14317](https://jira.hyperledger.org/browse/FAB-14317) Add default policies to org3
* [f942010](https://github.com/hyperledger/fabric-samples/commit/f942010) [FAB-14268](https://jira.hyperledger.org/browse/FAB-14268) Make BYFN/EYFN ports match external ports
* [4e2ce23](https://github.com/hyperledger/fabric-samples/commit/4e2ce23) [FAB-14271](https://jira.hyperledger.org/browse/FAB-14271) Add channel policies to channel config
* [f26477c](https://github.com/hyperledger/fabric-samples/commit/f26477c) [FAB-11796](https://jira.hyperledger.org/browse/FAB-11796)high-throughput:Remove unnecessary prunesafe
* [137327a](https://github.com/hyperledger/fabric-samples/commit/137327a) [FAB-14162](https://jira.hyperledger.org/browse/FAB-14162) Pin fabric-samples in master to "unstable"
* [6007c09](https://github.com/hyperledger/fabric-samples/commit/6007c09) [FAB-13862](https://jira.hyperledger.org/browse/FAB-13862) Rename example02 ABstore
* [94cb603](https://github.com/hyperledger/fabric-samples/commit/94cb603) [FAB-13933](https://jira.hyperledger.org/browse/FAB-13933) Fix misspellings
* [a8a5539](https://github.com/hyperledger/fabric-samples/commit/a8a5539) Fix doc link Fix variable error
* [b0cda61](https://github.com/hyperledger/fabric-samples/commit/b0cda61) [FAB-13769](https://jira.hyperledger.org/browse/FAB-13769) Add UT code to ABAC sample Chaincode
* [c7438e1](https://github.com/hyperledger/fabric-samples/commit/c7438e1) [FAB-13668](https://jira.hyperledger.org/browse/FAB-13668) BYFN's container volume mapping is bad
* [e48b2de](https://github.com/hyperledger/fabric-samples/commit/e48b2de) [FAB-13489](https://jira.hyperledger.org/browse/FAB-13489) fabric-samples add error msg
* [8a458b5](https://github.com/hyperledger/fabric-samples/commit/8a458b5) [FAB-12056](https://jira.hyperledger.org/browse/FAB-12056) Private marbles cc use transient data
* [6269941](https://github.com/hyperledger/fabric-samples/commit/6269941) Correct broken link
* [461b6ab](https://github.com/hyperledger/fabric-samples/commit/461b6ab) FABC-781 Remove fabric-ca sample
* [e9b9477](https://github.com/hyperledger/fabric-samples/commit/e9b9477) [FAB-13372](https://jira.hyperledger.org/browse/FAB-13372) Fabric-Samples return error msg
* [e3da220](https://github.com/hyperledger/fabric-samples/commit/e3da220) [FAB-13433](https://jira.hyperledger.org/browse/FAB-13433) - Update Jenkinsfile configuration
* [33db64e](https://github.com/hyperledger/fabric-samples/commit/33db64e) Configure Stale ProBot
* [5cd277f](https://github.com/hyperledger/fabric-samples/commit/5cd277f) [FAB-11951](https://jira.hyperledger.org/browse/FAB-11951) Interest-rate swap example for SBE
* [9567985](https://github.com/hyperledger/fabric-samples/commit/9567985) [FAB-13407](https://jira.hyperledger.org/browse/FAB-13407) Align fabric-samples with 1.4.0-rc2 release
* [c7572aa](https://github.com/hyperledger/fabric-samples/commit/c7572aa) [FAB-13305](https://jira.hyperledger.org/browse/FAB-13305) Update scripts to pull latest artifacts
* [ab46e35](https://github.com/hyperledger/fabric-samples/commit/ab46e35) [FAB-13283](https://jira.hyperledger.org/browse/FAB-13283) Update sample code for commercial paper
* [f677821](https://github.com/hyperledger/fabric-samples/commit/f677821) [FAB-13232](https://jira.hyperledger.org/browse/FAB-13232) fix peer node start command
* [6a7472e](https://github.com/hyperledger/fabric-samples/commit/6a7472e) [FAB-13126](https://jira.hyperledger.org/browse/FAB-13126) Align fabric-samples with 1.4.0-rc1 release
* [445ccbc](https://github.com/hyperledger/fabric-samples/commit/445ccbc) [FAB-12880](https://jira.hyperledger.org/browse/FAB-12880) Move old prog model samples for FabCar
* [5be62b5](https://github.com/hyperledger/fabric-samples/commit/5be62b5) [FAB-13207](https://jira.hyperledger.org/browse/FAB-13207) Remove incorrect discovery options
* [fdbd92d](https://github.com/hyperledger/fabric-samples/commit/fdbd92d) [FAB-13206](https://jira.hyperledger.org/browse/FAB-13206) Remove dependencies on fabric-client
* [eff0046](https://github.com/hyperledger/fabric-samples/commit/eff0046) [FAB-12877](https://jira.hyperledger.org/browse/FAB-12877) Add fabcar app using new prog model (JS)
* [c184196](https://github.com/hyperledger/fabric-samples/commit/c184196) [FAB-12878](https://jira.hyperledger.org/browse/FAB-12878) Add fabcar app using new prog model (TS)
* [e1a39e6](https://github.com/hyperledger/fabric-samples/commit/e1a39e6) [FAB-12724](https://jira.hyperledger.org/browse/FAB-12724) Upgrade from 1.3.x to 1.4.0
* [c21bbba](https://github.com/hyperledger/fabric-samples/commit/c21bbba) Update samples to use new logging env variables
* [7ad9f19](https://github.com/hyperledger/fabric-samples/commit/7ad9f19) [FAB-13011](https://jira.hyperledger.org/browse/FAB-13011) add kafka consensus type to byfn sample
* [33f064f](https://github.com/hyperledger/fabric-samples/commit/33f064f) [FAB-13170](https://jira.hyperledger.org/browse/FAB-13170) Add memberOnlyRead to marbles sample
* [928b72b](https://github.com/hyperledger/fabric-samples/commit/928b72b) [FAB-12875](https://jira.hyperledger.org/browse/FAB-12875) Add automated tests for fabcar sample
* [5c087f1](https://github.com/hyperledger/fabric-samples/commit/5c087f1) [FAB-13046](https://jira.hyperledger.org/browse/FAB-13046) Update TypeScript contract dependencies
* [3748983](https://github.com/hyperledger/fabric-samples/commit/3748983) [FAB-12879](https://jira.hyperledger.org/browse/FAB-12879) Update fabcar script for new contracts
* [4fb3b57](https://github.com/hyperledger/fabric-samples/commit/4fb3b57) [FAB-12852](https://jira.hyperledger.org/browse/FAB-12852) Add fabcar contract w/ new prog model (TS)
* [9facb42](https://github.com/hyperledger/fabric-samples/commit/9facb42) [FAB-12851](https://jira.hyperledger.org/browse/FAB-12851) Add fabcar contract w/ new prog model (JS)
* [e67fcf1](https://github.com/hyperledger/fabric-samples/commit/e67fcf1) [FAB-12322](https://jira.hyperledger.org/browse/FAB-12322) Update commercial-paper sample
* [fd6e2c4](https://github.com/hyperledger/fabric-samples/commit/fd6e2c4) [FAB-12703](https://jira.hyperledger.org/browse/FAB-12703) Fix misspelling "lauches"
* [c05f172](https://github.com/hyperledger/fabric-samples/commit/c05f172) [FAB-12608](https://jira.hyperledger.org/browse/FAB-12608) Update pipeline script
* [286861e](https://github.com/hyperledger/fabric-samples/commit/286861e) [FAB-12371](https://jira.hyperledger.org/browse/FAB-12371)Fix the abac sample to use new cid package
* [24c5e47](https://github.com/hyperledger/fabric-samples/commit/24c5e47) [FAB-12026](https://jira.hyperledger.org/browse/FAB-12026) pagination samples for node marbles02
* [6dc5ce5](https://github.com/hyperledger/fabric-samples/commit/6dc5ce5) [FAB-12587](https://jira.hyperledger.org/browse/FAB-12587) Fix for Query Block by block hash API
* [df311ce](https://github.com/hyperledger/fabric-samples/commit/df311ce) [FAB-12173](https://jira.hyperledger.org/browse/FAB-12173) balance-transfer: Update anchor peers
* [c925148](https://github.com/hyperledger/fabric-samples/commit/c925148) [FAB-12415](https://jira.hyperledger.org/browse/FAB-12415) samples for 1.3.0 (master cleanup)
* [3a12c60](https://github.com/hyperledger/fabric-samples/commit/3a12c60) [FAB-12275](https://jira.hyperledger.org/browse/FAB-12275) Fix the warn in creating genesis block
* [c6f6324](https://github.com/hyperledger/fabric-samples/commit/c6f6324) [FAB-12257](https://jira.hyperledger.org/browse/FAB-12257) allow balance-transfer for doscovery
* [4445e8d](https://github.com/hyperledger/fabric-samples/commit/4445e8d) [FAB-12272](https://jira.hyperledger.org/browse/FAB-12272) Increase MAX_RETRY to 10
* [33d333f](https://github.com/hyperledger/fabric-samples/commit/33d333f) [FAB-12190](https://jira.hyperledger.org/browse/FAB-12190) Update stable version in CI scripts
* [edee638](https://github.com/hyperledger/fabric-samples/commit/edee638) [FAB-12184](https://jira.hyperledger.org/browse/FAB-12184) Prepare fabric-samples for 1.3.0-rc1
* [514d456](https://github.com/hyperledger/fabric-samples/commit/514d456) [FAB-12170](https://jira.hyperledger.org/browse/FAB-12170) Fix dependency check in java chaincode
* [9f80e47](https://github.com/hyperledger/fabric-samples/commit/9f80e47) [FAB-12119](https://jira.hyperledger.org/browse/FAB-12119) Fix groupId in java chaincodes
* [3237229](https://github.com/hyperledger/fabric-samples/commit/3237229) [FAB-12073](https://jira.hyperledger.org/browse/FAB-12073) Fix Org3 peers CouchDB config.
* [eece3d8](https://github.com/hyperledger/fabric-samples/commit/eece3d8) [FAB-12106](https://jira.hyperledger.org/browse/FAB-12106) Update fabric-ca build scripts
* [f62952f](https://github.com/hyperledger/fabric-samples/commit/f62952f) [FABC-131] Change fabric-ca sample to build images
* [4089786](https://github.com/hyperledger/fabric-samples/commit/4089786) [FAB-11867](https://jira.hyperledger.org/browse/FAB-11867) Develop Apps:Sample pt 2 -- application
* [9ee57c6](https://github.com/hyperledger/fabric-samples/commit/9ee57c6) [FAB-11778](https://jira.hyperledger.org/browse/FAB-11778) Upgrade to v1.3.x from v1.2.x in byfn
* [e7a1b76](https://github.com/hyperledger/fabric-samples/commit/e7a1b76) [FAB-9386](https://jira.hyperledger.org/browse/FAB-9386) Remove Marbles sample reference to Fauxton
* [9c6acee](https://github.com/hyperledger/fabric-samples/commit/9c6acee) [FAB-12022](https://jira.hyperledger.org/browse/FAB-12022) Fix CI by increasing couchdb timeout
* [4030ebd](https://github.com/hyperledger/fabric-samples/commit/4030ebd) [FAB-11397](https://jira.hyperledger.org/browse/FAB-11397) Adding java cc
* [d776651](https://github.com/hyperledger/fabric-samples/commit/d776651) [FAB-11723](https://jira.hyperledger.org/browse/FAB-11723) Developing Apps: Sample pt 1 -- contract
* [cbbbc78](https://github.com/hyperledger/fabric-samples/commit/cbbbc78) [FAB-11577](https://jira.hyperledger.org/browse/FAB-11577) Fix balance transfer to install Chaincode
* [bfdc0b6](https://github.com/hyperledger/fabric-samples/commit/bfdc0b6) [FAB-11518](https://jira.hyperledger.org/browse/FAB-11518)
* [5930dfc](https://github.com/hyperledger/fabric-samples/commit/5930dfc) [FAB-11488](https://jira.hyperledger.org/browse/FAB-11488) Update CI script
* [ca6959c](https://github.com/hyperledger/fabric-samples/commit/ca6959c) [FAB-11311](https://jira.hyperledger.org/browse/FAB-11311) Update fabric image version
* [0ca9e6e](https://github.com/hyperledger/fabric-samples/commit/0ca9e6e) FABN-833 Update Jenkinsfile to capture build artifacts
* [a4a15cb](https://github.com/hyperledger/fabric-samples/commit/a4a15cb) [FAB-11220](https://jira.hyperledger.org/browse/FAB-11220) Samples - remove EventHub
* [c4bdc68](https://github.com/hyperledger/fabric-samples/commit/c4bdc68) [FAB-8479](https://jira.hyperledger.org/browse/FAB-8479) Added Endorsement policy
* [6edd320](https://github.com/hyperledger/fabric-samples/commit/6edd320) [FAB-9297](https://jira.hyperledger.org/browse/FAB-9297) fix README links and update bootstrap.sh
* [75e2931](https://github.com/hyperledger/fabric-samples/commit/75e2931) [FAB-10811](https://jira.hyperledger.org/browse/FAB-10811) fabric-ca sample is broken on v1.2
* [ad40e29](https://github.com/hyperledger/fabric-samples/commit/ad40e29) [FAB-10732](https://jira.hyperledger.org/browse/FAB-10732) BYFN upgrade to v1.2
* [20ad472](https://github.com/hyperledger/fabric-samples/commit/20ad472) [FAB-10801](https://jira.hyperledger.org/browse/FAB-10801) format and styling for shell artifacts
* [e95210e](https://github.com/hyperledger/fabric-samples/commit/e95210e) [FAB-10074](https://jira.hyperledger.org/browse/FAB-10074) CI Script for pipeline project type
* [5956178](https://github.com/hyperledger/fabric-samples/commit/5956178) [FAB-6600](https://jira.hyperledger.org/browse/FAB-6600) Sample chaincode for private data
* [21444ab](https://github.com/hyperledger/fabric-samples/commit/21444ab) [FAB-10297](https://jira.hyperledger.org/browse/FAB-10297) replace fabric-preload.sh
* [3c32c52](https://github.com/hyperledger/fabric-samples/commit/3c32c52) [FAB-10346](https://jira.hyperledger.org/browse/FAB-10346) Ensure peers are in sync in eyfn
* [2f30504](https://github.com/hyperledger/fabric-samples/commit/2f30504) [FAB-10340](https://jira.hyperledger.org/browse/FAB-10340) Fix broken link in chaincode-docker-devmode
* [a603655](https://github.com/hyperledger/fabric-samples/commit/a603655) [FAB-10306](https://jira.hyperledger.org/browse/FAB-10306) Fix config in balance-transfer
* [1cd059e](https://github.com/hyperledger/fabric-samples/commit/1cd059e) [FAB-8612](https://jira.hyperledger.org/browse/FAB-8612) Cleanup eyfn/byfn with --remove-orphans
* [3031a8c](https://github.com/hyperledger/fabric-samples/commit/3031a8c) [FAB-10235](https://jira.hyperledger.org/browse/FAB-10235) Update BYFN to use V1_2 capability
* [5cf1944](https://github.com/hyperledger/fabric-samples/commit/5cf1944) [FAB-10185](https://jira.hyperledger.org/browse/FAB-10185) Reorder for logical consistency
* [e2f4f20](https://github.com/hyperledger/fabric-samples/commit/e2f4f20) [FAB-10176](https://jira.hyperledger.org/browse/FAB-10176) Fix invalid configtx.yaml documents
* [cad2b98](https://github.com/hyperledger/fabric-samples/commit/cad2b98) [FAB-10073](https://jira.hyperledger.org/browse/FAB-10073) Fix broken link in samples/fabric-ca/README
* [ace2e5a](https://github.com/hyperledger/fabric-samples/commit/ace2e5a) [FAB-10021](https://jira.hyperledger.org/browse/FAB-10021) Typo in balance-transfer/testAPIs.sh
* [fa1d899](https://github.com/hyperledger/fabric-samples/commit/fa1d899) [FAB-9893](https://jira.hyperledger.org/browse/FAB-9893) Add .gitreview file for quicker Gerrit setup
* [98561e0](https://github.com/hyperledger/fabric-samples/commit/98561e0) [FAB-9552](https://jira.hyperledger.org/browse/FAB-9552) Fix access of an un-declared variable
* [b5dad8c](https://github.com/hyperledger/fabric-samples/commit/b5dad8c) [FAB-9317](https://jira.hyperledger.org/browse/FAB-9317) Update first-network with multi endorse
* [146f7bc](https://github.com/hyperledger/fabric-samples/commit/146f7bc) [FAB-9572](https://jira.hyperledger.org/browse/FAB-9572) Fix README to match new version of BYFN
* [010fcb5](https://github.com/hyperledger/fabric-samples/commit/010fcb5) [FAB-9326](https://jira.hyperledger.org/browse/FAB-9326) Clean up byfn.sh, drop "-m" option
* [2d6386c](https://github.com/hyperledger/fabric-samples/commit/2d6386c) [FAB-8245](https://jira.hyperledger.org/browse/FAB-8245) change first network according to the fix
* [3a5108f](https://github.com/hyperledger/fabric-samples/commit/3a5108f) [FAB-9475](https://jira.hyperledger.org/browse/FAB-9475) Fix a dead link in README
* [77a6568](https://github.com/hyperledger/fabric-samples/commit/77a6568) [FAB-9294](https://jira.hyperledger.org/browse/FAB-9294) eliminate excess noise in BYFN
* [41f5ab8](https://github.com/hyperledger/fabric-samples/commit/41f5ab8) [FAB-9406](https://jira.hyperledger.org/browse/FAB-9406) Typo in byfn.sh
* [f5c2eb8](https://github.com/hyperledger/fabric-samples/commit/f5c2eb8) [FAB-9362](https://jira.hyperledger.org/browse/FAB-9362) add CONTRIBUTING.md and CODE_OF_CONDUCT.md
* [9d518fb](https://github.com/hyperledger/fabric-samples/commit/9d518fb) [FAB-9330](https://jira.hyperledger.org/browse/FAB-9330) Refactor top-level .gitignore into subdirs
* [a080da3](https://github.com/hyperledger/fabric-samples/commit/a080da3) [FAB-8958](https://jira.hyperledger.org/browse/FAB-8958) Add org3 removal to byfn.sh
* [8fc0865](https://github.com/hyperledger/fabric-samples/commit/8fc0865) [FAB-9185](https://jira.hyperledger.org/browse/FAB-9185) add /config to .gitignore
* [680ff01](https://github.com/hyperledger/fabric-samples/commit/680ff01) [FAB-8600](https://jira.hyperledger.org/browse/FAB-8600)Clear hyperledger-related containers only
* [4f97717](https://github.com/hyperledger/fabric-samples/commit/4f97717) [FAB-8947](https://jira.hyperledger.org/browse/FAB-8947) Fabric-Samples remove package lock
* [fcf62ad](https://github.com/hyperledger/fabric-samples/commit/fcf62ad) [FAB-8265](https://jira.hyperledger.org/browse/FAB-8265) Fixed spelling error in registerUser
* [e4d7760](https://github.com/hyperledger/fabric-samples/commit/e4d7760) [ [FAB-8730](https://jira.hyperledger.org/browse/FAB-8730) ] hyphen breaks fabric-samples
* [2bbb0a8](https://github.com/hyperledger/fabric-samples/commit/2bbb0a8) [FAB-8630](https://jira.hyperledger.org/browse/FAB-8630) byfn failing intermittently in CI
* [823fb6b](https://github.com/hyperledger/fabric-samples/commit/823fb6b) [ [FAB-8679](https://jira.hyperledger.org/browse/FAB-8679) ] Permit samples to use RootCAs
* [9f9fc7e](https://github.com/hyperledger/fabric-samples/commit/9f9fc7e) [FAB-8633](https://jira.hyperledger.org/browse/FAB-8633) Correct revoked error check
* [f3b55c9](https://github.com/hyperledger/fabric-samples/commit/f3b55c9) [FAB-8621](https://jira.hyperledger.org/browse/FAB-8621) Remove Marbles index json data wrapper
* [f110a6e](https://github.com/hyperledger/fabric-samples/commit/f110a6e) [FAB-8602](https://jira.hyperledger.org/browse/FAB-8602) Add volumes to first-network e2e yaml
* [7362928](https://github.com/hyperledger/fabric-samples/commit/7362928) [FAB-8567](https://jira.hyperledger.org/browse/FAB-8567) Alt: Always use volumes for ledger (m)
* [afb3d62](https://github.com/hyperledger/fabric-samples/commit/afb3d62) [FAB-8561](https://jira.hyperledger.org/browse/FAB-8561) Add note to readthedocs link in README
* [10526d5](https://github.com/hyperledger/fabric-samples/commit/10526d5) [FAB-8564](https://jira.hyperledger.org/browse/FAB-8564) add debug commands to byfn
* [e73a481](https://github.com/hyperledger/fabric-samples/commit/e73a481) [FAB-8568](https://jira.hyperledger.org/browse/FAB-8568) BYFN: Fix IMAGE_TAG for couchdb
* [ffd7a25](https://github.com/hyperledger/fabric-samples/commit/ffd7a25) [FAB-6400](https://jira.hyperledger.org/browse/FAB-6400) Balance-transfer filtered events
* [cba57da](https://github.com/hyperledger/fabric-samples/commit/cba57da) [FAB-8165](https://jira.hyperledger.org/browse/FAB-8165) Adding upgrade function to byfn
* [c6166d6](https://github.com/hyperledger/fabric-samples/commit/c6166d6) [FAB-8540](https://jira.hyperledger.org/browse/FAB-8540) Add ledger persistance to first-network
* [77e74b7](https://github.com/hyperledger/fabric-samples/commit/77e74b7) [FAB-8497](https://jira.hyperledger.org/browse/FAB-8497) Download images required for fabric-ca
* [2bed1ef](https://github.com/hyperledger/fabric-samples/commit/2bed1ef) [FAB-8539](https://jira.hyperledger.org/browse/FAB-8539) Add version checking to first-network
* [7b7fc09](https://github.com/hyperledger/fabric-samples/commit/7b7fc09) [FAB-8496](https://jira.hyperledger.org/browse/FAB-8496) allow modification of affiliations
* [981efba](https://github.com/hyperledger/fabric-samples/commit/981efba) [FAB-8503](https://jira.hyperledger.org/browse/FAB-8503) Prevent CLI container from exiting
* [c93268f](https://github.com/hyperledger/fabric-samples/commit/c93268f) [FAB-8518](https://jira.hyperledger.org/browse/FAB-8518) Not all compose files have IMAGE_TAG
* [4f2cd8d](https://github.com/hyperledger/fabric-samples/commit/4f2cd8d) [FAB-8445](https://jira.hyperledger.org/browse/FAB-8445) Adding IMAGE_TAG option to byfn
* [ca80163](https://github.com/hyperledger/fabric-samples/commit/ca80163) [FAB-8387](https://jira.hyperledger.org/browse/FAB-8387) Add gencrl to revoke command
* [e379ac5](https://github.com/hyperledger/fabric-samples/commit/e379ac5) [FAB-8407](https://jira.hyperledger.org/browse/FAB-8407) Fix TLS bad cert error
* [02ca1dc](https://github.com/hyperledger/fabric-samples/commit/02ca1dc) [FAB-7494](https://jira.hyperledger.org/browse/FAB-7494) Fix bootstrap peer config
* [41e144f](https://github.com/hyperledger/fabric-samples/commit/41e144f) [FAB-8386](https://jira.hyperledger.org/browse/FAB-8386) eyfn fails to execute invoke on org3
* [4ab098f](https://github.com/hyperledger/fabric-samples/commit/4ab098f) [FAB-8327](https://jira.hyperledger.org/browse/FAB-8327) Change eyfn.sh to use configtxlator cli
* [24f35c1](https://github.com/hyperledger/fabric-samples/commit/24f35c1) [FAB-7750](https://jira.hyperledger.org/browse/FAB-7750) first network with support to [FAB-5664](https://jira.hyperledger.org/browse/FAB-5664)
* [305d6f4](https://github.com/hyperledger/fabric-samples/commit/305d6f4) [FAB-8238](https://jira.hyperledger.org/browse/FAB-8238)wrong orderer/peer type in fabric-ca sam
* [1d69e9e](https://github.com/hyperledger/fabric-samples/commit/1d69e9e) [FAB-7540](https://jira.hyperledger.org/browse/FAB-7540) Simplifies Reconfigure Your Network tutorial
* [0daa8bc](https://github.com/hyperledger/fabric-samples/commit/0daa8bc) [FAB-8122](https://jira.hyperledger.org/browse/FAB-8122) Updated README.md file- balancetransfer
* [652f074](https://github.com/hyperledger/fabric-samples/commit/652f074) [FAB-7342](https://jira.hyperledger.org/browse/FAB-7342) Enable client auth in fabric-ca sample
* [90c2bfd](https://github.com/hyperledger/fabric-samples/commit/90c2bfd) [FAB-6934](https://jira.hyperledger.org/browse/FAB-6934) fix devmode sample for v1.1
* [2c0c2c7](https://github.com/hyperledger/fabric-samples/commit/2c0c2c7) [FAB-7910](https://jira.hyperledger.org/browse/FAB-7910) Clean up chaincode containers in fabric-ca
* [bbee1b2](https://github.com/hyperledger/fabric-samples/commit/bbee1b2) [FAB-7908](https://jira.hyperledger.org/browse/FAB-7908) Change hf.admin attr to admin
* [dd12f88](https://github.com/hyperledger/fabric-samples/commit/dd12f88) [FAB-7834](https://jira.hyperledger.org/browse/FAB-7834) Add couchdb index to marbles02 sample
* [25f6091](https://github.com/hyperledger/fabric-samples/commit/25f6091) [FAB-7836](https://jira.hyperledger.org/browse/FAB-7836) Fix "No identity type provided" Error
* [5eb2fb2](https://github.com/hyperledger/fabric-samples/commit/5eb2fb2) [FAB-7653](https://jira.hyperledger.org/browse/FAB-7653) Fix incorrect chaincode location
* [5a974a4](https://github.com/hyperledger/fabric-samples/commit/5a974a4) [FAB-7533](https://jira.hyperledger.org/browse/FAB-7533) Fix typo in balance-transfer sample
* [038c496](https://github.com/hyperledger/fabric-samples/commit/038c496) [FAB-7592](https://jira.hyperledger.org/browse/FAB-7592) Give hf.Registrar attrs to admins
* [cf79cd1](https://github.com/hyperledger/fabric-samples/commit/cf79cd1) [FAB-7584](https://jira.hyperledger.org/browse/FAB-7584) Removes copy of creds to keystore
* [a0c1687](https://github.com/hyperledger/fabric-samples/commit/a0c1687) [FAB-7487](https://jira.hyperledger.org/browse/FAB-7487) Fix typo in node/fabcar.js
* [1883ae9](https://github.com/hyperledger/fabric-samples/commit/1883ae9) [FAB-7527](https://jira.hyperledger.org/browse/FAB-7527) Improves BYFN
* [e848216](https://github.com/hyperledger/fabric-samples/commit/e848216) [FAB-7511](https://jira.hyperledger.org/browse/FAB-7511) clear crypto material after clearing network
* [54ffa5f](https://github.com/hyperledger/fabric-samples/commit/54ffa5f) [FAB-7241](https://jira.hyperledger.org/browse/FAB-7241) Fix chaincode-devmode
* [c446510](https://github.com/hyperledger/fabric-samples/commit/c446510) [FAB-6550](https://jira.hyperledger.org/browse/FAB-6550) Sample app written in typescript
* [69a127e](https://github.com/hyperledger/fabric-samples/commit/69a127e) [FAB-6254](https://jira.hyperledger.org/browse/FAB-6254) Fix the default CLI timeout
* [7428f64](https://github.com/hyperledger/fabric-samples/commit/7428f64) [FAB-7160](https://jira.hyperledger.org/browse/FAB-7160) Samples - readme sample commands
* [2474704](https://github.com/hyperledger/fabric-samples/commit/2474704) [FAB-6967](https://jira.hyperledger.org/browse/FAB-6967) Added steps to query by a revoked user
* [948e237](https://github.com/hyperledger/fabric-samples/commit/948e237) [FAB-5913](https://jira.hyperledger.org/browse/FAB-5913)balance-transfer:Fix query response message
* [7b76a7a](https://github.com/hyperledger/fabric-samples/commit/7b76a7a) [FAB-6632](https://jira.hyperledger.org/browse/FAB-6632) - Artifacts for BYFN reconfigure
* [24ef9da](https://github.com/hyperledger/fabric-samples/commit/24ef9da) [FAB-6902](https://jira.hyperledger.org/browse/FAB-6902) [FAB-6904](https://jira.hyperledger.org/browse/FAB-6904) correct the sample for FAB-6904
* [fd795d2](https://github.com/hyperledger/fabric-samples/commit/fd795d2) [FAB-6745](https://jira.hyperledger.org/browse/FAB-6745) Fix timing issue in sample
* [a7be462](https://github.com/hyperledger/fabric-samples/commit/a7be462) [FAB-6895](https://jira.hyperledger.org/browse/FAB-6895) chaincode mounting issue
* [7c23985](https://github.com/hyperledger/fabric-samples/commit/7c23985) [FAB-5221](https://jira.hyperledger.org/browse/FAB-5221) generateCerts should delete crypto
* [1961835](https://github.com/hyperledger/fabric-samples/commit/1961835) [FAB-6870](https://jira.hyperledger.org/browse/FAB-6870) Update node modules versions
* [bb3ac84](https://github.com/hyperledger/fabric-samples/commit/bb3ac84) [FAB-5363](https://jira.hyperledger.org/browse/FAB-5363) fabric-samples update balance
* [6b2799e](https://github.com/hyperledger/fabric-samples/commit/6b2799e) [FAB-6568](https://jira.hyperledger.org/browse/FAB-6568) Fabric-Samples - update fabcar
* [fafae55](https://github.com/hyperledger/fabric-samples/commit/fafae55) [FAB-6779](https://jira.hyperledger.org/browse/FAB-6779) Fix the error in fabric-ca
* [caf5c33](https://github.com/hyperledger/fabric-samples/commit/caf5c33) [FAB-6050](https://jira.hyperledger.org/browse/FAB-6050) Adding fabric-ca sample
* [44c204d](https://github.com/hyperledger/fabric-samples/commit/44c204d) [FAB-5898](https://jira.hyperledger.org/browse/FAB-5898) porting samples to node.js chaincode
* [07a07d5](https://github.com/hyperledger/fabric-samples/commit/07a07d5) [FAB-6361](https://jira.hyperledger.org/browse/FAB-6361) Update license text in README
* [c9b0c62](https://github.com/hyperledger/fabric-samples/commit/c9b0c62) [FAB-6292](https://jira.hyperledger.org/browse/FAB-6292) Fix spelling error
* [9d70f31](https://github.com/hyperledger/fabric-samples/commit/9d70f31) Add high-throughput example to samples
* [194b9b9](https://github.com/hyperledger/fabric-samples/commit/194b9b9) [FAB-5618](https://jira.hyperledger.org/browse/FAB-5618) Allow directory to contain spaces
* [77b4090](https://github.com/hyperledger/fabric-samples/commit/77b4090) [FAB-5992](https://jira.hyperledger.org/browse/FAB-5992) Fix error in first-network dir

## "v1.0.2"

* [ba0a098](https://github.com/hyperledger/fabric-samples/commit/ba0a098) [FAB-5995](https://jira.hyperledger.org/browse/FAB-5995) Update samples to work with v1.0.2
* [7cca09f](https://github.com/hyperledger/fabric-samples/commit/7cca09f) [FAB-5759](https://jira.hyperledger.org/browse/FAB-5759) fix byfn e2e test failures
* [7f1c2f4](https://github.com/hyperledger/fabric-samples/commit/7f1c2f4) [FAB-5056](https://jira.hyperledger.org/browse/FAB-5056) enable couchdb test in byfn e2e script
* [2571c96](https://github.com/hyperledger/fabric-samples/commit/2571c96) [FAB-56431](https://jira.hyperledger.org/browse/FAB-56431): Add dependency for couchdb into peer.
* [be773d2](https://github.com/hyperledger/fabric-samples/commit/be773d2) [FAB-5603](https://jira.hyperledger.org/browse/FAB-5603) fixed missing f option on switch
* [5921140](https://github.com/hyperledger/fabric-samples/commit/5921140) [FAB-5576](https://jira.hyperledger.org/browse/FAB-5576) -f flag to choose docker-compose on byfn.sh
* [79cb041](https://github.com/hyperledger/fabric-samples/commit/79cb041) [FAB-5303](https://jira.hyperledger.org/browse/FAB-5303) Further balance-transfer code optimization
* [92348cb](https://github.com/hyperledger/fabric-samples/commit/92348cb) [FAB-5453](https://jira.hyperledger.org/browse/FAB-5453) Enable CouchDB passwords in Fabric Samples
* [2f4e9b8](https://github.com/hyperledger/fabric-samples/commit/2f4e9b8) [FAB-5130](https://jira.hyperledger.org/browse/FAB-5130) Invalid syntax in docker compose files
* [7778837](https://github.com/hyperledger/fabric-samples/commit/7778837) [FAB-5394](https://jira.hyperledger.org/browse/FAB-5394) Introduce Delay as configurable variable
* [134549a](https://github.com/hyperledger/fabric-samples/commit/134549a) [FAB-5412](https://jira.hyperledger.org/browse/FAB-5412) add fabric-preload.sh script
* [a7e83fc](https://github.com/hyperledger/fabric-samples/commit/a7e83fc) [FAB-5392](https://jira.hyperledger.org/browse/FAB-5392) Fix path issue for Git Bash users
* [6cce07c](https://github.com/hyperledger/fabric-samples/commit/6cce07c) [FAB-5155](https://jira.hyperledger.org/browse/FAB-5155)Fix README.md in "balance-transfer" example
* [6422fc3](https://github.com/hyperledger/fabric-samples/commit/6422fc3) [FAB-5311](https://jira.hyperledger.org/browse/FAB-5311) Fix spelling error
* [e0db341](https://github.com/hyperledger/fabric-samples/commit/e0db341) [FAB-5287](https://jira.hyperledger.org/browse/FAB-5287) NodeSDK - fix sample
* [ca8fad3](https://github.com/hyperledger/fabric-samples/commit/ca8fad3) [FAB-5260](https://jira.hyperledger.org/browse/FAB-5260) Update samples to v1.0.0
* [8f01c7f](https://github.com/hyperledger/fabric-samples/commit/8f01c7f) [FAB-5260](https://jira.hyperledger.org/browse/FAB-5260) Update balance-transfer sample to v1.0.0
* [6899719](https://github.com/hyperledger/fabric-samples/commit/6899719) [FAB-5195](https://jira.hyperledger.org/browse/FAB-5195) byfn.sh help text is incorrect
* [70bff28](https://github.com/hyperledger/fabric-samples/commit/70bff28) [FAB-5197](https://jira.hyperledger.org/browse/FAB-5197)Check all prop-responses in balance-transfer
* [f9c2954](https://github.com/hyperledger/fabric-samples/commit/f9c2954) [FAB-5038](https://jira.hyperledger.org/browse/FAB-5038) Generate artifacts if they don't exist
* [957a1ff](https://github.com/hyperledger/fabric-samples/commit/957a1ff) [FAB-5062](https://jira.hyperledger.org/browse/FAB-5062) Correct Gossip bootstrap setup in byfn
* [d58af56](https://github.com/hyperledger/fabric-samples/commit/d58af56) [FAB-5031](https://jira.hyperledger.org/browse/FAB-5031) Fix e2e template typo
* [04c9eff](https://github.com/hyperledger/fabric-samples/commit/04c9eff) [FAB-4996](https://jira.hyperledger.org/browse/FAB-4996) NodeSDK - move sample to fabric-samples
* [6610584](https://github.com/hyperledger/fabric-samples/commit/6610584) [FAB-4994](https://jira.hyperledger.org/browse/FAB-4994) Fix formatting of js files
* [e265cac](https://github.com/hyperledger/fabric-samples/commit/e265cac) [FAB-4988](https://jira.hyperledger.org/browse/FAB-4988) Update certs generated using rc1 binary
* [ba777f3](https://github.com/hyperledger/fabric-samples/commit/ba777f3) [FAB-4371](https://jira.hyperledger.org/browse/FAB-4371) - Chaincode Dev Mode
* [ca0b5d2](https://github.com/hyperledger/fabric-samples/commit/ca0b5d2) [FAB-4916](https://jira.hyperledger.org/browse/FAB-4916) add marbles02 chaincode
* [b4beeff](https://github.com/hyperledger/fabric-samples/commit/b4beeff) [FAB-4927](https://jira.hyperledger.org/browse/FAB-4927) Modify TLS config
* [803da71](https://github.com/hyperledger/fabric-samples/commit/803da71) [FAB-4430](https://jira.hyperledger.org/browse/FAB-4430) - WYFA scripts and node code
* [241d08e](https://github.com/hyperledger/fabric-samples/commit/241d08e) [FAB-4910](https://jira.hyperledger.org/browse/FAB-4910) fix incorrect network spec
* [fbc120f](https://github.com/hyperledger/fabric-samples/commit/fbc120f) [FAB-4851](https://jira.hyperledger.org/browse/FAB-4851) basic network sample
* [3297865](https://github.com/hyperledger/fabric-samples/commit/3297865) [FAB-4073](https://jira.hyperledger.org/browse/FAB-4073) build your first network sample
* [54cae68](https://github.com/hyperledger/fabric-samples/commit/54cae68) [FAB-4853](https://jira.hyperledger.org/browse/FAB-4853) initial content
* [abb2b38](https://github.com/hyperledger/fabric-samples/commit/abb2b38) Initial empty repository
