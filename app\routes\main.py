from flask import Blueprint, render_template, redirect, url_for
from flask_login import login_required, current_user
from datetime import datetime, timedelta, time
import json
from sqlalchemy.sql.expression import or_
from app import db
from app.models.user import User
from app.models.survey import Survey, Response, Question, Answer
from app.models.blockchain import Block, Transaction

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """首页"""
    # 获取最新的几个活跃问卷
    active_surveys = Survey.query.filter_by(is_active=True).order_by(Survey.created_at.desc()).limit(5).all()
    return render_template('main/index.html', active_surveys=active_surveys)

@main_bp.route('/dashboard')
@login_required
def dashboard():
    """用户仪表盘"""
    # 获取用户创建的问卷
    user_surveys = Survey.query.filter_by(creator_id=current_user.id).order_by(Survey.created_at.desc()).all()
    
    # 获取用户参与的问卷
    from app.models.survey import Response
    participated_survey_ids = Response.query.filter_by(user_id=current_user.id).with_entities(Response.survey_id).distinct().all()
    participated_survey_ids = [id[0] for id in participated_survey_ids]
    participated_surveys = Survey.query.filter(Survey.id.in_(participated_survey_ids)).all() if participated_survey_ids else []
    
    # 添加当前时间，用于判断问卷是否过期
    now = datetime.utcnow()
    
    return render_template('main/dashboard.html', 
                           user_surveys=user_surveys,
                           participated_surveys=participated_surveys,
                           now=now)

@main_bp.route('/about')
def about():
    """关于页面"""
    return render_template('main/about.html')

@main_bp.route('/blockchain')
def blockchain_info():
    """区块链数据可视化页面"""
    from app import db
    from app.models.survey import Survey, Response
    from app.models.blockchain import Block, Transaction
    
    # 获取区块链数据
    blocks = Block.query.order_by(Block.index.desc()).limit(10).all()
    block_count = Block.query.count()
    
    # 增加区块的交易计数
    for block in blocks:
        block.transaction_count = Transaction.query.filter_by(block_id=block.id).count()
    
    # 获取交易总数和今日新增区块
    transactions_count = Transaction.query.count()
    today = datetime.now().date()
    today_start = datetime.combine(today, time.min)
    today_end = datetime.combine(today, time.max)
    block_mined_today = Block.query.filter(Block.timestamp.between(today_start, today_end)).count()
    
    # 获取问卷数据
    surveys = Survey.query.all()
    survey_count = len(surveys)
    active_surveys = Survey.query.filter_by(is_active=True).count()
    
    # 计算活跃问卷变化趋势
    yesterday = today - timedelta(days=1)
    yesterday_start = datetime.combine(yesterday, time.min)
    yesterday_end = datetime.combine(yesterday, time.max)
    active_yesterday = Survey.query.filter(
        Survey.created_at <= yesterday_end,
        or_(
            Survey.expiry_date.is_(None),
            Survey.expiry_date >= yesterday_start
        )
    ).count()
    active_surveys_change = active_surveys - active_yesterday
    
    # 获取响应数据
    responses = Response.query.count()
    
    # 计算响应增长百分比
    last_week = today - timedelta(days=7)
    last_week_responses = Response.query.filter(Response.submitted_at < today_start).count()
    responses_last_period = max(1, Response.query.filter(Response.submitted_at < last_week).count())
    responses_this_period = last_week_responses - responses_last_period
    responses_percent = ((responses_this_period / responses_last_period) * 100) if responses_last_period > 0 else 0
    
    # 最近响应
    recent_responses = Response.query.order_by(Response.submitted_at.desc()).limit(10).all()
    recent_responses_data = []
    
    for response in recent_responses:
        survey = Survey.query.get(response.survey_id)
        if survey:
            # 查找关联的区块链交易数据
            transaction = Transaction.query.filter_by(data=str(response.id)).first()
            blockchain_hash = transaction.hash if transaction else None
            
            # 获取回答者信息
            user_info = "匿名"
            if response.user_id:
                user = User.query.get(response.user_id)
                if user:
                    user_info = user.username
            
            recent_responses_data.append({
                'survey_title': survey.title,
                'submitted_at': response.submitted_at.strftime('%Y-%m-%d %H:%M'),
                'blockchain_hash': blockchain_hash,
                'user': user_info
            })
    
    # 生成过去30天的日期列表和趋势数据
    trend_dates = []
    trend_counts = []
    
    for i in range(29, -1, -1):
        date = (datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d')
        trend_dates.append(date)
        
        # 使用日期区间查询
        start_date = datetime.strptime(f"{date} 00:00:00", '%Y-%m-%d %H:%M:%S')
        end_date = datetime.strptime(f"{date} 23:59:59", '%Y-%m-%d %H:%M:%S')

        count = Response.query.filter(
            Response.submitted_at >= start_date,
            Response.submitted_at <= end_date
        ).count()

        trend_counts.append(count)

    # 问卷类型分布
    survey_types = {
        '应急管理': 0,
        '满意度调查': 0,
        '信息收集': 0,
        '意见反馈': 0,
        '其他': 0
    }
    
    # 统计问卷类型分布
    all_surveys = Survey.query.all()
    for survey in all_surveys:
        if '应急' in survey.title or '应急' in survey.description:
            survey_types['应急管理'] += 1
        elif '满意度' in survey.title or '满意度' in survey.description:
            survey_types['满意度调查'] += 1
        elif '信息' in survey.title or '收集' in survey.description:
            survey_types['信息收集'] += 1
        elif '意见' in survey.title or '反馈' in survey.description:
            survey_types['意见反馈'] += 1
        else:
            survey_types['其他'] += 1
    
    survey_types_data = [{"name": k, "value": v} for k, v in survey_types.items()]
    
    # 响应时间分布
    time_slots = ["00:00-04:00", "04:00-08:00", "08:00-12:00", "12:00-16:00", "16:00-20:00", "20:00-24:00"]
    time_distribution = [0, 0, 0, 0, 0, 0]
    
    # 获取所有响应数据
    all_responses = Response.query.all()
    for response in all_responses:
        hour = response.submitted_at.hour
        slot = hour // 4  # 将24小时分成6个时间段
        time_distribution[slot] += 1
    
    # 热门问卷统计 - 增强版数据
    popular_surveys = {}
    for response in all_responses:
        survey = Survey.query.get(response.survey_id)
        if not survey:
            continue
        
        survey_id = response.survey_id
        if survey_id in popular_surveys:
            popular_surveys[survey_id]['count'] += 1
            # 记录最新的提交时间，用于判断趋势
            if response.submitted_at > popular_surveys[survey_id]['last_response']:
                popular_surveys[survey_id]['last_response'] = response.submitted_at
        else:
            # 确定问卷类型
            survey_type = "其他"
            if '应急' in survey.title:
                survey_type = "应急管理"
            elif '满意度' in survey.title:
                survey_type = "满意度调查"
            elif '信息' in survey.title:
                survey_type = "信息收集"
            elif '意见' in survey.title or '反馈' in survey.title:
                survey_type = "意见反馈"
            
            creator = User.query.get(survey.creator_id)
            creator_name = creator.username if creator else "未知"
            
            # 查找区块链哈希
            transaction = Transaction.query.filter(Transaction.data.like(f"%{survey_id}%")).first()
            hash_value = transaction.hash if transaction else None
            
            popular_surveys[survey_id] = {
                'title': survey.title,
                'count': 1,
                'created_at': survey.created_at.strftime('%Y-%m-%d'),
                'is_active': survey.is_active,
                'type': survey_type,
                'creator': creator_name,
                'hash': hash_value,
                'last_response': response.submitted_at,
                'trend_data': []  # 将填充趋势数据
            }
    
    # 为每个问卷生成趋势数据（模拟）
    for survey_id, data in popular_surveys.items():
        # 简单模拟：根据问卷最近提交时间，生成更可信的趋势数据
        days_since_last_response = (datetime.now() - data['last_response']).days
        trend = []
        for i in range(10):
            if days_since_last_response > 20:  # 长期无人回复的问卷，呈下降趋势
                trend.append(str(max(1, 10 - i)))
            elif days_since_last_response < 2:  # 近期有人回复的问卷，呈上升趋势
                trend.append(str(5 + i))
            else:  # 其他问卷，呈波动趋势
                trend.append(str(5 + (i % 5)))
        
        popular_surveys[survey_id]['trend_data'] = trend
    
    # 转换为前端所需格式并排序
    popular_surveys_data = []
    for survey_id, data in sorted(popular_surveys.items(), key=lambda x: x[1]['count'], reverse=True)[:10]:
        popular_surveys_data.append({
            'title': data['title'],
            'count': data['count'],
            'type': data['type'],
            'creator': data['creator'],
            'created_at': data['created_at'],
            'is_active': data['is_active'],
            'hash': data['hash'],
            'trend_data': data['trend_data']
        })
    
    return render_template(
        'main/blockchain_info.html',
        blocks=blocks,
        block_count=block_count,
        block_count_json=json.dumps(block_count),
        surveys=surveys,
        survey_count=survey_count,
        active_surveys=active_surveys,
        active_surveys_change=active_surveys_change,
        responses=responses,
        responses_percent=responses_percent,
        recent_responses=recent_responses_data,
        trend_dates=json.dumps(trend_dates),
        trend_counts=json.dumps(trend_counts),
        survey_types=json.dumps(survey_types_data),
        time_slots=json.dumps(time_slots),
        time_distribution=json.dumps(time_distribution),
        popular_surveys=json.dumps(popular_surveys_data),
        transactions_count=transactions_count,
        block_mined_today=block_mined_today
    )

@main_bp.route('/blockchain/test-3d')
def blockchain_3d_test():
    """区块链3D可视化兼容性测试页面"""
    return render_template('main/blockchain_3d_test.html') 