// 区块链信息页面JSON解析修复
document.addEventListener('DOMContentLoaded', function() {
    // 全局重建区块链数据
    window.blockchainData = [];
    
    try {
        console.log('尝试初始化区块链数据...');
        
        // 尝试从页面元素中获取数据
        var dataElement = document.getElementById('blockchain-data');
        if (dataElement && dataElement.dataset && dataElement.dataset.json) {
            var rawJson = dataElement.dataset.json;
            console.log('找到原始JSON数据');
            
            try {
                // 预处理JSON - 处理可能的特殊字符和转义问题
                rawJson = rawJson.replace(/\\u0022/g, '"')
                                  .replace(/\\u005C/g, '\\')
                                  .replace(/\\\\/g, '\\');
                
                // 解析JSON数据
                window.blockchainData = JSON.parse(rawJson);
                console.log('成功解析区块链数据：', window.blockchainData.length, '个区块');
            } catch (parseError) {
                console.error('解析区块链数据JSON失败:', parseError);
                
                // 尝试使用备用方法解析
                try {
                    console.log('尝试备用解析方法...');
                    // 尝试将内容作为JavaScript对象评估（注意：这通常不推荐，但在这种特定的受控环境下可以作为备选）
                    window.blockchainData = eval('(' + rawJson + ')');
                    console.log('备用方法成功，获取到', window.blockchainData.length, '个区块');
                } catch (evalError) {
                    console.error('备用解析方法也失败:', evalError);
                    
                    // 作为最后手段，创建一个空的或者模拟的数据结构
                    window.blockchainData = createFallbackData();
                }
            }
        } else {
            console.warn('未找到包含区块链数据的元素或数据为空，使用备用数据');
            window.blockchainData = createFallbackData();
        }
    } catch (e) {
        console.error('区块链数据初始化错误:', e);
        window.blockchainData = createFallbackData();
    }
    
    // 创建备用的区块链数据
    function createFallbackData() {
        console.log('创建备用区块链数据');
        // 生成一些模拟数据，让页面功能可以展示
        var fallbackData = [];
        for (var i = 0; i < 5; i++) {
            fallbackData.push({
                id: i,
                index: i,
                timestamp: new Date().toISOString(),
                hash: '0000' + Math.random().toString(16).substring(2, 10) + '...',
                previous_hash: i > 0 ? ('0000' + Math.random().toString(16).substring(2, 10) + '...') : '0',
                nonce: Math.floor(Math.random() * 10000),
                data: i === 0 ? 'Genesis Block' : JSON.stringify(['交易1', '交易2', '交易3'])
            });
        }
        return fallbackData;
    }
    
    // 延迟初始化所有图表，让数据有时间完全加载
    setTimeout(initAllCharts, 500);
    
    // 统一初始化所有图表
    function initAllCharts() {
        // 检查数据准备情况
        if (!window.blockchainData || !Array.isArray(window.blockchainData) || window.blockchainData.length === 0) {
            console.warn('区块链数据不可用或为空，使用备用数据初始化图表');
            window.blockchainData = createFallbackData();
        }
        
        console.log('开始初始化所有图表');
        
        // 包装每个图表初始化，增加错误处理
        try {
            if (typeof window.initBlockchainChart === 'function') {
                initWithErrorHandling(window.initBlockchainChart, 'blockchainChart', '区块链结构图');
            }
            
            if (typeof window.init3DBlockchainChart === 'function') {
                initWithErrorHandling(window.init3DBlockchainChart, 'blockchainChart3D', '3D区块链图');
            }
            
            if (typeof window.initTimeDistributionChart === 'function') {
                initWithErrorHandling(window.initTimeDistributionChart, 'timeDistributionChart', '时间分布图');
            }
            
            if (typeof window.initBlockSizeChart === 'function') {
                initWithErrorHandling(window.initBlockSizeChart, 'blockSizeChart', '区块大小图');
            }
            
            if (typeof window.initNonceDistributionChart === 'function') {
                initWithErrorHandling(window.initNonceDistributionChart, 'nonceDistributionChart', 'Nonce分布图');
            }
            
            if (typeof window.initTransactionTrendChart === 'function') {
                initWithErrorHandling(window.initTransactionTrendChart, 'transactionTrendChart', '交易趋势图');
            }
            
            // 更新统计信息
            updateStatistics();
        } catch (e) {
            console.error('初始化图表过程中发生错误:', e);
        }
    }
    
    // 包装图表初始化函数，添加错误处理
    function initWithErrorHandling(initFunction, containerId, chartName) {
        try {
            console.log('初始化', chartName);
            initFunction();
        } catch (e) {
            console.error(chartName + '初始化失败:', e);
            
            // 显示错误信息在对应容器中
            var container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = '<div class="alert alert-warning m-3">' +
                    '<i class="fas fa-exclamation-triangle me-2"></i>' +
                    chartName + '无法加载: ' + e.message +
                    '</div>';
            }
        }
    }
    
    // 更新页面上的统计信息
    function updateStatistics() {
        try {
            // 计算总交易数
            var totalTransactions = 0;
            for (var i = 1; i < window.blockchainData.length; i++) { // 跳过创世区块
                var block = window.blockchainData[i];
                if (block.data && typeof block.data === 'string' && block.data !== 'Genesis Block') {
                    // 尝试解析交易数据
                    try {
                        var transactions = JSON.parse(block.data);
                        if (Array.isArray(transactions)) {
                            totalTransactions += transactions.length;
                        }
                    } catch (e) {
                        // 如果解析失败，尝试其他方法估计交易数
                        var transCount = (block.data.match(/,/g) || []).length + 1;
                        totalTransactions += transCount;
                    }
                }
            }
            
            // 计算平均挖矿时间
            var avgMiningTime = "N/A";
            if (window.blockchainData.length > 1) {
                var lastBlockTime = new Date(window.blockchainData[window.blockchainData.length - 1].timestamp);
                var firstBlockTime = new Date(window.blockchainData[0].timestamp);
                var totalTimeMs = lastBlockTime - firstBlockTime;
                if (totalTimeMs > 0) {
                    var avgTimeMs = totalTimeMs / (window.blockchainData.length - 1);
                    avgMiningTime = Math.round(avgTimeMs / 1000) + " 秒";
                }
            }
            
            // 更新DOM元素
            var totalTransElement = document.getElementById('totalTransactions');
            if (totalTransElement) {
                totalTransElement.textContent = totalTransactions;
            }
            
            var avgTimeElement = document.getElementById('avgMiningTime');
            if (avgTimeElement) {
                avgTimeElement.textContent = avgMiningTime;
            }
        } catch (e) {
            console.error('更新统计信息失败:', e);
        }
    }
    
    // 修复3D图表初始化
    enhanceBlockchainCharts();
});

// 增强3D区块链图表和其他图表
function enhanceBlockchainCharts() {
    // 备份原始函数
    var original3DInit = window.init3DBlockchainChart;
    
    // 重写3D区块链图表初始化函数
        window.init3DBlockchainChart = function() {
            try {
            // 检查WebGL支持
            var canvas = document.createElement('canvas');
            var gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            
            if (!gl) {
                throw new Error('浏览器不支持WebGL，无法渲染3D图表');
            }
            
            // 检查数据
            if (!window.blockchainData || !Array.isArray(window.blockchainData) || window.blockchainData.length === 0) {
                throw new Error('区块链数据无效或为空');
            }
            
            // 检查容器
            var container = document.getElementById('blockchainChart3D');
            if (!container) {
                throw new Error('3D图表容器不存在');
            }
            
            // 如果原始3D初始化函数存在，调用它
            if (typeof original3DInit === 'function') {
                original3DInit();
            } else {
                // 否则创建一个简单的3D效果
                createSimple3DChart();
            }
        } catch (e) {
            console.error('3D图表初始化失败:', e);
            
            // 显示错误信息
            var container = document.getElementById('blockchainChart3D');
            if (container) {
                container.innerHTML = '<div class="alert alert-warning m-3 p-3">' +
                    '<i class="fas fa-exclamation-triangle me-2"></i>' +
                    '3D区块链可视化无法加载: ' + e.message +
                    '</div>';
            }
            
            // 显示备用2D可视化
            showFallback2DVisualization();
        }
    };
    
    // 创建简单的3D图表
    function createSimple3DChart() {
        var container = document.getElementById('blockchainChart3D');
        var myChart = echarts.init(container);
        
        var data = [];
        for (var i = 0; i < window.blockchainData.length; i++) {
            data.push({
                value: [i, Math.sin(i * 0.5) * 2, Math.cos(i * 0.5) * 2],
                name: '区块 #' + window.blockchainData[i].index
            });
        }
        
                var option = {
            tooltip: {},
            backgroundColor: '#0e1621',
            visualMap: {
                show: false,
                dimension: 2,
                min: -2,
                max: 2,
                inRange: {
                    color: ['#1E90FF', '#00BFFF']
                }
            },
            xAxis3D: { type: 'value' },
            yAxis3D: { type: 'value' },
            zAxis3D: { type: 'value' },
            grid3D: {
                viewControl: {
                    autoRotate: true,
                    autoRotateSpeed: 10
                }
            },
                    series: [{
                        type: 'scatter3D',
                data: data,
                symbolSize: 12,
                itemStyle: {
                    borderWidth: 1,
                    borderColor: 'rgba(255,255,255,0.8)'
                },
                emphasis: {
                    itemStyle: {
                        color: '#ff4500'
                    }
                }
            }]
        };
        
                myChart.setOption(option);
    }
    
    // 显示备用的2D可视化
    function showFallback2DVisualization() {
        var fallback = document.getElementById('fallback2DVisualization');
        if (fallback) {
            fallback.style.display = 'block';
            
            var content = document.getElementById('fallback2DContent');
            if (content && window.blockchainData && window.blockchainData.length > 0) {
                var html = '<div class="d-flex flex-nowrap overflow-auto">';
                
                for (var i = 0; i < Math.min(window.blockchainData.length, 5); i++) {
                    var block = window.blockchainData[i];
                    html += '<div class="static-block">';
                    html += '<div class="static-block-index">' + block.index + '</div>';
                    html += '<div class="small">哈希: <span class="hash-text">' + 
                            (block.hash ? block.hash.substring(0, 8) + '...' : 'N/A') + '</span></div>';
                    
                    if (i === 0) {
                        html += '<div class="badge bg-success mt-2">创世区块</div>';
                    } else {
                        var txCount = 0;
                        try {
                            if (block.data) {
                                txCount = (block.data.match(/,/g) || []).length + 1;
                            }
                        } catch (e) {}
                        html += '<div class="badge bg-info mt-2">交易数: ' + txCount + '</div>';
                    }
                    
                    html += '</div>';
                    
                    if (i < Math.min(window.blockchainData.length, 5) - 1) {
                        html += '<div class="static-arrow"><i class="fas fa-long-arrow-alt-right"></i></div>';
                    }
                }
                
                html += '</div>';
                content.innerHTML = html;
            }
        }
    }
    
    // 如果页面上没有定义基本图表函数，则提供默认的实现
    if (typeof window.initBlockchainChart !== 'function') {
        window.initBlockchainChart = function() {
            var container = document.getElementById('blockchainChart');
            if (!container) return;
            
            var myChart = echarts.init(container);
            var option = {
                tooltip: {},
                series: [{
                    type: 'graph',
                    layout: 'circular',
                    data: window.blockchainData.map(function(block, index) {
                        return {
                            name: '区块 #' + block.index,
                            value: block.hash ? block.hash.substring(0, 8) : 'N/A',
                            symbolSize: 50,
                            itemStyle: {
                                color: index === 0 ? '#91cc75' : '#5470c6'
                            }
                        };
                    }),
                    links: window.blockchainData.slice(1).map(function(_, index) {
                        return {
                            source: index,
                            target: index + 1
                        };
                    }),
                    roam: true,
                    label: {
                        show: true
                    },
                    lineStyle: {
                        color: '#5470c6',
                        width: 2
                    }
                }]
            };
            myChart.setOption(option);
        };
    }
}
    