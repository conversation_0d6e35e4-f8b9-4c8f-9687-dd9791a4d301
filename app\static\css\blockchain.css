/* 区块链信息页面样式 */
.blockchain-container {
    background-color: #0e1621;
    color: #e1e8ed;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    margin-bottom: 2rem;
    overflow: hidden;
}

.blockchain-title {
    color: #61dafb;
    font-weight: 600;
    text-shadow: 0 0 10px rgba(97, 218, 251, 0.5);
}

.chart-container {
    height: 360px;
    background-color: #162d3d;
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 1.5rem;
    position: relative;
}

.chart-title {
    color: #61dafb;
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

/* 3D视图容器的特殊样式 */
#blockchainChart3D {
    height: 400px;
}

/* 区块可视化样式 */
.static-block {
    background: linear-gradient(135deg, #162d3d, #1a3f5a);
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: 10px;
    margin: 10px;
    width: 120px;
    min-width: 120px;
    text-align: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.static-block:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
    border-color: #61dafb;
}

.static-block-index {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
    color: #61dafb;
}

.static-arrow {
    display: flex;
    align-items: center;
    color: #61dafb;
    padding: 0 5px;
    font-size: 1.2rem;
}

.hash-text {
    font-family: 'Courier New', monospace;
    color: #a5d6ff;
}

/* 挖矿演示区域 */
.mining-area {
    background-color: #162d3d;
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: 15px;
    margin-bottom: 1.5rem;
}

.mining-demo-box {
    height: 160px;
    overflow-y: auto;
    background-color: #0a1620;
    border-radius: 4px;
    padding: 10px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: #a5d6ff;
    margin-top: 10px;
}

.mining-log {
    margin-bottom: 5px;
    white-space: pre-wrap;
    word-break: break-all;
}

.nonce-value {
    color: #f0c674;
    font-weight: bold;
}

.hash-value {
    color: #8abeb7;
}

.hash-success {
    color: #b5bd68;
}

.info-card {
    background-color: #162d3d;
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: 15px;
    height: 100%;
    transition: all 0.3s ease;
}

.info-card:hover {
    background-color: #1a3f5a;
    transform: translateY(-5px);
}

.info-card-title {
    color: #61dafb;
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 0.75rem;
}

.info-card-value {
    font-size: 2rem;
    font-weight: 700;
    color: #ffffff;
}

.info-card-label {
    color: #a5d6ff;
    font-size: 0.9rem;
}

/* 区块链健康状态指示器 */
.health-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 5px;
}

.health-good {
    background-color: #b5bd68;
    box-shadow: 0 0 5px #b5bd68;
}

.health-warning {
    background-color: #f0c674;
    box-shadow: 0 0 5px #f0c674;
}

.health-bad {
    background-color: #cc6666;
    box-shadow: 0 0 5px #cc6666;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .chart-container {
        height: 300px;
    }
    
    #blockchainChart3D {
        height: 350px;
    }
}

/* 加载动画 */
.loading-spinner {
    text-align: center;
    padding: 2rem;
}

.spinner-border {
    color: #61dafb;
}

/* 淡入动画 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* 区块链交易数据表格 */
.transactions-table {
    background-color: #162d3d;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.transactions-table th {
    background-color: #1a3f5a;
    color: #61dafb;
    border-color: rgba(255, 255, 255, 0.1);
}

.transactions-table td {
    border-color: rgba(255, 255, 255, 0.1);
    vertical-align: middle;
}

.block-badge {
    background-color: #1a3f5a;
    color: #a5d6ff;
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
}

/* 页面动画 */
.page-transition-enter {
    opacity: 0;
}

.page-transition-enter-active {
    opacity: 1;
    transition: opacity 300ms;
}

.page-transition-exit {
    opacity: 1;
}

.page-transition-exit-active {
    opacity: 0;
    transition: opacity 300ms;
}

.block-card {
    margin-bottom: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}
.block-card:hover {
    transform: translateY(-5px);
}
.block-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #eee;
    padding: 15px;
    border-radius: 10px 10px 0 0;
}
.hash-text {
    font-family: monospace;
    word-break: break-all;
}
.valid-chain {
    color: #198754;
    font-weight: bold;
}
.invalid-chain {
    color: #dc3545;
    font-weight: bold;
}
/* ECharts容器样式 */
.echarts-container {
    width: 100%;
    height: 500px;
    background: #f8f9fa;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}
.echarts-3d-container {
    width: 100%;
    height: 600px;
    background: #0e1621;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
}
.echarts-small-container {
    width: 100%;
    height: 300px;
    background: #f8f9fa;
    border-radius: 10px;
    margin-bottom: 20px;
}
/* 区块链可视化样式 */
.blockchain-visualization {
    margin: 50px 0;
    overflow-x: auto;
}
.blockchain-container {
    min-width: 1000px;
    height: 250px;
    position: relative;
}
.block-visual {
    width: 180px;
    height: 200px;
    background-color: #fff;
    border: 2px solid #0d6efd;
    border-radius: 10px;
    position: absolute;
    box-shadow: 0 8px 16px rgba(13, 110, 253, 0.1);
    transition: all 0.3s;
    text-align: center;
    padding: 10px;
    overflow: hidden;
}
.block-visual:hover {
    transform: translateY(-10px);
    box-shadow: 0 14px 20px rgba(13, 110, 253, 0.2);
    z-index: 10;
}
.block-link {
    position: absolute;
    height: 2px;
    background-color: #0d6efd;
    z-index: 1;
}
.block-link-arrow {
    position: absolute;
    width: 0;
    height: 0;
    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent;
    border-left: 12px solid #0d6efd;
    z-index: 1;
}
.block-hash-preview {
    font-family: monospace;
    font-size: 11px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
}
.block-index {
    background-color: #0d6efd;
    color: white;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    line-height: 30px;
    margin: 5px auto;
    font-weight: bold;
}
/* 挖矿动画 */
.mining-animation {
    border: 1px solid #eee;
    border-radius: 10px;
    padding: 20px;
    margin-top: 40px;
    background-color: #f8f9fa;
}
.nonce-container {
    font-family: monospace;
    margin: 20px 0;
}
.nonce-value {
    font-size: 24px;
    font-weight: bold;
    color: #0d6efd;
}
.hash-animation {
    font-family: monospace;
    margin: 10px 0;
    font-size: 12px;
    overflow-wrap: break-word;
}
.leading-zeros {
    color: #198754;
    font-weight: bold;
}
.hash-rest {
    color: #6c757d;
}
.difficulty-container {
    margin-top: 15px;
    font-family: monospace;
}
.target-zeros {
    color: #198754;
    font-weight: bold;
}
/* 区块详情折叠面板 */
.block-details-accordion {
    margin-top: 50px;
}
.transaction-link {
    display: block;
    margin: 5px 0;
    font-family: monospace;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
/* 区块链健康状态卡片 */
.blockchain-health {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 30px;
    transition: transform 0.3s;
}
.blockchain-health:hover {
    transform: translateY(-5px);
}
/* 数据安全动画 */
.data-security-animation {
    margin: 30px 0;
    text-align: center;
}
.encryption-container {
    display: inline-block;
    position: relative;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 10px;
    margin-bottom: 20px;
}
.data-packet {
    display: inline-block;
    padding: 10px 15px;
    background-color: #e9ecef;
    border-radius: 5px;
    font-family: monospace;
    transition: all 0.5s;
}
.encrypted-data {
    background-color: #0dcaf0;
    color: white;
}
/* 交易可视化 */
.transactions-visualization {
    margin: 30px 0;
}
.transaction-dot {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #0dcaf0;
    margin: 0 3px;
}
/* 预渲染的区块链可视化 */
.static-blockchain {
    display: flex;
    justify-content: center;
    margin: 30px 0;
    overflow-x: auto;
    padding: 20px 0;
}
.static-block {
    width: 180px;
    height: 180px;
    border: 2px solid #0d6efd;
    border-radius: 10px;
    padding: 15px;
    margin: 0 15px;
    text-align: center;
    position: relative;
    background-color: white;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}
.static-block:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.15);
}
.static-arrow {
    margin: 0 -5px;
    display: flex;
    align-items: center;
    color: #0d6efd;
    font-size: 24px;
}
.static-block-index {
    background-color: #0d6efd;
    color: white;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    line-height: 36px;
    margin: 0 auto 10px;
    font-weight: bold;
} 