// 区块链信息页面JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // 初始化所有图表
    try {
        initBlockchainChart();
    } catch (e) {
        console.error('初始化区块链图表失败:', e);
    }
    
    try {
        init3DBlockchain();
    } catch (e) {
        console.error('初始化3D区块链图表失败:', e);
        // 如果3D初始化失败，尝试回退到2D
        try {
            init2DBlockchain();
        } catch (e2) {
            console.error('回退到2D区块链图表也失败:', e2);
        }
    }
    
    try {
        initTransactionsChart();
    } catch (e) {
        console.error('初始化交易图表失败:', e);
    }
    
    try {
        initMiningDemo();
    } catch (e) {
        console.error('初始化挖矿演示失败:', e);
    }
});

// 初始化区块链结构图表
function initBlockchainChart() {
    const chartDom = document.getElementById('blockchain-chart');
    if (!chartDom) return;
    
    const myChart = echarts.init(chartDom);
    
    // 获取区块链数据
    let blockchainData;
    try {
        blockchainData = JSON.parse(document.getElementById('blockchain-data').getAttribute('data-blockchain'));
    } catch (e) {
        console.error('解析区块链数据失败:', e);
        blockchainData = {
            nodes: [],
            links: []
        };
    }
    
    const option = {
        title: {
            text: '区块链结构',
            subtext: '区块关系可视化',
            left: 'center'
        },
        tooltip: {
            trigger: 'item',
            formatter: function(params) {
                if (params.dataType === 'node') {
                    return `区块 #${params.data.id}<br/>
                            哈希: ${params.data.hash ? params.data.hash.substring(0, 15) + '...' : '无'}<br/>
                            时间戳: ${params.data.timestamp || '无'}`;
                }
                return '';
            }
        },
        legend: {
            data: ['区块', '连接'],
            orient: 'vertical',
            right: 10,
            top: 20
        },
        series: [{
            name: '区块链',
            type: 'graph',
            layout: 'force',
            data: blockchainData.nodes || [],
            links: blockchainData.links || [],
            roam: true,
            label: {
                show: true,
                position: 'right',
                formatter: '{b}'
            },
            lineStyle: {
                color: '#0d6efd',
                width: 2,
                curveness: 0.3
            },
            itemStyle: {
                color: function(params) {
                    // 创世区块使用特殊颜色
                    if (params.dataIndex === 0) {
                        return '#dc3545';
                    }
                    return '#0d6efd';
                }
            },
            emphasis: {
                lineStyle: {
                    width: 5
                }
            },
            force: {
                repulsion: 100,
                gravity: 0.1,
                edgeLength: 150
            }
        }]
    };
    
    myChart.setOption(option);
    
    // 窗口大小变化时自动调整图表大小
    window.addEventListener('resize', function() {
        myChart.resize();
    });
}

// 初始化3D区块链可视化
function init3DBlockchain() {
    const chartDom = document.getElementById('blockchain-3d');
    if (!chartDom) return;
    
    // 检查是否支持WebGL
    if (!isWebGLSupported()) {
        throw new Error('浏览器不支持WebGL');
    }
    
    // 检查是否有echarts-gl
    if (!echarts.gl) {
        throw new Error('未加载echarts-gl库');
    }
    
    const myChart = echarts.init(chartDom);
    
    // 获取区块链数据
    let blockchainData;
    try {
        blockchainData = JSON.parse(document.getElementById('blockchain-data').getAttribute('data-blockchain'));
    } catch (e) {
        console.error('解析区块链数据失败:', e);
        blockchainData = {
            nodes: [],
            links: []
        };
    }
    
    // 准备3D数据
    const nodes3D = (blockchainData.nodes || []).map((node, index) => {
        return {
            name: `区块 #${node.id}`,
            value: [index * 30, Math.random() * 40 - 20, Math.random() * 40 - 20],
            symbolSize: index === 0 ? 25 : 20,
            itemStyle: {
                color: index === 0 ? '#dc3545' : '#0d6efd'
            }
        };
    });
    
    const links3D = (blockchainData.links || []).map(link => {
        return {
            source: link.source,
            target: link.target
        };
    });
    
    const option = {
        backgroundColor: '#0e1621',
        globe: {
            environment: '#0e1621',
            baseTexture: '#0e1621',
            heightTexture: '#0e1621',
            displacementScale: 0,
            displacementQuality: 'high',
            shading: 'realistic',
            realisticMaterial: {
                roughness: 0.8,
                metalness: 0
            },
            postEffect: {
                enable: true,
                bloom: {
                    enable: true
                }
            },
            light: {
                main: {
                    intensity: 1,
                    shadow: true
                },
                ambient: {
                    intensity: 0.3
                }
            },
            viewControl: {
                autoRotate: true
            }
        },
        series: [{
            type: 'graphGL',
            nodes: nodes3D,
            edges: links3D,
            itemStyle: {
                color: '#0d6efd'
            },
            lineStyle: {
                color: '#6c757d',
                width: 2
            },
            forceAtlas2: {
                steps: 5,
                stopThreshold: 0.01,
                jitter: 0.1,
                gravity: 0.2,
                edgeWeight: [0.2, 1],
                edgeWeightInfluence: 1
            }
        }]
    };
    
    myChart.setOption(option);
    
    // 窗口大小变化时自动调整图表大小
    window.addEventListener('resize', function() {
        myChart.resize();
    });
}

// 2D区块链备用可视化（当3D加载失败时使用）
function init2DBlockchain() {
    const chartDom = document.getElementById('blockchain-3d');
    if (!chartDom) return;
    
    // 添加错误提示
    const errorMsg = document.createElement('div');
    errorMsg.style.position = 'absolute';
    errorMsg.style.top = '10px';
    errorMsg.style.left = '10px';
    errorMsg.style.color = '#fff';
    errorMsg.style.padding = '5px 10px';
    errorMsg.style.background = 'rgba(220, 53, 69, 0.8)';
    errorMsg.style.borderRadius = '5px';
    errorMsg.style.zIndex = '10';
    errorMsg.innerHTML = '3D视图不可用，已切换到2D视图';
    chartDom.parentNode.appendChild(errorMsg);
    
    const myChart = echarts.init(chartDom);
    
    // 获取区块链数据
    let blockchainData;
    try {
        blockchainData = JSON.parse(document.getElementById('blockchain-data').getAttribute('data-blockchain'));
    } catch (e) {
        console.error('解析区块链数据失败:', e);
        blockchainData = {
            nodes: [],
            links: []
        };
    }
    
    const option = {
        backgroundColor: '#0e1621',
        title: {
            text: '区块链结构（2D视图）',
            textStyle: {
                color: '#fff'
            },
            left: 'center'
        },
        tooltip: {
            trigger: 'item'
        },
        series: [{
            name: '区块链',
            type: 'graph',
            layout: 'circular',
            circular: {
                rotateLabel: true
            },
            data: blockchainData.nodes || [],
            links: blockchainData.links || [],
            roam: true,
            label: {
                show: true,
                position: 'right',
                color: '#fff',
                formatter: '{b}'
            },
            lineStyle: {
                color: '#6c757d',
                width: 1,
                curveness: 0.3
            },
            itemStyle: {
                color: function(params) {
                    if (params.dataIndex === 0) {
                        return '#dc3545';
                    }
                    return '#0d6efd';
                }
            },
            emphasis: {
                lineStyle: {
                    width: 5
                }
            }
        }]
    };
    
    myChart.setOption(option);
    
    // 窗口大小变化时自动调整图表大小
    window.addEventListener('resize', function() {
        myChart.resize();
    });
}

// 初始化交易图表
function initTransactionsChart() {
    const chartDom = document.getElementById('transactions-chart');
    if (!chartDom) return;
    
    const myChart = echarts.init(chartDom);
    
    // 获取交易数据
    let transactionsData;
    try {
        transactionsData = JSON.parse(document.getElementById('blockchain-data').getAttribute('data-transactions'));
    } catch (e) {
        console.error('解析交易数据失败:', e);
        transactionsData = [];
    }
    
    // 统计每个区块的交易数量
    const blockIds = [];
    const transactionCounts = [];
    
    // 如果数据为空，生成模拟数据
    if (!transactionsData || transactionsData.length === 0) {
        for (let i = 0; i < 10; i++) {
            blockIds.push(`区块 #${i}`);
            transactionCounts.push(Math.floor(Math.random() * 10));
        }
    } else {
        // 处理真实数据
        const blockTransactions = {};
        
        transactionsData.forEach(transaction => {
            const blockId = transaction.block_id || 0;
            if (!blockTransactions[blockId]) {
                blockTransactions[blockId] = 0;
            }
            blockTransactions[blockId]++;
        });
        
        // 转换为数组
        Object.keys(blockTransactions).forEach(blockId => {
            blockIds.push(`区块 #${blockId}`);
            transactionCounts.push(blockTransactions[blockId]);
        });
    }
    
    const option = {
        title: {
            text: '区块交易分布',
            left: 'center'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: blockIds
        },
        yAxis: {
            type: 'value',
            name: '交易数量'
        },
        series: [{
            name: '交易数量',
            type: 'bar',
            data: transactionCounts,
            itemStyle: {
                color: '#0d6efd'
            }
        }]
    };
    
    myChart.setOption(option);
    
    // 窗口大小变化时自动调整图表大小
    window.addEventListener('resize', function() {
        myChart.resize();
    });
}

// 初始化挖矿演示
function initMiningDemo() {
    const startButton = document.getElementById('start-mining');
    const stopButton = document.getElementById('stop-mining');
    const nonceElement = document.getElementById('nonce-value');
    const hashElement = document.getElementById('current-hash');
    const difficultyElement = document.getElementById('target-difficulty');
    
    if (!startButton || !stopButton || !nonceElement || !hashElement || !difficultyElement) {
        return;
    }
    
    let mining = false;
    let nonce = 0;
    let miningInterval;
    
    // 设置目标难度（前导零的个数）
    const targetDifficulty = 3;
    difficultyElement.textContent = targetDifficulty;
    
    // 简化的SHA-256哈希函数模拟
    function simulateHash(data, nonce) {
        // 实际应用中应该使用真正的SHA-256算法
        // 这里做了简化模拟
        const combined = `${data}-${nonce}`;
        let hash = '';
        
        // 生成一个64字符的模拟哈希
        for (let i = 0; i < 64; i++) {
            const char = Math.floor(Math.random() * 16).toString(16);
            hash += char;
        }
        
        // 根据nonce值确定前导零的数量
        // 使得大约每1000次尝试能找到一个满足条件的哈希
        if (nonce % 1000 === 0) {
            hash = '0'.repeat(targetDifficulty) + hash.substring(targetDifficulty);
        }
        
        return hash;
    }
    
    // 检查哈希是否满足难度要求
    function checkDifficulty(hash, difficulty) {
        const prefix = '0'.repeat(difficulty);
        return hash.startsWith(prefix);
    }
    
    // 更新哈希显示
    function updateHashDisplay(hash) {
        let leadingZeros = 0;
        for (let i = 0; i < hash.length; i++) {
            if (hash[i] === '0') {
                leadingZeros++;
            } else {
                break;
            }
        }
        
        if (leadingZeros > 0) {
            hashElement.innerHTML = `<span class="leading-zeros">${'0'.repeat(leadingZeros)}</span><span class="hash-rest">${hash.substring(leadingZeros)}</span>`;
        } else {
            hashElement.innerHTML = `<span class="hash-rest">${hash}</span>`;
        }
    }
    
    // 挖矿过程
    function mine() {
        if (!mining) return;
        
        nonce++;
        nonceElement.textContent = nonce;
        
        const data = "区块数据";
        const hash = simulateHash(data, nonce);
        
        updateHashDisplay(hash);
        
        if (checkDifficulty(hash, targetDifficulty)) {
            // 找到了符合条件的哈希
            mining = false;
            clearInterval(miningInterval);
            startButton.disabled = false;
            stopButton.disabled = true;
            
            // 显示成功信息
            const successMsg = document.createElement('div');
            successMsg.className = 'alert alert-success mt-3';
            successMsg.textContent = `成功找到符合难度要求的哈希! 使用的Nonce值: ${nonce}`;
            document.querySelector('.mining-animation').appendChild(successMsg);
            
            // 5秒后移除成功信息
            setTimeout(() => {
                successMsg.remove();
            }, 5000);
        }
    }
    
    // 启动挖矿
    startButton.addEventListener('click', function() {
        mining = true;
        startButton.disabled = true;
        stopButton.disabled = false;
        
        // 重置nonce和哈希
        nonce = 0;
        nonceElement.textContent = nonce;
        updateHashDisplay('0'.repeat(64));
        
        // 每100毫秒尝试一次
        miningInterval = setInterval(mine, 100);
    });
    
    // 停止挖矿
    stopButton.addEventListener('click', function() {
        mining = false;
        clearInterval(miningInterval);
        startButton.disabled = false;
        stopButton.disabled = true;
    });
    
    // 初始禁用停止按钮
    stopButton.disabled = true;
}

// 检查是否支持WebGL
function isWebGLSupported() {
    try {
        const canvas = document.createElement('canvas');
        return !!(window.WebGLRenderingContext && 
            (canvas.getContext('webgl') || canvas.getContext('experimental-webgl')));
    } catch (e) {
        return false;
    }
} 