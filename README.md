# 基于区块链的应急管理问卷调查系统

这是一个利用区块链技术保障数据安全和可靠性的应急管理问卷调查系统。系统采用B/S架构，使用Python+Flask开发后端，使用Vue.js开发前端，数据存储采用MySQL数据库，并集成了Hyperledger Fabric区块链技术确保数据安全性。

## 主要功能

1. 用户管理：注册、登录、修改个人信息、权限控制
2. 问卷管理：创建、编辑、发布、结束问卷
3. 区块链数据加密：确保问卷数据安全性和完整性
4. 数据可视化：通过图表等方式直观展示调查结果
5. 数据导出：支持多种格式导出问卷数据

## 系统架构

- 前端：HTML, CSS, JavaScript, Vue.js
- 后端：Python, Flask
- 数据库：MySQL
- 区块链：Hyperledger Fabric

## 目录结构

```
├── app/                    # 应用程序主目录
│   ├── blockchain/         # 区块链相关模块
│   ├── controllers/        # 控制器
│   ├── models/             # 数据模型
│   ├── routes/             # 路由定义
│   ├── static/             # 静态资源文件
│   ├── templates/          # 模板文件
│   └── utils/              # 工具函数
├── fabric/                 # Hyperledger Fabric区块链配置
├── migrations/             # 数据库迁移文件
├── add_admin_shell.py      # 添加管理员命令行工具
├── config.py               # 配置文件
├── create_admin.py         # 创建管理员账户
├── create_admin_interactive.py # 交互式创建管理员
├── fix_blockchain.py       # 修复区块链数据工具
├── generate_mock_data.py   # 生成模拟数据
├── requirements.txt        # 项目依赖
└── run.py                  # 应用启动入口
```

## 安装说明

### 1. 环境准备

- Python 3.8+
- MySQL 8.0+
- Docker和Docker Compose (用于运行Hyperledger Fabric)

### 2. 安装步骤

1. 克隆项目到本地

2. 创建并激活虚拟环境：
```
python -m venv .venv
.venv\Scripts\activate  # Windows
source .venv/bin/activate  # Linux/Mac
```

3. 安装依赖：
```
pip install -r requirements.txt
```

4. 创建环境变量文件：
在项目根目录创建`.env`文件并添加以下配置：
```
# 应用配置
SECRET_KEY=your_secret_key_here
DEBUG=True

# 数据库配置
DB_USERNAME=root
DB_PASSWORD=your_password_here
DB_HOST=localhost
DB_PORT=3306
DB_NAME=emergency_survey

# 区块链配置
BLOCKCHAIN_DIFFICULTY=4
BLOCKCHAIN_MINING_REWARD=10
BLOCKCHAIN_SECRET_KEY=your_blockchain_key_here

# 管理员账户
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin_password
```

5. 创建数据库：
```
mysql -u root -p
CREATE DATABASE emergency_survey CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
EXIT;
```

6. 初始化数据库：
```
flask db init
flask db migrate
flask db upgrade
```

7. 创建管理员账户：
```
python create_admin.py
```
或使用交互式脚本：
```
python create_admin_interactive.py
```

### 3. 配置区块链网络

1. 安装Docker和Docker Compose：
   - 从[Docker官网](https://www.docker.com/products/docker-desktop)下载并安装Docker Desktop

2. 启动Hyperledger Fabric网络：
```
cd fabric
start-network.bat  # Windows
bash start-network.sh  # Linux/Mac
```

3. 验证区块链网络状态：
```
docker ps
```
应看到以下容器运行中：
- peer0.org1.example.com
- orderer.example.com
- couchdb
- ca.org1.example.com

### 4. 运行应用

1. 启动应用：
```
python run.py
```
或
```
flask run
```

2. 访问应用：
浏览器访问 http://localhost:5000

## 区块链数据管理

1. 生成测试数据：
```
python generate_mock_data.py
```

2. 修复区块链数据（如有需要）：
```
python fix_blockchain.py
```

## 系统安全性

1. 所有问卷数据通过区块链技术加密存储
2. 采用防SQL注入等安全措施
3. 基于角色的访问控制系统
4. 敏感数据加密处理

## 故障排除

1. 区块链网络连接问题：
   - 确认所有Docker容器正在运行
   - 检查`.env`文件中的区块链配置
   - 使用`fix_blockchain.py`修复区块链数据

2. 数据库连接问题：
   - 检查MySQL服务是否运行
   - 验证`.env`文件中的数据库连接信息

3. 前端资源加载问题：
   - 清除浏览器缓存
   - 检查应用日志中的错误信息 