{% extends "base.html" %}

{% block title %}区块链数据可视化平台 - 基于区块链的应急管理问卷调查系统{% endblock %}

{% block extra_css %}
<!-- 引入 ECharts -->
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.2/dist/echarts.min.js"></script>
<!-- 引入ECharts GL库，用于3D可视化 -->
<script src="https://cdn.jsdelivr.net/npm/echarts-gl@2.0.9/dist/echarts-gl.min.js"></script>
<!-- 自定义样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/blockchain.css') }}">
<style>
  :root {
    --primary-color: #1f6fff;
    --secondary-color: #05c19c;
    --dark-color: #0e2638;
    --light-color: #f8f9fa;
    --graph-color-1: #4b91ff;
    --graph-color-2: #04d182;
    --graph-color-3: #ff6d4a;
    --graph-color-4: #ffc142;
  }
  
  body {
    background-color: #f5f8fa;
  }
  
  .blockchain-container {
    background-color: #ffffff;
    border-radius: 12px;
    box-shadow: 0 2px 15px rgba(0,0,0,0.05);
    overflow: hidden;
    margin-bottom: 30px;
  }
  
  .blockchain-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 20px;
    position: relative;
    overflow: hidden;
    border-radius: 12px 12px 0 0;
  }
  
  .blockchain-header::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E") repeat;
    opacity: 0.3;
  }
  
  .data-card {
    border-radius: 10px;
    background: white;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    overflow: hidden;
  }
  
  .data-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  }
  
  .card-header {
    background: linear-gradient(to right, var(--primary-color), var(--primary-color));
    color: white;
    border-radius: 10px 10px 0 0 !important;
    border: none;
    padding: 15px 20px;
    font-weight: 600;
    letter-spacing: 0.5px;
  }
  
  .card-header h3 {
    margin: 0;
    font-size: 1.25rem;
    display: flex;
    align-items: center;
  }
  
  .chart-container {
    height: 340px;
    border-radius: 0 0 10px 10px;
    overflow: hidden;
  }
  
  .survey-stat {
    text-align: center;
    padding: 25px 15px;
    position: relative;
    overflow: hidden;
  }
  
  .survey-stat::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  }
  
  .stat-value {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 5px;
    position: relative;
  }
  
  .stat-label {
    color: #6c757d;
    font-size: 0.95rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 500;
  }
  
  .secure-badge {
    background-color: var(--secondary-color);
    color: white;
    font-size: 12px;
    padding: 3px 8px;
    border-radius: 12px;
    margin-left: 5px;
    display: inline-flex;
    align-items: center;
  }
  
  .secure-badge i {
    margin-right: 3px;
  }
  
  .survey-list {
    max-height: 400px;
    overflow-y: auto;
    padding: 0;
  }
  
  .survey-item {
    padding: 15px;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    transition: background-color 0.2s;
  }
  
  .survey-item:hover {
    background-color: rgba(31, 111, 255, 0.03);
  }
  
  .survey-item:last-child {
    border-bottom: none;
  }
  
  .response-date {
    font-size: 12px;
    color: #6c757d;
  }
  
  .transaction-id {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #6c757d;
    word-break: break-all;
    background-color: rgba(0,0,0,0.03);
    padding: 5px 8px;
    border-radius: 4px;
    margin-top: 5px;
  }
  
  .blockchain-network {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.05;
    z-index: 0;
  }
  
  .section-title {
    position: relative;
    margin-bottom: 30px;
    padding-left: 15px;
    font-weight: 700;
    color: var(--dark-color);
  }
  
  .section-title::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 5px;
    height: 25px;
    background: linear-gradient(to bottom, var(--primary-color), var(--secondary-color));
    border-radius: 3px;
  }
  
  .security-feature {
    padding: 20px;
    border-radius: 10px;
    background-color: white;
    box-shadow: 0 4px 15px rgba(0,0,0,0.05);
    margin-bottom: 20px;
    transition: all 0.3s ease;
  }
  
  .security-feature:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  }
  
  .security-feature h5 {
    color: var(--dark-color);
    margin-bottom: 15px;
    font-weight: 600;
  }
  
  .security-feature i {
    color: var(--primary-color);
  }
  
  .hash-animation {
    font-family: 'Courier New', monospace;
    background-color: rgba(0, 0, 0, 0.03);
    padding: 8px 12px;
    border-radius: 4px;
    overflow-x: auto;
    white-space: nowrap;
    font-size: 12px;
    letter-spacing: -0.5px;
    color: #495057;
  }
  
  /* 向下滚动按钮 */
  .scroll-down {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: white;
    cursor: pointer;
    animation: bounce 2s infinite;
  }
  
  @keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
      transform: translateY(0) translateX(-50%);
    }
    40% {
      transform: translateY(-15px) translateX(-50%);
    }
    60% {
      transform: translateY(-7px) translateX(-50%);
    }
  }
  
  /* 区块动画 */
  .block-animation {
    position: relative;
    padding: 30px 0;
  }
  
  .block {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), #4a8eff);
    border-radius: 10px;
    position: relative;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    box-shadow: 0 5px 15px rgba(31, 111, 255, 0.3);
  }
  
  .block::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E") repeat;
    background-size: 15px 15px;
    border-radius: 10px;
  }
  
  .chain-line {
    width: 2px;
    height: 30px;
    background-color: rgba(31, 111, 255, 0.3);
    margin: 10px auto;
  }

  /* 新增样式 - 数据仪表盘 */
  .data-dashboard {
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border-radius: 12px;
    overflow: hidden;
  }
  
  .data-dashboard .nav-tabs {
    background-color: var(--primary-color);
    border-bottom: none;
  }
  
  .data-dashboard .nav-tabs .nav-link {
    color: rgba(255,255,255,0.7);
    border: none;
    border-radius: 0;
    padding: 15px 20px;
    transition: all 0.2s;
  }
  
  .data-dashboard .nav-tabs .nav-link:hover {
    background-color: rgba(255,255,255,0.1);
  }
  
  .data-dashboard .nav-tabs .nav-link.active {
    color: #fff;
    background-color: rgba(255,255,255,0.2);
    border: none;
    font-weight: 500;
  }
  
  .data-toolbar {
    border-bottom: 1px solid rgba(0,0,0,0.05);
  }
  
  /* 表格相关样式 */
  .table th {
    font-weight: 600;
    font-size: 0.85rem;
    color: #495057;
  }
  
  .blockchain-table tr:hover {
    background-color: #f8f9fa;
  }
  
  .hash-text {
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    letter-spacing: -0.5px;
    color: #6c757d;
  }
  
  /* 趋势小图样式 */
  .sparkline {
    width: 100px;
    height: 30px;
    display: inline-block;
  }
  
  /* 优化列表 */
  .modern-list {
    max-height: 400px;
    overflow-y: auto;
  }
  
  .modern-list .survey-item {
    transition: all 0.2s;
  }
  
  .modern-list .survey-item:hover {
    transform: translateX(5px);
  }
  
  .survey-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  /* 响应式调整 */
  @media (max-width: 768px) {
    .data-metrics {
      flex-direction: column;
      align-items: flex-start;
    }
    
    .data-metrics > div {
      margin-bottom: 15px;
    }
    
    .data-actions {
      margin-top: 15px;
      flex-direction: column;
      align-items: flex-start;
    }
    
    .data-actions .btn-group {
      margin-bottom: 10px;
    }
  }
</style>
{% endblock %}

{% block content %}
<!-- 存储数据的隐藏元素 -->
<div id="block-count-data" style="display:none;" data-count="{{ block_count|default(0) }}"></div>

<!-- 调试信息 -->
<div class="container mt-3 alert alert-info">
  <h5>调试信息</h5>
  <p>区块总数: {{ block_count }}</p>
  <p>趋势日期: {{ trend_dates }}</p>
  <p>趋势计数: {{ trend_counts }}</p>
  <p>问卷类型: {{ survey_types }}</p>
  <p>时间段: {{ time_slots }}</p>
  <p>时间分布: {{ time_distribution }}</p>
  <p>热门问卷: {{ popular_surveys }}</p>
</div>

<!-- 区块链头部 -->
<div class="blockchain-header py-5">
  <div class="container">
    <div class="row align-items-center">
      <div class="col-md-8">
        <h1 class="display-4 fw-bold mb-3">区块链数据可视化平台</h1>
        <p class="lead mb-4">基于区块链技术构建的分布式问卷调查系统，保证数据安全与完整性</p>
        <div class="d-flex align-items-center">
          <div class="me-4">
            <div class="d-flex align-items-center">
              <div class="spinner-grow spinner-grow-sm text-success me-2" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
              <span>区块链状态： <strong class="text-white">运行中</strong></span>
            </div>
          </div>
          <div>
            <div class="d-flex align-items-center">
              <span class="badge bg-light text-dark">区块总数：{{ block_count }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-4 d-none d-md-block">
        <div class="block-animation float-end">
          <div class="block">块 #{{ blocks[-1].index if blocks else '0' }}</div>
          <div class="chain-line"></div>
          <div class="block" style="opacity: 0.8; transform: scale(0.9)">块 #{{ blocks[-2].index if blocks and blocks|length > 1 else '0' }}</div>
          <div class="chain-line"></div>
          <div class="block" style="opacity: 0.6; transform: scale(0.8)">块 #{{ blocks[-3].index if blocks and blocks|length > 2 else '0' }}</div>
        </div>
      </div>
    </div>
    <div class="scroll-down">
      <div class="mb-1">查看详情</div>
      <i class="fas fa-chevron-down"></i>
    </div>
  </div>
</div>

<div class="container py-5">
  <!-- 区块链摘要信息 -->
  <div class="row mb-5">
    <div class="col-md-3">
      <div class="card data-card h-100">
        <div class="survey-stat">
          <div class="stat-value" id="total-surveys">{{ survey_count }}</div>
          <div class="stat-label">问卷总数</div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card data-card h-100">
        <div class="survey-stat">
          <div class="stat-value" id="total-responses">{{ responses }}</div>
          <div class="stat-label">总回复数</div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card data-card h-100">
        <div class="survey-stat">
          <div class="stat-value" id="active-surveys">{{ active_surveys }}</div>
          <div class="stat-label">进行中的问卷</div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card data-card h-100">
        <div class="survey-stat">
          <div class="stat-value">{{ block_count }}</div>
          <div class="stat-label">区块总数</div>
        </div>
      </div>
    </div>
  </div>

  <!-- 核心数据 -->
  <h2 class="section-title mb-4">核心数据分析</h2>
  
  <!-- 区块链可视化（2D版本） -->
  <div class="row mb-4">
    <div class="col-md-12">
      <div class="card data-card">
        <div class="card-header">
          <h3 class="mb-0"><i class="fas fa-project-diagram me-2"></i>区块链网络结构图</h3>
        </div>
        <div class="card-body p-0">
          <div class="chart-container" id="blockchain-3d-chart" style="height: 400px;"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- 问卷参与度趋势图 -->
  <div class="row mb-4">
    <div class="col-md-12">
      <div class="card data-card">
        <div class="card-header">
          <h3 class="mb-0"><i class="fas fa-chart-line me-2"></i>问卷参与度趋势</h3>
        </div>
        <div class="card-body p-0">
          <div class="chart-container" id="participation-trend-chart"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- 问卷调查分析图表 -->
  <div class="row mb-4">
    <div class="col-md-12">
      <div class="card data-card">
        <div class="card-header">
          <h3 class="mb-0"><i class="fas fa-chart-bar me-2"></i>问卷调查分析</h3>
        </div>
        <div class="card-body p-0">
          <div class="chart-container" id="survey-analysis-chart" style="height: 400px;"></div>
        </div>
      </div>
    </div>
  </div>

  <h2 class="section-title mb-4">详细数据展示</h2>

  <!-- 改进的详细数据展示区域，类似股票行情页面 -->
  <div class="data-dashboard mb-5">
    <!-- 数据卡片头部 -->
    <div class="card data-card">
      <div class="card-header bg-primary text-white p-0">
        <ul class="nav nav-tabs card-header-tabs border-0" id="dataTab" role="tablist">
          <li class="nav-item" role="presentation">
            <button class="nav-link text-white active" id="surveyData-tab" data-bs-toggle="tab" data-bs-target="#surveyData" type="button" role="tab" aria-controls="surveyData" aria-selected="true">问卷数据</button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link text-white" id="blockchainData-tab" data-bs-toggle="tab" data-bs-target="#blockchainData" type="button" role="tab" aria-controls="blockchainData" aria-selected="false">区块链数据</button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link text-white" id="relatedData-tab" data-bs-toggle="tab" data-bs-target="#relatedData" type="button" role="tab" aria-controls="relatedData" aria-selected="false">相关数据</button>
          </li>
        </ul>
      </div>
      <div class="card-body p-0">
        <div class="tab-content" id="dataTabContent">
          <!-- 问卷数据选项卡 -->
          <div class="tab-pane fade show active" id="surveyData" role="tabpanel" aria-labelledby="surveyData-tab">
            <!-- 自选问卷和快速查看区域 -->
            <div class="data-toolbar p-3 bg-light">
              <div class="row align-items-center">
                <div class="col-md-6">
                  <div class="data-metrics d-flex align-items-center">
                    <div class="me-4">
                      <div class="fs-4 fw-bold">{{ survey_count }}</div>
                      <div class="small text-muted">问卷总数</div>
                    </div>
                    <div class="me-4">
                      <div class="fs-4 fw-bold">{{ responses }} <span class="small text-success">+{{ '%0.2f'|format(responses_percent|float) }}%</span></div>
                      <div class="small text-muted">回复总数</div>
                    </div>
                    <div class="me-4">
                      <div class="fs-4 fw-bold">{{ active_surveys }} <span class="small text-{{ 'success' if active_surveys_change > 0 else 'danger' }}">{{ '%+d'|format(active_surveys_change|int) }}</span></div>
                      <div class="small text-muted">进行中问卷</div>
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="data-actions d-flex align-items-center justify-content-end">
                    <div class="btn-group me-2">
                      <button type="button" class="btn btn-sm btn-outline-primary active">日</button>
                      <button type="button" class="btn btn-sm btn-outline-primary">周</button>
                      <button type="button" class="btn btn-sm btn-outline-primary">月</button>
                      <button type="button" class="btn btn-sm btn-outline-primary">年</button>
                    </div>
                    <div class="input-group input-group-sm w-auto">
                      <input type="text" class="form-control" placeholder="搜索问卷...">
                      <button class="btn btn-outline-secondary" type="button"><i class="fas fa-search"></i></button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 问卷类型分布和响应时间分布 -->
            <div class="row g-0">
              <div class="col-md-6 border-end">
                <div class="p-3">
                  <h5 class="d-flex justify-content-between align-items-center">
                    <span><i class="fas fa-chart-pie me-2 text-primary"></i>问卷类型分布</span>
                    <button class="btn btn-sm btn-link text-decoration-none" data-bs-toggle="tooltip" title="查看详情">
                      <i class="fas fa-expand-alt"></i>
                    </button>
                  </h5>
                  <div class="chart-container" id="survey-type-chart" style="height: 350px;"></div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="p-3">
                  <h5 class="d-flex justify-content-between align-items-center">
                    <span><i class="fas fa-clock me-2 text-primary"></i>响应时间分布</span>
                    <button class="btn btn-sm btn-link text-decoration-none" data-bs-toggle="tooltip" title="查看详情">
                      <i class="fas fa-expand-alt"></i>
                    </button>
                  </h5>
                  <div class="chart-container" id="response-time-chart" style="height: 350px;"></div>
                </div>
              </div>
            </div>
            
            <!-- 热门问卷排行和最近响应数据表格 -->
            <div class="row g-0 border-top">
              <div class="col-12">
                <div class="p-3">
                  <h5 class="d-flex justify-content-between align-items-center">
                    <span><i class="fas fa-table me-2 text-primary"></i>问卷数据一览表</span>
                    <div>
                      <button class="btn btn-sm btn-outline-primary me-2" id="exportDataBtn">
                        <i class="fas fa-download me-1"></i>导出数据
                      </button>
                      <div class="btn-group">
                        <button class="btn btn-sm btn-primary">
                          <i class="fas fa-sync-alt me-1"></i>刷新
                        </button>
                      </div>
                    </div>
                  </h5>
                  
                  <div class="table-responsive">
                    <table class="table table-hover align-middle">
                      <thead class="table-light">
                        <tr>
                          <th scope="col">#</th>
                          <th scope="col">问卷标题</th>
                          <th scope="col">类型</th>
                          <th scope="col">创建者</th>
                          <th scope="col">回复数</th>
                          <th scope="col">趋势</th>
                          <th scope="col">状态</th>
                          <th scope="col">创建时间</th>
                        </tr>
                      </thead>
                      <tbody>
                        {% if popular_surveys %}
                          {% for survey in popular_surveys[:10] %}
                            <tr>
                              <td>{{ loop.index }}</td>
                              <td>
                                <div class="fw-medium">{{ survey.title }}</div>
                                <div class="small text-muted hash-text">{{ survey.hash[:12] if survey.hash else '无哈希值' }}</div>
                              </td>
                              <td><span class="badge bg-{{ ['primary', 'success', 'info', 'warning', 'secondary']|random }}">{{ survey.type }}</span></td>
                              <td>{{ survey.creator }}</td>
                              <td class="fw-bold">{{ survey.count }}</td>
                              <td>
                                <div class="sparkline" data-values="[{{ ','.join(survey.trend_data) if survey.trend_data else '5,8,12,8,10,5,15,12,8,10' }}]"></div>
                              </td>
                              <td>
                                {% if survey.is_active %}
                                  <span class="badge bg-success">活跃</span>
                                {% else %}
                                  <span class="badge bg-secondary">已结束</span>
                                {% endif %}
                              </td>
                              <td class="small">{{ survey.created_at }}</td>
                            </tr>
                          {% endfor %}
                        {% else %}
                          <tr>
                            <td colspan="8" class="text-center py-4 text-muted">
                              <i class="fas fa-info-circle me-2"></i>暂无问卷数据
                            </td>
                          </tr>
                        {% endif %}
                      </tbody>
                    </table>
                  </div>
                  
                  <nav aria-label="Page navigation" class="mt-3">
                    <ul class="pagination justify-content-end">
                      <li class="page-item disabled">
                        <a class="page-link" href="#" tabindex="-1" aria-disabled="true">上一页</a>
                      </li>
                      <li class="page-item active"><a class="page-link" href="#">1</a></li>
                      <li class="page-item"><a class="page-link" href="#">2</a></li>
                      <li class="page-item"><a class="page-link" href="#">3</a></li>
                      <li class="page-item">
                        <a class="page-link" href="#">下一页</a>
                      </li>
                    </ul>
                  </nav>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 区块链数据选项卡 -->
          <div class="tab-pane fade" id="blockchainData" role="tabpanel" aria-labelledby="blockchainData-tab">
            <div class="p-3">
              <div class="row align-items-center mb-4">
                <div class="col-md-6">
                  <div class="data-metrics d-flex align-items-center">
                    <div class="me-4">
                      <div class="fs-4 fw-bold">{{ block_count }}</div>
                      <div class="small text-muted">区块总数</div>
                    </div>
                    <div class="me-4">
                      <div class="fs-4 fw-bold">{{ block_mined_today|default('0') }}</div>
                      <div class="small text-muted">今日新增区块</div>
                    </div>
                    <div class="me-4">
                      <div class="fs-4 fw-bold">{{ '%0.2f'|format(block_avg_time|default(0)) }}</div>
                      <div class="small text-muted">平均出块时间(分钟)</div>
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="d-flex justify-content-end align-items-center">
                    <div class="btn-group" id="blockDataViewControls">
                      <button type="button" class="btn btn-sm btn-outline-primary" data-view="all">全部</button>
                      <button type="button" class="btn btn-sm btn-outline-primary active" data-view="recent">最近区块</button>
                      <button type="button" class="btn btn-sm btn-outline-primary" data-view="visual">可视化</button>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 区块链数据视图 -->
              <div id="blockDataViews">
                <!-- 表格视图 -->
                <div class="data-view active" id="recentBlocksView">
              <div class="table-responsive">
                <table class="table table-hover align-middle blockchain-table">
                  <thead class="table-light">
                    <tr>
                      <th scope="col">区块高度</th>
                      <th scope="col">区块哈希</th>
                      <th scope="col">前序哈希</th>
                          <th scope="col">问卷数据</th>
                      <th scope="col">Nonce</th>
                      <th scope="col">时间戳</th>
                      <th scope="col">操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    {% if blocks %}
                      {% for block in blocks %}
                            <tr class="block-row" data-block-id="{{ block.index }}">
                          <td class="fw-bold text-primary">{{ block.index }}</td>
                          <td class="hash-text">{{ block.hash[:16] }}...</td>
                          <td class="hash-text">{{ block.previous_hash[:16] }}...</td>
                              <td>{{ block.transaction_count|default('0') }} 条记录</td>
                          <td>{{ block.nonce }}</td>
                          <td>{{ block.timestamp }}</td>
                          <td>
                                <button class="btn btn-sm btn-outline-primary view-block-btn" data-bs-toggle="tooltip" title="查看详情" data-block-id="{{ block.index }}">
                              <i class="fas fa-eye"></i>
                            </button>
                          </td>
                        </tr>
                      {% endfor %}
                    {% else %}
                      <tr>
                        <td colspan="7" class="text-center py-4 text-muted">
                          <i class="fas fa-info-circle me-2"></i>暂无区块数据
                        </td>
                      </tr>
                    {% endif %}
                  </tbody>
                </table>
                  </div>
                </div>
                
                <!-- 3D可视化视图 -->
                <div class="data-view" id="visualView" style="display:none;">
                  <div class="row">
                    <div class="col-md-9">
                      <div class="card shadow-sm mb-4">
                        <div class="card-header bg-white d-flex justify-content-between align-items-center py-3">
                          <h5 class="m-0"><i class="fas fa-project-diagram me-2 text-primary"></i>区块链网络可视化</h5>
                          <div>
                            <select id="displayModeSelect" class="form-select form-select-sm me-2" style="width: auto; display: inline-block;">
                              <option value="chain">主链视图</option>
                              <option value="network">网络视图</option>
                              <option value="fork">分叉视图</option>
                              <option value="mining">挖矿视图</option>
                            </select>
                            <button class="btn btn-sm btn-outline-primary" id="refreshChartBtn">
                              <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                          </div>
                        </div>
                        <div class="card-body p-0">
                          <div id="blockchain-chart" style="height: 500px;"></div>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-3">
                      <div class="card shadow-sm mb-4">
                        <div class="card-header bg-white">
                          <h5 class="m-0"><i class="fas fa-info-circle me-2 text-primary"></i>区块信息</h5>
                        </div>
                        <div class="card-body">
                          <div id="block-details-panel">
                            <div class="text-center py-5 text-muted">
                              <i class="fas fa-hand-pointer mb-3" style="font-size: 2rem;"></i>
                              <p>点击区块查看详情</p>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div class="card shadow-sm">
                        <div class="card-header bg-white">
                          <h5 class="m-0"><i class="fas fa-chart-bar me-2 text-primary"></i>区块链统计</h5>
                        </div>
                        <div class="card-body">
                          <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                              总区块数
                              <span class="badge bg-primary rounded-pill" id="total-blocks">0</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                              主链长度
                              <span class="badge bg-success rounded-pill" id="main-chain-length">0</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                              待确认区块
                              <span class="badge bg-warning rounded-pill" id="pending-blocks">0</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                              孤立区块
                              <span class="badge bg-secondary rounded-pill" id="orphaned-blocks">0</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                              活跃矿工
                              <span class="badge bg-info rounded-pill" id="active-miners">0</span>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 区块链实时哈希流 -->
            <div class="border-top p-3">
              <h5 class="d-flex justify-content-between align-items-center mb-3">
                <span><i class="fas fa-stream me-2 text-primary"></i>区块链实时哈希流</span>
                <span class="badge bg-success">实时连接</span>
              </h5>
              <div class="hash-animation p-3 bg-light rounded" id="hash-stream">
                正在连接区块链网络...
              </div>
            </div>
          </div>
          
          <!-- 区块详情模态框 -->
          <div class="modal fade" id="blockDetailModal" tabindex="-1" aria-labelledby="blockDetailModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg modal-dialog-scrollable">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title" id="blockDetailModalLabel">区块详情</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="blockDetailContent">
                  <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                      <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-3">加载区块数据中...</p>
                  </div>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-sm btn-secondary" data-bs-dismiss="modal">关闭</button>
                  <button type="button" class="btn btn-sm btn-primary" id="prevBlockBtn">
                    <i class="fas fa-chevron-left me-1"></i>上一个区块
                  </button>
                  <button type="button" class="btn btn-sm btn-primary" id="nextBlockBtn">
                    下一个区块<i class="fas fa-chevron-right ms-1"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 相关数据选项卡 -->
          <div class="tab-pane fade" id="relatedData" role="tabpanel" aria-labelledby="relatedData-tab">
                <div class="p-3">
              <div class="row mb-4">
                <div class="col-md-6">
                  <div class="card h-100">
                    <div class="card-header bg-light">
                      <h5 class="mb-0"><i class="fas fa-chart-bar me-2 text-primary"></i>数据统计概览</h5>
                    </div>
                    <div class="card-body">
                      <div class="row">
                        <div class="col-6 mb-3">
                          <div class="stat-card bg-light p-3 rounded">
                            <h3 class="display-6 mb-0 fw-bold text-primary">{{ active_surveys }}</h3>
                            <p class="text-muted mb-0">活跃问卷</p>
                          </div>
                        </div>
                        <div class="col-6 mb-3">
                          <div class="stat-card bg-light p-3 rounded">
                            <h3 class="display-6 mb-0 fw-bold text-success">{{ responses }}</h3>
                            <p class="text-muted mb-0">回复总数</p>
                          </div>
                        </div>
                        <div class="col-6 mb-3">
                          <div class="stat-card bg-light p-3 rounded">
                            <h3 class="display-6 mb-0 fw-bold text-info">{{ block_count }}</h3>
                            <p class="text-muted mb-0">区块总数</p>
                          </div>
                        </div>
                        <div class="col-6 mb-3">
                          <div class="stat-card bg-light p-3 rounded">
                            <h3 class="display-6 mb-0 fw-bold text-warning">{{ '%0.1f'|format(responses_percent|float) }}%</h3>
                            <p class="text-muted mb-0">参与率</p>
                          </div>
                        </div>
                      </div>
                    </div>
                </div>
              </div>
              <div class="col-md-6">
                  <div class="card h-100">
                    <div class="card-header bg-light">
                      <h5 class="mb-0"><i class="fas fa-search me-2 text-primary"></i>问卷数据检索</h5>
                    </div>
                    <div class="card-body">
                      <form>
                        <div class="mb-3">
                          <label class="form-label">检索类型</label>
                          <select class="form-select">
                            <option>按问卷标题</option>
                            <option>按创建者</option>
                            <option>按问卷类型</option>
                            <option>按区块编号</option>
                            <option>按哈希值</option>
                          </select>
                        </div>
                        <div class="mb-3">
                          <div class="input-group">
                            <input type="text" class="form-control" placeholder="输入搜索关键词">
                            <button class="btn btn-primary">
                              <i class="fas fa-search"></i>
                    </button>
                              </div>
                              </div>
                        <div class="mb-3">
                          <label class="form-label">高级筛选</label>
                          <div class="row g-2">
                            <div class="col-6">
                              <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="activeCheck">
                                <label class="form-check-label" for="activeCheck">仅活跃问卷</label>
                            </div>
                            </div>
                            <div class="col-6">
                              <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="validatedCheck">
                                <label class="form-check-label" for="validatedCheck">已验证</label>
                          </div>
                            </div>
                        </div>
                      </div>
                      </form>
                  </div>
                </div>
              </div>
            </div>
              
              <h5 class="mb-3"><i class="fas fa-project-diagram me-2 text-primary"></i>区块链与问卷相关性分析</h5>
              
              <div class="row mb-4">
                <div class="col-md-7">
                  <div class="card h-100">
                    <div class="card-header bg-light">
                      <h6 class="mb-0">问卷在区块链上分布情况</h6>
          </div>
                    <div class="card-body">
                      <div class="chart-container" id="survey-distribution-chart" style="height: 300px;"></div>
                    </div>
                  </div>
                </div>
                <div class="col-md-5">
                  <div class="card h-100">
                    <div class="card-header bg-light">
                      <h6 class="mb-0">区块链与问卷数据关联数据</h6>
                    </div>
                    <div class="card-body">
                      <table class="table table-sm table-hover">
                        <thead>
                          <tr>
                            <th>指标</th>
                            <th>数值</th>
                            <th>趋势</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr>
                            <td>每区块平均问卷数</td>
                            <td>3.2</td>
                            <td><span class="badge bg-success"><i class="fas fa-arrow-up"></i> 5%</span></td>
                          </tr>
                          <tr>
                            <td>问卷确认时间</td>
                            <td>4.5 分钟</td>
                            <td><span class="badge bg-success"><i class="fas fa-arrow-down"></i> 12%</span></td>
                          </tr>
                          <tr>
                            <td>区块填充率</td>
                            <td>68%</td>
                            <td><span class="badge bg-warning"><i class="fas fa-arrow-up"></i> 3%</span></td>
                          </tr>
                          <tr>
                            <td>问卷数据安全指数</td>
                            <td>9.4/10</td>
                            <td><span class="badge bg-success"><i class="fas fa-equals"></i></span></td>
                          </tr>
                          <tr>
                            <td>数据一致性</td>
                            <td>99.9%</td>
                            <td><span class="badge bg-success"><i class="fas fa-arrow-up"></i> 0.2%</span></td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 热门问卷排行图 -->
              <div class="row mb-4">
                <div class="col-md-12">
                  <div class="card">
                    <div class="card-header bg-light">
                      <h6 class="mb-0"><i class="fas fa-fire me-2 text-primary"></i>热门问卷排行</h6>
                    </div>
                    <div class="card-body">
                      <div class="chart-container" id="popular-surveys-chart" style="height: 300px;"></div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="card mb-0">
                <div class="card-header bg-light">
                  <h5 class="mb-0"><i class="fas fa-code-branch me-2 text-primary"></i>问卷数据在区块链上的验证过程</h5>
                </div>
                <div class="card-body">
                  <div class="verification-flow">
                    <div class="row text-center">
                      <div class="col">
                        <div class="verification-step p-3 rounded bg-light mb-2">
                          <i class="fas fa-file-alt fa-2x text-primary mb-2"></i>
                          <h6>问卷创建</h6>
                        </div>
                        <i class="fas fa-arrow-right text-muted"></i>
                      </div>
                      <div class="col">
                        <div class="verification-step p-3 rounded bg-light mb-2">
                          <i class="fas fa-signature fa-2x text-primary mb-2"></i>
                          <h6>数据签名</h6>
                        </div>
                        <i class="fas fa-arrow-right text-muted"></i>
                      </div>
                      <div class="col">
                        <div class="verification-step p-3 rounded bg-light mb-2">
                          <i class="fas fa-network-wired fa-2x text-primary mb-2"></i>
                          <h6>广播到网络</h6>
                        </div>
                        <i class="fas fa-arrow-right text-muted"></i>
                      </div>
                      <div class="col">
                        <div class="verification-step p-3 rounded bg-light mb-2">
                          <i class="fas fa-check-double fa-2x text-primary mb-2"></i>
                          <h6>数据验证</h6>
                        </div>
                        <i class="fas fa-arrow-right text-muted"></i>
                      </div>
                      <div class="col">
                        <div class="verification-step p-3 rounded bg-light mb-2">
                          <i class="fas fa-cube fa-2x text-success mb-2"></i>
                          <h6>写入区块</h6>
                        </div>
                      </div>
                    </div>
                    <div class="text-center mt-3">
                      <button class="btn btn-outline-primary">
                        <i class="fas fa-play-circle me-2"></i>查看验证过程演示
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <h2 class="section-title mb-4">区块链安全机制</h2>

  <!-- 数据安全保障 -->
  <div class="row mb-5">
    <div class="col-md-6">
      <div class="security-feature">
        <h5><i class="fas fa-lock me-2"></i>数据加密保护</h5>
        <p>系统对所有问卷回答进行AES-256加密处理，保障数据安全。每份问卷回答都使用独特密钥进行加密，即使数据被截获也无法读取内容。</p>
      </div>
    </div>
    <div class="col-md-6">
      <div class="security-feature">
        <h5><i class="fas fa-shield-alt me-2"></i>分布式存储机制</h5>
        <p>问卷回答数据作为交易记录存储在区块链中，通过分布式账本技术和密码学算法，确保数据一旦提交就无法被篡改，保证数据的真实性和完整性。</p>
      </div>
    </div>
    <div class="col-md-6">
      <div class="security-feature">
        <h5><i class="fas fa-search me-2"></i>数据透明可追溯</h5>
        <p>每份问卷回答生成唯一的交易哈希值，任何时候都可以在区块链上通过哈希验证数据完整性，保证数据来源的可靠性，支持完整的数据审计。</p>
      </div>
    </div>
    <div class="col-md-6">
      <div class="security-feature">
        <h5><i class="fas fa-user-shield me-2"></i>个人隐私保护</h5>
        <p>系统支持零知识证明和匿名问卷模式，结合区块链技术的不可篡改特性，既能保障数据的真实可靠，又能最大限度保护参与者的个人隐私。</p>
      </div>
    </div>
  </div>

  <!-- 技术架构 -->
  <div class="row mb-5">
    <div class="col-md-12">
      <div class="card data-card">
        <div class="card-header">
          <h3 class="mb-0"><i class="fas fa-layer-group me-2"></i>区块链技术架构</h3>
        </div>
        <div class="card-body p-4">
          <div class="row align-items-center">
            <div class="col-md-6">
              <h5 class="mb-3">核心技术组件</h5>
              <ul class="list-group list-group-flush">
                <li class="list-group-item d-flex align-items-center">
                  <i class="fas fa-cubes me-3 text-primary"></i>
                  <div>
                    <strong>分布式账本</strong>
                    <p class="mb-0 small">支持数据不可篡改性和完整性验证</p>
                  </div>
                </li>
                <li class="list-group-item d-flex align-items-center">
                  <i class="fas fa-key me-3 text-primary"></i>
                  <div>
                    <strong>非对称加密</strong>
                    <p class="mb-0 small">确保数据传输和存储安全</p>
                  </div>
                </li>
                <li class="list-group-item d-flex align-items-center">
                  <i class="fas fa-link me-3 text-primary"></i>
                  <div>
                    <strong>哈希链接</strong>
                    <p class="mb-0 small">通过密码学哈希算法确保区块完整性</p>
                  </div>
                </li>
                <li class="list-group-item d-flex align-items-center">
                  <i class="fas fa-handshake me-3 text-primary"></i>
                  <div>
                    <strong>共识机制</strong>
                    <p class="mb-0 small">保证网络节点间数据一致性</p>
                  </div>
                </li>
              </ul>
            </div>
            <div class="col-md-6 text-center">
              <div id="blockchain-structure" style="height: 300px;"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 确保ECharts已加载
document.addEventListener('DOMContentLoaded', function() {
  // 图表初始化状态跟踪
  window.chartsInitialized = {
    blockchain3D: false,
    surveyAnalysis: false
  };
  
  // 检查echarts是否已加载
  if (typeof echarts === 'undefined') {
    console.error('ECharts库未加载');
    // 尝试加载ECharts
    const script = document.createElement('script');
    script.src = "https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js";
    script.onload = function() {
      console.log('ECharts库已动态加载');
      initCharts();
    };
    script.onerror = function() {
      console.error('无法加载ECharts库');
    };
    document.head.appendChild(script);
  } else {
    console.log('ECharts库已存在');
    initCharts();
  }
});

// 初始化所有图表的入口函数
function initCharts() {
  try {
    console.log("开始初始化所有图表...");
    
    // 设置默认空数组
    let trendDates = [];
    let trendCounts = [];
    let surveyTypes = [];
    let timeSlots = [];
    let timeDistribution = [];
    let popularSurveys = [];
    
    // 将后端传递的数据转换为JavaScript变量
    try {
      const trendDatesStr = '{{ trend_dates|safe }}';
      trendDates = trendDatesStr && trendDatesStr.trim() !== '' ? JSON.parse(trendDatesStr) : [];
      console.log("解析后 trendDates:", trendDates);
    
      const trendCountsStr = '{{ trend_counts|safe }}';
      trendCounts = trendCountsStr && trendCountsStr.trim() !== '' ? JSON.parse(trendCountsStr) : [];
      console.log("解析后 trendCounts:", trendCounts);
    
      const surveyTypesStr = '{{ survey_types|safe }}';
      surveyTypes = surveyTypesStr && surveyTypesStr.trim() !== '' ? JSON.parse(surveyTypesStr) : [];
      console.log("解析后 surveyTypes:", surveyTypes);
    
      const timeSlotsStr = '{{ time_slots|safe }}';
      timeSlots = timeSlotsStr && timeSlotsStr.trim() !== '' ? JSON.parse(timeSlotsStr) : [];
      console.log("解析后 timeSlots:", timeSlots);
    
      const timeDistributionStr = '{{ time_distribution|safe }}';
      timeDistribution = timeDistributionStr && timeDistributionStr.trim() !== '' ? JSON.parse(timeDistributionStr) : [];
      console.log("解析后 timeDistribution:", timeDistribution);
    
      const popularSurveysStr = '{{ popular_surveys|safe }}';
      popularSurveys = popularSurveysStr && popularSurveysStr.trim() !== '' ? JSON.parse(popularSurveysStr) : [];
      console.log("解析后 popularSurveys:", popularSurveys);
    } catch (e) {
      console.error("解析数据失败:", e);
    }
    
    // 初始化所有图表
    initBlockchain3DChart();
    initParticipationTrendChart(trendDates, trendCounts);
    initSurveyTypeChart(surveyTypes);
    initResponseTimeChart(timeSlots, timeDistribution);
    initPopularSurveysChart(popularSurveys);
    initBlockchainStructure();
    initHashStream();
    initSurveyDistributionChart();
    
    // 初始化区块链数据视图
    initBlockchainDataViews();
    
    // 初始化问卷调查分析图表
    initSurveyAnalysisChart();
    
    // 初始化迷你趋势图
    initSparklines();
    
    // 初始化提示工具
    initTooltips();
    
    // 添加选项卡切换效果增强
    enhanceTabSwitching();
    
    // 添加表格导出功能
    initTableExport();
  
  // 平滑滚动
    const scrollDownBtn = document.querySelector('.scroll-down');
    if (scrollDownBtn) {
      scrollDownBtn.addEventListener('click', function() {
        const container = document.querySelector('.container.py-5');
        if (container) {
    window.scrollTo({
            top: container.offsetTop,
      behavior: 'smooth'
    });
        }
      });
    }
    
    // 主动初始化区块链3D可视化和相关图表
    setTimeout(() => {
      // 切换到区块链数据选项卡
      const blockchainDataTab = document.getElementById('blockchainData-tab');
      if (blockchainDataTab) {
        console.log("主动切换到区块链数据选项卡");
        // 使用Bootstrap的Tab API触发切换
        const bsTab = new bootstrap.Tab(blockchainDataTab);
        bsTab.show();
        
        // 然后点击可视化按钮
        setTimeout(() => {
          const visualBtn = document.querySelector('#blockDataViewControls button[data-view="visual"]');
          if (visualBtn) {
            console.log("主动点击可视化按钮");
            visualBtn.click();
            
            // 确保问卷数据分析图表被正确初始化
            setTimeout(() => {
              if (document.getElementById('blockchain-survey-analysis-chart')) {
                console.log("主动初始化问卷数据分析图表");
                // 直接调用初始化函数
                initSurveyAnalysisForBlockchain();
              }
            }, 300);
          }
        }, 500);
      }
    }, 1000);
  } catch (error) {
    console.error("初始化所有图表时发生错误:", error);
  }
}

// 初始化迷你趋势图
function initSparklines() {
  const sparklineElements = document.querySelectorAll('.sparkline');
  
  sparklineElements.forEach(element => {
    const dataValues = element.getAttribute('data-values');
    if (dataValues) {
      try {
        const values = JSON.parse(dataValues);
        const chartDom = element;
        const myChart = echarts.init(chartDom);
        
        const option = {
          xAxis: {
            type: 'category',
            show: false
          },
          yAxis: {
            type: 'value',
            show: false
          },
          grid: {
            left: 0,
            right: 0,
            top: 0,
            bottom: 0
          },
          series: [{
            data: values,
            type: 'line',
            showSymbol: false,
            lineStyle: {
              width: 2,
              color: '#1f6fff'
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(31, 111, 255, 0.5)' },
                { offset: 1, color: 'rgba(31, 111, 255, 0.1)' }
              ])
            }
          }]
        };
        
        myChart.setOption(option);
      } catch (e) {
        console.error("迷你趋势图初始化失败:", e);
      }
    }
  });
}

// 初始化提示工具
function initTooltips() {
  const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
  tooltipTriggerList.map(function(tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
  });
}

// 增强选项卡切换效果
function enhanceTabSwitching() {
  const tabEls = document.querySelectorAll('button[data-bs-toggle="tab"]');
  
  tabEls.forEach(tabEl => {
    tabEl.addEventListener('shown.bs.tab', function(event) {
      // 重新调整图表大小
      const targetId = event.target.getAttribute('data-bs-target');
      const targetPane = document.querySelector(targetId);
      
      if (targetPane) {
        const charts = targetPane.querySelectorAll('.chart-container');
        charts.forEach(chart => {
          const chartInstance = echarts.getInstanceByDom(chart);
          if (chartInstance) {
            chartInstance.resize();
          }
        });
      }
      
      // 添加动画效果
      const contentElements = targetPane.querySelectorAll('.row, .table-responsive, h5');
      contentElements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
        element.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        
        setTimeout(() => {
          element.style.opacity = '1';
          element.style.transform = 'translateY(0)';
        }, index * 100);
      });
    });
  });
}

// 表格导出功能
function initTableExport() {
  const exportBtn = document.getElementById('exportDataBtn');
  if (exportBtn) {
    exportBtn.addEventListener('click', function() {
      const table = document.querySelector('.table');
      if (!table) return;
      
      let csvContent = "data:text/csv;charset=utf-8,";
      
      // 添加表头
      const headers = Array.from(table.querySelectorAll('th')).map(th => th.textContent.trim());
      csvContent += headers.join(',') + '\r\n';
      
      // 添加表格数据
      const rows = table.querySelectorAll('tbody tr');
      rows.forEach(row => {
        const rowData = Array.from(row.querySelectorAll('td')).map(cell => {
          // 获取单元格文本内容，移除额外空格和引号，以防止CSV格式问题
          let text = cell.textContent.trim().replace(/"/g, '""');
          // 如果含有逗号，用引号包裹
          if (text.includes(',')) {
            text = `"${text}"`;
          }
          return text;
        });
        csvContent += rowData.join(',') + '\r\n';
      });
      
      // 创建下载链接
      const encodedUri = encodeURI(csvContent);
      const link = document.createElement("a");
      link.setAttribute("href", encodedUri);
      link.setAttribute("download", "survey_data_" + new Date().toISOString().slice(0, 10) + ".csv");
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    });
  }
}

// 区块链可视化（2D版本）
function initBlockchain3DChart() {
  try {
    console.log("开始初始化区块链3D图表...");
  const chartDom = document.getElementById('blockchain-3d-chart');
    if (!chartDom) {
      console.error("找不到区块链3D图表容器元素");
      return;
    }
    
    // 确保图表容器有合适的尺寸
    if (chartDom.offsetHeight < 100 || chartDom.offsetWidth < 100) {
      console.warn("图表容器尺寸过小，设置默认尺寸");
      chartDom.style.height = '400px';
    }
    
    // 初始化ECharts实例
  const myChart = echarts.init(chartDom);
    console.log("ECharts实例已创建");
  
  // 读取页面上的数据
  const blockCountElement = document.getElementById('block-count-data');
  let blockCount = 0;
  
  try {
    if (blockCountElement) {
        console.log("找到区块数量元素:", blockCountElement);
      blockCount = parseInt(blockCountElement.getAttribute('data-count'), 10) || 0;
        console.log("解析的区块数量:", blockCount);
      } else {
        console.warn("找不到区块数量元素，使用默认值");
    }
  } catch (e) {
    console.error("解析区块数量时出错:", e);
  }
  
  console.log("区块数量:", blockCount);
  
    // 备用显示方式 - 如果使用图形网络布局出错，可以回退到此简单树状图
    try {
  // 创建节点数据
  const nodes = [];
  const links = [];
  
  // 创建足够的区块，至少5个
  const actualBlockCount = Math.max(blockCount, 5);
      console.log("实际使用的区块数量:", actualBlockCount);
  
  // 生成节点和链接
  for (let i = 0; i < actualBlockCount; i++) {
    // 生成一个随机的哈希值
    const hash = Array.from({length: 10}, () => 
      Math.floor(Math.random() * 16).toString(16)).join('');
    
    nodes.push({
      id: i,
      name: `块 #${i}`,
      symbolSize: i === 0 ? 60 : 40,
      value: hash,
      category: 0,
      itemStyle: {
        color: i === 0 ? '#ff6d4a' : '#4b91ff'
      }
    });
    
    // 添加链接，每个区块链接到下一个
    if (i < actualBlockCount - 1) {
      links.push({
        source: i,
        target: i + 1,
        lineStyle: {
          width: 2,
          color: '#5588ee',
          curveness: 0.3
        }
      });
    }
  }
  
  // 为一些区块添加交易
  for (let i = 0; i < actualBlockCount; i++) {
    const txCount = Math.floor(Math.random() * 3) + 1;
    
    for (let j = 0; j < txCount; j++) {
      const txId = `tx-${i}-${j}`;
      nodes.push({
        id: txId,
        name: `交易 ${j+1}`,
        value: Array.from({length: 8}, () => 
          Math.floor(Math.random() * 16).toString(16)).join(''),
        symbolSize: 20,
        category: 1,
        itemStyle: {
          color: '#04d182'
        }
      });
      
      links.push({
        source: i,
        target: txId,
        lineStyle: {
          width: 1,
          color: '#04d182',
          curveness: 0.2
        }
      });
    }
  }
      
      console.log("已生成节点和链接数据");
      console.log("节点数量:", nodes.length);
      console.log("链接数量:", links.length);
  
  const option = {
        title: {
          text: '区块链3D结构',
          left: 'center',
          top: 5,
          textStyle: {
            fontSize: 16
          }
        },
    tooltip: {
      formatter: function(params) {
        if (params.dataType === 'node') {
              const node = params.data;
              return `${node.name}<br/>ID: ${node.id}<br/>值: ${node.value}`;
            } else if (params.dataType === 'edge') {
              return `连接: ${params.data.source} → ${params.data.target}`;
        }
        return '';
      }
    },
    legend: [
      {
            data: ['区块', '问卷数据'],
            icon: 'circle',
            textStyle: {
              color: '#333'
            },
        right: 10,
        top: 20
      }
    ],
    series: [
      {
        type: 'graph',
        layout: 'force',
        data: nodes,
        links: links,
        categories: [
          { name: '区块' },
              { name: '问卷数据' }
        ],
        roam: true,
        label: {
          show: true,
          position: 'right',
          formatter: '{b}'
        },
        force: {
          repulsion: 100,
          edgeLength: [50, 100]
        },
        lineStyle: {
          color: '#999',
          opacity: 0.5,
          width: 1
        },
        emphasis: {
          focus: 'adjacency',
          lineStyle: {
            width: 5
          }
        }
      }
    ]
  };
  
      // 设置图表选项
      console.log("设置图表选项");
  myChart.setOption(option);
    } catch (graphError) {
      console.error("使用图形网络布局失败，切换到备用树状图:", graphError);
      
      // 备用选项 - 简单树状图
      const backupOption = {
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c}'
        },
        series: [
          {
            type: 'tree',
            data: [{
              name: '区块链',
              children: Array.from({length: Math.max(blockCount, 5)}, (_, i) => ({
                name: `区块 #${i}`,
                value: i,
                children: i === 0 ? 
                  [{ name: '创世块', value: '起源' }] : 
                  Array.from({length: Math.floor(Math.random() * 3) + 1}, (_, j) => ({
                    name: `交易 ${j+1}`,
                    value: `Hash-${Math.random().toString(36).substring(2, 10)}`
                  }))
              }))
            }],
            top: '5%',
            left: '7%',
            bottom: '2%',
            right: '20%',
            symbolSize: 20,
            orient: 'horizontal',
            label: {
              position: 'top',
              rotate: 0,
              verticalAlign: 'middle',
              align: 'right',
              fontSize: 12
            },
            leaves: {
              label: {
                position: 'right',
                verticalAlign: 'middle',
                align: 'left'
              }
            },
            emphasis: {
              focus: 'descendant'
            },
            expandAndCollapse: true,
            animationDuration: 550,
            animationDurationUpdate: 750
          }
        ]
      };
      
      myChart.setOption(backupOption);
    }
    
    // 添加窗口大小变化监听器
  window.addEventListener('resize', function() {
      if (myChart && !myChart.isDisposed()) {
    myChart.resize();
      }
    });
    
    console.log("区块链3D图表初始化完成");
  } catch (error) {
    console.error("区块链3D图表初始化错误:", error);
    
    // 极端情况下的降级方案，显示一个非常简单的提示
    try {
      const chartDom = document.getElementById('blockchain-3d-chart');
      if (chartDom) {
        chartDom.innerHTML = `
          <div class="d-flex justify-content-center align-items-center" style="height:400px;">
            <div class="text-center">
              <i class="fas fa-exclamation-triangle text-warning mb-3" style="font-size: 3rem;"></i>
              <h4>可视化加载失败</h4>
              <p class="text-muted">图表加载遇到问题，请刷新页面或检查控制台错误</p>
              <button class="btn btn-primary mt-3" onclick="location.reload()">
                <i class="fas fa-sync-alt me-2"></i>刷新页面
              </button>
            </div>
          </div>
        `;
      }
    } catch (finalError) {
      console.error("无法显示降级提示:", finalError);
    }
  }
}

// 问卷参与度趋势图
function initParticipationTrendChart(trendDates, trendCounts) {
  const chartDom = document.getElementById('participation-trend-chart');
  if (!chartDom) {
    console.warn("找不到participation-trend-chart元素，跳过初始化问卷参与度趋势图");
    return;
  }
  
  const myChart = echarts.init(chartDom);
  
  // 检查是否有数据，如果没有则使用默认数据
  if (!trendDates || !trendDates.length || !trendCounts || !trendCounts.length) {
    console.log("使用默认参与度趋势数据");
    // 创建默认数据 - 最近30天
    trendDates = [];
    trendCounts = [];
    
    const today = new Date();
    for (let i = 29; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(today.getDate() - i);
      const dateStr = date.toISOString().substring(0, 10);
      trendDates.push(dateStr);
      
      // 生成一些随机数据作为默认值
      trendCounts.push(Math.floor(Math.random() * 10));
    }
  }
  
  // 生成额外的数据以便展示更多维度
  const trendAverage = trendCounts.map(count => Math.max(Math.round(count * 0.8 * Math.random()), 0));
  const trendMax = trendCounts.map(count => Math.round(count * (1 + 0.5 * Math.random())));
  
  // 生成数据标记点 - 重要的参与高峰/活动
  const markPoints = [];
  for (let i = 0; i < trendCounts.length; i++) {
    if (trendCounts[i] > (Math.max(...trendCounts) * 0.7)) {
      markPoints.push({
        name: '参与高峰',
        value: trendCounts[i],
        xAxis: i,
        yAxis: trendCounts[i],
        itemStyle: {
          color: '#ff6d4a'
        }
      });
    }
  }
  
  // 添加一些额外的工具箱功能
  const option = {
    title: {
      text: '问卷参与趋势分析',
      subtext: '显示参与度变化和关键时间点',
      left: 'center',
      textStyle: {
        fontSize: 16,
        color: '#333'
      },
      subtextStyle: {
        fontSize: 12,
        color: '#999'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    legend: {
      data: ['参与人数', '平均完成率', '最高参与度'],
      bottom: 0
    },
    toolbox: {
      feature: {
        saveAsImage: {}
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: trendDates
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '参与人数',
        type: 'line',
        stack: '总量',
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(31, 111, 255, 0.5)' },
            { offset: 1, color: 'rgba(31, 111, 255, 0.1)' }
          ])
        },
        emphasis: {
          focus: 'series'
        },
        markPoint: {
          data: markPoints
        },
        markLine: {
          data: [
            { type: 'average', name: '平均值' }
          ]
        },
        data: trendCounts,
        lineStyle: {
          width: 3,
          color: '#1f6fff'
        },
        symbol: 'circle',
        symbolSize: 8
      },
      {
        name: '平均完成率',
        type: 'line',
        stack: '总量',
        emphasis: {
          focus: 'series'
        },
        data: trendAverage,
        lineStyle: {
          width: 2,
          color: '#04d182',
          type: 'dashed'
        },
        symbol: 'circle',
        symbolSize: 6
      },
      {
        name: '最高参与度',
        type: 'line',
        stack: '总量',
        emphasis: {
          focus: 'series'
        },
        data: trendMax,
        lineStyle: {
          width: 2,
          color: '#ff6d4a',
          type: 'dotted'
        },
        symbol: 'circle',
        symbolSize: 6
      }
    ]
  };
  
  myChart.setOption(option);
  
  window.addEventListener('resize', function() {
    if (myChart && !myChart.isDisposed()) {
    myChart.resize();
    }
  });
}

// 问卷类型分布图
function initSurveyTypeChart(surveyTypes) {
  const chartDom = document.getElementById('survey-type-chart');
  if (!chartDom) {
    console.warn("找不到survey-type-chart元素，跳过初始化问卷类型分布图");
    return;
  }
  
  const myChart = echarts.init(chartDom);
  
  // 检查是否有数据，如果没有则使用默认数据
  if (!surveyTypes || !surveyTypes.length) {
    console.log("使用默认问卷类型数据");
    // 创建默认数据
    surveyTypes = [
      { name: '满意度调查', value: 35 },
      { name: '用户反馈', value: 20 },
      { name: '产品评价', value: 18 },
      { name: '教育问卷', value: 15 },
      { name: '市场研究', value: 12 }
    ];
  }
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      type: 'scroll',
      orient: 'horizontal',
      bottom: 10,
      data: surveyTypes.map(type => type.name)
    },
    series: [
      {
        name: '问卷类型',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: surveyTypes
      }
    ]
  };
  
  myChart.setOption(option);
  
  window.addEventListener('resize', function() {
    if (myChart && !myChart.isDisposed()) {
    myChart.resize();
    }
  });
}

// 响应时间分布图
function initResponseTimeChart(timeSlots, timeDistribution) {
  const chartDom = document.getElementById('response-time-chart');
  if (!chartDom) {
    console.warn("找不到response-time-chart元素，跳过初始化响应时间分布图");
    return;
  }
  
  const myChart = echarts.init(chartDom);
  
  // 检查是否有数据，如果没有则使用默认数据
  if (!timeSlots || !timeSlots.length || !timeDistribution || !timeDistribution.length) {
    console.log("使用默认响应时间分布数据");
    // 创建默认数据
    timeSlots = ['0-1分钟', '1-2分钟', '2-5分钟', '5-10分钟', '10-30分钟', '30+分钟'];
    timeDistribution = [10, 25, 35, 20, 8, 2];
  }
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: timeSlots,
      axisLabel: {
        interval: 0,
        rotate: 30
      }
    },
    yAxis: {
      type: 'value',
      name: '响应数量'
    },
    series: [
      {
      name: '响应数量',
      type: 'bar',
        data: timeDistribution.map((value, index) => {
          // 添加渐变色
          return {
            value: value,
      itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#1f6fff' },
                { offset: 1, color: '#4b91ff' }
              ])
            }
          };
        }),
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(180, 180, 180, 0.2)'
        }
      }
    ]
  };
  
  myChart.setOption(option);
  
  window.addEventListener('resize', function() {
    if (myChart && !myChart.isDisposed()) {
    myChart.resize();
    }
  });
}

// 热门问卷排行图
function initPopularSurveysChart(popularSurveys) {
  const chartDom = document.getElementById('popular-surveys-chart');
  if (!chartDom) {
    console.warn("找不到popular-surveys-chart元素，跳过初始化热门问卷排行图");
    return;
  }
  
  const myChart = echarts.init(chartDom);
  
  // 检查是否有数据，如果没有则使用默认数据
  if (!popularSurveys || !popularSurveys.length) {
    console.log("使用默认热门问卷数据");
    // 创建默认数据
    popularSurveys = [
      { title: '应急响应满意度调查', count: 145 },
      { title: '灾害防备意识调查', count: 120 },
      { title: '社区应急演练反馈', count: 98 },
      { title: '避难场所评估', count: 78 },
      { title: '应急物资储备调查', count: 65 }
    ];
  }
  
  const titles = popularSurveys.map(survey => survey.title);
  const counts = popularSurveys.map(survey => survey.count);
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '响应数量'
    },
    yAxis: {
      type: 'category',
      data: titles,
      axisLabel: {
        interval: 0,
        rotate: 0,
        formatter: function(value) {
          if (value.length > 10) {
            return value.substring(0, 10) + '...';
          }
          return value;
        }
      }
    },
    series: [{
      name: '响应数量',
      type: 'bar',
      data: counts.map(count => ({
        value: count,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#ffc142' },
            { offset: 1, color: '#ff6d4a' }
          ])
        }
      }))
    }]
  };
  
  myChart.setOption(option);
  
  window.addEventListener('resize', function() {
    myChart.resize();
  });
}

// 区块链结构图示
function initBlockchainStructure() {
  const chartDom = document.getElementById('blockchain-structure');
  if (!chartDom) {
    console.error("找不到blockchain-structure元素");
    return;
  }
  
  try {
    const myChart = echarts.init(chartDom);
    
    const nodes = [];
    const links = [];
    
    // 创建区块节点
    for (let i = 0; i < 6; i++) {
      nodes.push({
        id: `block-${i}`,
        name: `Block #${i}`,
        category: 0,
        symbolSize: 50,
        x: i * 100,
        y: 0
      });
      
      // 添加区块之间的链接
      if (i > 0) {
        links.push({
          source: `block-${i-1}`,
          target: `block-${i}`,
          lineStyle: {
            width: 2,
            curveness: 0
          }
        });
      }
      
      // 为每个区块添加交易节点
      const txCount = Math.floor(Math.random() * 3) + 1;
      for (let j = 0; j < txCount; j++) {
        nodes.push({
          id: `tx-${i}-${j}`,
          name: `Transaction ${j}`,
          category: 1,
          symbolSize: 20,
          x: i * 100,
          y: 50 + j * 30
        });
        
        links.push({
          source: `block-${i}`,
          target: `tx-${i}-${j}`,
          lineStyle: {
            width: 1,
            curveness: 0.2
          }
        });
      }
    }
    
    const option = {
      tooltip: {
        formatter: function(params) {
          if (params.dataType === 'node') {
            const type = params.data.id.startsWith('block') ? '区块' : '交易';
            return `${type}: ${params.data.name}`;
          }
          return '';
        }
      },
      legend: {
        show: false
      },
      series: [{
        type: 'graph',
        layout: 'none',
        data: nodes,
        links: links,
        roam: false,
        label: {
          show: false
        },
        categories: [
          { name: '区块' },
          { name: '交易' }
        ],
        itemStyle: {
          color: function(params) {
            return params.data.id.startsWith('block') ? '#1f6fff' : '#04d182';
          }
        },
        lineStyle: {
          color: '#999',
          opacity: 0.5,
          width: 1,
          curveness: 0
        },
        emphasis: {
          lineStyle: {
            width: 3
          }
        }
      }]
    };
    
    myChart.setOption(option);
    
    window.addEventListener('resize', function() {
      if (myChart && !myChart.isDisposed()) {
        myChart.resize();
      }
    });
  } catch (error) {
    console.error("初始化区块链结构图时出错:", error);
  }
}

// 区块链哈希流
function initHashStream() {
  const hashStreamEl = document.getElementById('hash-stream');
  if (!hashStreamEl) {
    console.error("找不到hash-stream元素");
    return;
  }
  
  try {
    // 生成随机哈希样式
    function generateRandomHash() {
      let hash = '';
      const chars = '0123456789abcdef';
      const length = 64; // SHA-256哈希长度
      
      for (let i = 0; i < length; i++) {
        hash += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      
      return hash;
    }
    
    // 生成带有高亮的哈希HTML
    function generateHashHTML(hash) {
      let html = '';
      const zeroCount = Math.min(4, Math.floor(Math.random() * 5));
      
      // 哈希前缀（难度）
      html += `<span style="color:#04d182; font-weight:bold;">${'0'.repeat(zeroCount)}</span>`;
      
      // 哈希剩余部分
      html += `<span>${hash.substring(zeroCount)}</span>`;
      
      return html;
    }
    
    // 生成随机区块信息
    function generateBlockInfo() {
      const blockIndex = Math.floor(Math.random() * 1000);
      const txCount = Math.floor(Math.random() * 10) + 1;
      const hashType = Math.random() > 0.7 ? '区块哈希' : '交易哈希';
      
      return `<span style="color:#1f6fff; font-weight:bold;">[${hashType}]</span> ` +
             `<span style="color:#6c757d;">块高度 #${blockIndex} | ${txCount}个交易 | </span>`;
    }
    
    // 创建哈希流
    let hashStream = '';
    const hashCount = 5;
    
    for (let i = 0; i < hashCount; i++) {
      const hash = generateRandomHash();
      const hashHTML = generateHashHTML(hash);
      const blockInfo = generateBlockInfo();
      
      hashStream += `<div>${blockInfo}${hashHTML}</div>`;
    }
    
    hashStreamEl.innerHTML = hashStream;
    
    // 动态更新哈希流
    const updateInterval = setInterval(function() {
      if (!document.body.contains(hashStreamEl)) {
        console.log("哈希流元素已被移除，停止更新");
        clearInterval(updateInterval);
        return;
      }
      
      const hash = generateRandomHash();
      const hashHTML = generateHashHTML(hash);
      const blockInfo = generateBlockInfo();
      
      const newHash = document.createElement('div');
      newHash.innerHTML = blockInfo + hashHTML;
      newHash.style.opacity = '0';
      newHash.style.transition = 'opacity 0.5s ease';
      
      hashStreamEl.prepend(newHash);
      
      setTimeout(() => {
        newHash.style.opacity = '1';
        
        // 删除最后一个哈希，保持数量不变
        if (hashStreamEl.children.length > hashCount) {
          const lastHash = hashStreamEl.lastElementChild;
          if (lastHash) {
            lastHash.style.opacity = '0';
            
            setTimeout(() => {
              if (lastHash.parentNode === hashStreamEl) {
                hashStreamEl.removeChild(lastHash);
              }
            }, 500);
          }
        }
      }, 50);
    }, 5000);
    
    // 页面卸载时清除定时器
    window.addEventListener('beforeunload', function() {
      clearInterval(updateInterval);
    });
  } catch (error) {
    console.error("初始化区块链哈希流时出错:", error);
    hashStreamEl.innerHTML = '<div class="text-danger">加载哈希流时出错</div>';
  }
}

// 区块链数据视图切换
function initBlockchainDataViews() {
  const viewControls = document.getElementById('blockDataViewControls');
  const dataViews = document.querySelectorAll('#blockDataViews .data-view');
  
  if (viewControls) {
    const viewButtons = viewControls.querySelectorAll('button');
    
    viewButtons.forEach(button => {
      button.addEventListener('click', function() {
        // 更新按钮状态
        viewButtons.forEach(btn => btn.classList.remove('active'));
        this.classList.add('active');
        
        // 获取视图类型
        const viewType = this.getAttribute('data-view');
        
        // 隐藏所有视图
        dataViews.forEach(view => {
          view.style.display = 'none';
          view.classList.remove('active');
        });
        
        // 显示选定的视图
        let targetView;
        switch(viewType) {
          case 'all':
          case 'recent':
            targetView = document.getElementById('recentBlocksView');
            break;
          case 'visual':
            targetView = document.getElementById('visualView');
            // 初始化可视化视图
            if (targetView) {
              if (!targetView.classList.contains('initialized')) {
                console.log("初始化可视化视图");
                targetView.classList.add('initialized');
              }
              
              // 确保视图先显示出来，再初始化图表
              targetView.style.display = 'block';
              targetView.classList.add('active');
              
              // 设置延迟执行图表初始化，确保DOM元素已经完全显示
              setTimeout(function() {
                // 先初始化3D视图
                if (!window.chartsInitialized || !window.chartsInitialized.blockchain3D) {
                  console.log("初始化3D视图");
                  // 确保先删除已有实例
                  const chartDom = document.getElementById('blockchain-3d-view');
                  if (chartDom) {
                    try {
                      const existingChart = echarts.getInstanceByDom(chartDom);
                      if (existingChart) {
                        existingChart.dispose();
                      }
                    } catch (e) {
                      console.error("销毁实例出错:", e);
                    }
                  }
                  initBlockchain3DView();
                } else {
                  // 强制重新初始化3D视图
                  console.log("强制重新初始化3D视图");
                  initBlockchain3DView();
                }
                
                // 延迟初始化问卷数据分析图表（确保DOM已完全渲染）
                setTimeout(function() {
                  if (!window.chartsInitialized || !window.chartsInitialized.surveyAnalysis) {
                    console.log("初始化问卷数据分析图表");
                    initSurveyAnalysisForBlockchain();
                  } else {
                    // 如果已经初始化，刷新一下图表大小
                    const chartDom = document.getElementById('blockchain-survey-analysis-chart');
                    if (chartDom) {
                      const chart = echarts.getInstanceByDom(chartDom);
                      if (chart) {
                        chart.resize();
                      }
                    }
                  }
                }, 200);
              }, 100);
              
              return; // 提前返回，避免下面的代码再次设置样式
            }
            break;
          default:
            targetView = document.getElementById('recentBlocksView');
        }
      });
    });
  }
}

// 问卷调查分析图表
function initSurveyAnalysisChart() {
  // 获取图表容器
  const chartContainer = document.getElementById('survey-analysis-chart');
  
  if (!chartContainer) {
    console.error('问卷分析图表容器不存在');
    return;
  }
  
  // 显示加载状态
  chartContainer.innerHTML = `
    <div class="text-center p-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
      </div>
      <p class="mt-2">正在加载问卷数据...</p>
    </div>
  `;
  
  // 初始化ECharts实例
  const myChart = echarts.init(chartContainer);
  
  // 从API获取问卷数据
  fetchSurveyData()
    .then(processSurveyData)
    .catch(handleError);
    
  // 获取问卷数据
  function fetchSurveyData() {
    return fetch('/api/surveys')
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP错误: ${response.status}`);
        }
        // 检查content-type
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
          throw new Error('返回数据格式错误，预期为JSON，实际为: ' + contentType);
        }
        return response.json();
      });
  }
  
  // 处理问卷数据
  function processSurveyData(data) {
    if (!data.success || !data.surveys || data.surveys.length === 0) {
      chartContainer.innerHTML = '<div class="alert alert-info">暂无问卷数据</div>';
      return;
    }
    
    console.log('获取到问卷数据:', data.surveys.length, '个问卷');
    
    // 处理问卷数据
    const surveyCategories = [];
    const categoryMapping = {
      '突发': '应急管理',
      '灾害': '应急管理',
      '应急': '应急管理',
      '满意度': '满意度调查',
      '服务': '满意度调查',
      '评价': '满意度调查',
      '信息收集': '信息收集',
      '调查': '信息收集',
      '意见': '意见反馈',
      '建议': '意见反馈',
      '反馈': '意见反馈'
    };
    
    // 分类问卷
    const categorizedSurveys = {
      '应急管理': 0,
      '满意度调查': 0,
      '信息收集': 0,
      '意见反馈': 0,
      '其他': 0
    };
    
    // 统计各问卷类别的数量和回复数
    const responseData = {
      '应急管理': [],
      '满意度调查': [],
      '信息收集': [],
      '意见反馈': [],
      '其他': []
    };
    
    // 遍历问卷进行分类和统计
    data.surveys.forEach(survey => {
      let category = '其他';
      
      // 根据标题确定分类
      if (survey.title) {
        for (const [keyword, categoryName] of Object.entries(categoryMapping)) {
          if (survey.title.includes(keyword)) {
            category = categoryName;
            break;
          }
        }
      }
      
      // 统计分类数量
      categorizedSurveys[category]++;
      
      // 添加回复数据
      responseData[category].push(survey.response_count || 0);
    });
    
    // 计算每个类别的平均回复数
    const avgResponses = {};
    for (const category in responseData) {
      const responses = responseData[category];
      avgResponses[category] = responses.length > 0 
        ? Math.round(responses.reduce((sum, count) => sum + count, 0) / responses.length) 
        : 0;
    }
    
    // 配置图表选项
    const option = {
      title: {
        text: '区块链问卷调查分析',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        data: ['问卷数量', '平均回复数'],
        top: 30
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: Object.keys(categorizedSurveys)
      },
      yAxis: [
        {
          type: 'value',
          name: '问卷数量',
          position: 'left'
        },
        {
          type: 'value',
          name: '平均回复数',
          position: 'right'
        }
      ],
      series: [
        {
          name: '问卷数量',
          type: 'bar',
          data: Object.values(categorizedSurveys),
          itemStyle: {
            color: '#5470c6'
          }
        },
        {
          name: '平均回复数',
          type: 'line',
          yAxisIndex: 1,
          data: Object.keys(categorizedSurveys).map(category => avgResponses[category]),
          symbolSize: 8,
          itemStyle: {
            color: '#91cc75'
          },
          smooth: true,
          lineStyle: {
            width: 3
          }
        }
      ]
    };
    
    // 设置图表
    myChart.setOption(option);
    
    // 添加信息提示
    console.log('问卷调查分析图表已初始化，共分析了', data.surveys.length, '个问卷');
  }
  
  // 处理错误
  function handleError(error) {
    console.error('获取问卷数据失败:', error);
    chartContainer.innerHTML = `
      <div class="alert alert-danger">
        <strong>获取问卷数据失败</strong><br>
        ${error.message || '请检查网络连接或联系管理员'}
      </div>
    `;
    
    // 使用模拟数据
    setTimeout(function() {
      const mockData = {
        '应急管理': 12,
        '满意度调查': 18,
        '信息收集': 10,
        '意见反馈': 15,
        '其他': 5
      };
      
      const mockAvgResponses = {
        '应急管理': 25,
        '满意度调查': 35,
        '信息收集': 15,
        '意见反馈': 20,
        '其他': 10
      };
      
      const option = {
        title: {
          text: '区块链问卷调查分析 (模拟数据)',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['问卷数量', '平均回复数'],
          top: 30
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: Object.keys(mockData)
        },
        yAxis: [
          {
            type: 'value',
            name: '问卷数量',
            position: 'left'
          },
          {
            type: 'value',
            name: '平均回复数',
            position: 'right'
          }
        ],
        series: [
          {
            name: '问卷数量',
            type: 'bar',
            data: Object.values(mockData),
            itemStyle: {
              color: '#5470c6'
            }
          },
          {
            name: '平均回复数',
            type: 'line',
            yAxisIndex: 1,
            data: Object.keys(mockData).map(category => mockAvgResponses[category]),
            symbolSize: 8,
            itemStyle: {
              color: '#91cc75'
            },
            smooth: true,
            lineStyle: {
              width: 3
            }
          }
        ]
      };
      
      // 设置图表
      myChart.setOption(option);
      console.log('已使用模拟数据初始化图表');
    }, 500);
  }
  
  // 响应窗口大小变化
  window.addEventListener('resize', function() {
    if (myChart && !myChart.isDisposed()) {
      myChart.resize();
    }
  });
}

// 视图切换函数
function setupViewSwitching() {
  document.addEventListener('DOMContentLoaded', function() {
    const viewButtons = document.querySelectorAll('[data-view]');
    
    // 获取所有可能的视图
    const views = {
      recentBlocksView: document.getElementById('recentBlocksView'),
      visualView: document.getElementById('visualView'),
      surveyView: document.getElementById('surveyView'),
      statisticsView: document.getElementById('statisticsView')
    };
    
    // 初始化 - 只显示默认视图
    Object.values(views).forEach(view => {
      if (view) view.style.display = 'none';
    });
    
    // 默认显示最近区块视图
    if (views.recentBlocksView) {
      views.recentBlocksView.style.display = 'block';
    }
    
    // 设置按钮点击事件
    viewButtons.forEach(button => {
      button.addEventListener('click', function() {
        const targetViewId = this.getAttribute('data-view');
        const targetView = document.getElementById(targetViewId);
        
        if (!targetView) {
          console.error(`找不到视图元素: ${targetViewId}`);
          return;
        }
        
        // 隐藏所有视图
        Object.values(views).forEach(view => {
          if (view) view.style.display = 'none';
        });
        
        // 显示目标视图
        targetView.style.display = 'block';
        
        // 标记当前按钮为活跃状态
        viewButtons.forEach(btn => btn.classList.remove('active'));
        this.classList.add('active');
        
        // 根据视图类型初始化相应图表
        setTimeout(() => {
          if (targetViewId === 'visualView') {
            console.log('初始化可视化视图图表');
            
            // 如果已初始化过3D视图，则只需要调整大小
            if (window.chartsInitialized && window.chartsInitialized.blockchain3D) {
              const chart = echarts.getInstanceByDom(document.getElementById('blockchain-3d-view'));
              if (chart && !chart.isDisposed()) {
                chart.resize();
                console.log('3D视图图表已调整大小');
              }
            } else {
              // 初始化3D视图
              initBlockchain3DView();
            }
          }
          
          // 问卷视图初始化
          if (targetViewId === 'surveyView') {
            console.log('初始化问卷视图图表');
            
            // 初始化问卷分析图表
            if (!window.chartsInitialized || !window.chartsInitialized.surveyAnalysis) {
              console.log('初始化问卷数据分析图表');
              initSurveyAnalysisForBlockchain();
            } else {
              console.log('问卷数据分析图表已经初始化');
              const chart = echarts.getInstanceByDom(document.getElementById('blockchain-survey-analysis-chart'));
              if (chart && !chart.isDisposed()) {
                chart.resize();
              }
            }
            
            // 初始化问卷调查分析图表
            const surveyChart = echarts.getInstanceByDom(document.getElementById('survey-analysis-chart'));
            if (!surveyChart || surveyChart.isDisposed()) {
              console.log('初始化问卷调查分析图表');
              initSurveyAnalysisChart();
            } else {
              console.log('问卷调查分析图表已调整大小');
              surveyChart.resize();
            }
            
            // 初始化问卷分布图表
            const distributionChart = echarts.getInstanceByDom(document.getElementById('survey-distribution-chart'));
            if (!distributionChart || distributionChart.isDisposed()) {
              console.log('初始化问卷分布图表');
              initSurveyDistributionChart();
            } else {
              console.log('问卷分布图表已调整大小');
              distributionChart.resize();
            }
          }
        }, 300); // 等待DOM完全渲染
      });
    });
  });
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
  console.log('区块链信息页面初始化');
  
  // 设置视图切换功能
  setupViewSwitching();
  
  // 设置全局图表初始化标记
  if (!window.chartsInitialized) {
    window.chartsInitialized = {};
  }
  
  // 先初始化所有视图的图表
  console.log('预先初始化所有图表，确保数据显示');
  setTimeout(() => {
    // 初始化3D区块链图表
    if (!window.chartsInitialized.blockchain3D) {
      console.log('初始化3D区块链图表');
      initBlockchain3DView();
    }
    
    // 初始化问卷调查图表
    console.log('初始化问卷调查图表');
    initSurveyAnalysisChart();
    
    // 初始化问卷分布图表
    console.log('初始化问卷分布图表');
    initSurveyDistributionChart();
    
    // 初始化问卷分析图表
    console.log('初始化问卷数据分析图表');
    initSurveyAnalysisForBlockchain();
    
    // 获取默认视图并确保显示
    const activeButton = document.querySelector('[data-view].active');
    if (activeButton) {
      const defaultViewId = activeButton.getAttribute('data-view');
      console.log('显示默认视图：', defaultViewId);
      
      // 显示默认视图
      const views = {
        recentBlocksView: document.getElementById('recentBlocksView'),
        visualView: document.getElementById('visualView'),
        surveyView: document.getElementById('surveyView'),
        statisticsView: document.getElementById('statisticsView')
      };
      
      // 先隐藏所有视图
      Object.values(views).forEach(view => {
        if (view) view.style.display = 'none';
      });
      
      // 显示默认视图
      const targetView = document.getElementById(defaultViewId);
      if (targetView) {
        targetView.style.display = 'block';
      } else {
        // 如果没有默认视图，显示最近区块视图
        if (views.recentBlocksView) {
          views.recentBlocksView.style.display = 'block';
        }
      }
    } else {
      console.log('未找到默认视图，显示最近区块视图');
    }
  }, 500);
});

// 添加辅助函数，用于处理API错误并显示模拟数据
function handleApiErrorWithMockData(containerId, errorMessage, mockDataGenerator) {
  console.error(errorMessage);
  const container = document.getElementById(containerId);
  if (!container) return;
  
  container.innerHTML = `
    <div class="alert alert-danger mb-2">
      <strong>获取数据失败</strong><br>
      ${errorMessage}
    </div>
  `;
  
  // 在错误提示后使用模拟数据
  setTimeout(function() {
    try {
      // 确保容器未被其他脚本修改
      if (container.querySelector('.alert')) {
        // 移除错误提示不会影响图表的初始化
        // 但保留它可以提示用户查看的是模拟数据
        mockDataGenerator(container);
      }
    } catch (e) {
      console.error('使用模拟数据失败:', e);
    }
  }, 300);
}

// 问卷在区块链上分布情况图表
function initSurveyDistributionChart() {
  const chartDom = document.getElementById('survey-distribution-chart');
  if (!chartDom) {
    console.warn("找不到survey-distribution-chart元素，跳过初始化问卷分布图");
    return;
  }
  
  // 显示加载状态
  chartDom.innerHTML = `
    <div class="text-center p-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
      </div>
      <p class="mt-2">正在加载区块链问卷分布数据...</p>
    </div>
  `;
  
  const myChart = echarts.init(chartDom);
  
  // 获取数据并处理
  fetchData()
    .then(processData)
    .catch(handleError);
  
  // 获取数据
  function fetchData() {
    return Promise.all([
      fetch('/api/blockchain/info').then(response => {
        if (!response.ok) {
          throw new Error(`区块链数据HTTP错误: ${response.status}`);
        }
        return response.json();
      }),
      fetch('/api/surveys').then(response => {
        if (!response.ok) {
          throw new Error(`问卷数据HTTP错误: ${response.status}`);
        }
        return response.json();
      })
    ]);
  }
  
  // 处理数据
  function processData([blocksData, surveysData]) {
    if (!blocksData.success || !blocksData.blocks || !surveysData.success || !surveysData.surveys) {
      chartDom.innerHTML = '<div class="alert alert-info">暂无区块链或问卷数据</div>';
      return;
    }
    
    console.log('获取到区块数据:', blocksData.blocks.length, '个区块');
    console.log('获取到问卷数据:', surveysData.surveys.length, '个问卷');
    
    // 问卷分类
    const categoryMapping = {
      '突发': '应急管理',
      '灾害': '应急管理',
      '应急': '应急管理',
      '满意度': '满意度调查',
      '服务': '满意度调查',
      '评价': '满意度调查',
      '信息收集': '信息收集',
      '调查': '信息收集',
      '意见': '意见反馈',
      '建议': '意见反馈',
      '反馈': '意见反馈'
    };
    
    // 对问卷进行分类
    const categorizedSurveys = {};
    surveysData.surveys.forEach(survey => {
      let category = '其他问卷';
      
      // 根据标题确定分类
      if (survey.title) {
        for (const [keyword, categoryName] of Object.entries(categoryMapping)) {
          if (survey.title.includes(keyword)) {
            category = categoryName;
            break;
          }
        }
      }
      
      // 记录问卷及其分类和所在区块
      if (survey.blockchain_hash) {
        if (!categorizedSurveys[survey.id]) {
          categorizedSurveys[survey.id] = {
            id: survey.id,
            title: survey.title || '未命名问卷',
            category: category,
            blockchain_hash: survey.blockchain_hash
          };
        }
      }
    });
    
    // 按照区块分组问卷
    const data = [];
    // 限制显示最近15个区块
    const recentBlocks = blocksData.blocks.slice(0, 15);
    const blockIds = recentBlocks.map(block => block.index || block.id);
    
    // 对每个区块查找包含的问卷
    recentBlocks.forEach(block => {
      const blockSurveys = {};
      
      // 初始化类别计数
      const categories = ['应急管理', '满意度调查', '信息收集', '意见反馈', '其他问卷'];
      categories.forEach(category => {
        blockSurveys[category] = 0;
      });
      
      // 统计区块中的问卷分类数量
      for (const survey of Object.values(categorizedSurveys)) {
        // 检查问卷是否在当前区块中
        if (block.transactions && block.transactions.some(tx => 
            tx.hash === survey.blockchain_hash || 
            (tx.data && tx.data.includes(survey.blockchain_hash))
          )) {
          blockSurveys[survey.category]++;
        }
      }
      
      // 添加到数据集
      for (const [category, count] of Object.entries(blockSurveys)) {
        if (count > 0) {
          data.push({
            name: category,
            value: [block.index || block.id || 0, count, category]
          });
        }
      }
    });
    
    // 如果没有数据，显示提示信息
    if (data.length === 0) {
      data.push({
        name: '暂无数据',
        value: [1, 0, '暂无数据']
      });
    }
    
    const option = {
      title: {
        text: '问卷在区块链上的分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: function(params) {
          return `区块 #${params.value[0]}<br/>${params.value[2]}: ${params.value[1]}个`;
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        name: '区块高度',
        nameLocation: 'middle',
        nameGap: 30,
        min: Math.min(...blockIds, 1) - 1,
        max: Math.max(...blockIds, 2) + 1
      },
      yAxis: {
        type: 'value',
        name: '问卷数量'
      },
      series: [
        {
          name: '应急管理',
          type: 'scatter',
          symbolSize: function(val) {
            return val[1] * 10 + 10; // 基于问卷数量的大小
          },
          emphasis: {
            focus: 'series'
          },
          data: data.filter(item => item.name === '应急管理'),
          itemStyle: {
            color: '#5470c6'
          }
        },
        {
          name: '满意度调查',
          type: 'scatter',
          symbolSize: function(val) {
            return val[1] * 10 + 10;
          },
          emphasis: {
            focus: 'series'
          },
          data: data.filter(item => item.name === '满意度调查'),
          itemStyle: {
            color: '#91cc75'
          }
        },
        {
          name: '信息收集',
          type: 'scatter',
          symbolSize: function(val) {
            return val[1] * 10 + 10;
          },
          emphasis: {
            focus: 'series'
          },
          data: data.filter(item => item.name === '信息收集'),
          itemStyle: {
            color: '#fac858'
          }
        },
        {
          name: '意见反馈',
          type: 'scatter',
          symbolSize: function(val) {
            return val[1] * 10 + 10;
          },
          emphasis: {
            focus: 'series'
          },
          data: data.filter(item => item.name === '意见反馈'),
          itemStyle: {
            color: '#ee6666'
          }
        },
        {
          name: '其他问卷',
          type: 'scatter',
          symbolSize: function(val) {
            return val[1] * 10 + 10;
          },
          emphasis: {
            focus: 'series'
          },
          data: data.filter(item => item.name === '其他问卷'),
          itemStyle: {
            color: '#73c0de'
          }
        }
      ],
      legend: {
        data: ['应急管理', '满意度调查', '信息收集', '意见反馈', '其他问卷'],
        top: 'bottom'
      }
    };
    
    myChart.setOption(option);
    console.log('问卷分布图表初始化完成');
  }
  
  // 处理错误
  function handleError(error) {
    console.error('获取区块链问卷分布数据失败:', error);
    chartDom.innerHTML = `
      <div class="alert alert-danger mb-2">
        <strong>获取区块链问卷分布数据失败</strong><br>
        ${error.message || '请检查网络连接或联系管理员'}
      </div>
    `;
    
    // 使用模拟数据作为备用
    setTimeout(function() {
      try {
        const myChart = echarts.init(chartDom);
        
        // 生成模拟数据
        const data = [];
        const blockCount = 15;
        const categories = ['应急管理', '满意度调查', '信息收集', '意见反馈', '其他问卷'];
        
        // 生成每个区块中的问卷数量
        for (let i = 0; i < blockCount; i++) {
          const blockIndex = blockCount - i;
          
          // 为每个类别随机生成0-3个问卷
          categories.forEach(category => {
            const count = Math.floor(Math.random() * 4);
            if (count > 0) {
              data.push({
                name: category,
                value: [blockIndex, count, category]
              });
            }
          });
        }
        
        const option = {
          title: {
            text: '问卷在区块链上的分布 (模拟数据)',
            left: 'center'
          },
          tooltip: {
            trigger: 'item',
            formatter: function(params) {
              return `区块 #${params.value[0]}<br/>${params.value[2]}: ${params.value[1]}个`;
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'value',
            name: '区块高度',
            nameLocation: 'middle',
            nameGap: 30,
            min: 0,
            max: blockCount + 1
          },
          yAxis: {
            type: 'value',
            name: '问卷数量'
          },
          series: categories.map(category => ({
            name: category,
            type: 'scatter',
            symbolSize: function(val) {
              return val[1] * 10 + 10;
            },
            emphasis: {
              focus: 'series'
            },
            data: data.filter(item => item.name === category),
            itemStyle: {
              color: {
                '应急管理': '#5470c6',
                '满意度调查': '#91cc75',
                '信息收集': '#fac858',
                '意见反馈': '#ee6666',
                '其他问卷': '#73c0de'
              }[category]
            }
          })),
          legend: {
            data: categories,
            top: 'bottom'
          }
        };
        
        myChart.setOption(option);
        console.log('已使用模拟数据初始化问卷分布图表');
        
        // 响应窗口大小变化
        window.addEventListener('resize', function() {
          if (myChart && !myChart.isDisposed()) {
            myChart.resize();
          }
        });
      } catch (e) {
        console.error('使用模拟数据初始化问卷分布图表失败:', e);
      }
    }, 300);
  }
  
  // 响应窗口大小变化
  window.addEventListener('resize', function() {
    if (myChart && !myChart.isDisposed()) {
      myChart.resize();
    }
  });
}

// 3D区块链可视化
function initBlockchain3DView() {
  console.log("开始初始化区块链3D可视化视图");
  
  // 获取图表容器
  const chartDom = document.getElementById('blockchain-3d-view');
  if (!chartDom) {
    console.error("找不到blockchain-3d-view元素，3D视图无法初始化");
    return;
  }
  
  // 显示加载状态
  chartDom.innerHTML = `
    <div class="text-center p-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
      </div>
      <p class="mt-2">正在加载3D区块链视图...</p>
    </div>
  `;
  
  // 标记状态初始化
  if (!window.chartsInitialized) {
    window.chartsInitialized = {};
  }

  try {
    // 获取图表实例
    let myChart = echarts.getInstanceByDom(chartDom);
    if (myChart) {
      myChart.dispose();
    }
    
    myChart = echarts.init(chartDom);
    
    // 获取当前容器高度
    const containerHeight = chartDom.clientHeight;
    // 确保最小高度500px以保证良好的显示效果
    if (containerHeight < 500) {
      chartDom.style.height = '500px';
      myChart.resize();
    }
    
    // 获取展示模式
    const displayModeSelect = document.getElementById('displayModeSelect');
    let currentDisplayMode = 'chain';
    if (displayModeSelect) {
      currentDisplayMode = displayModeSelect.value || 'chain';
    }
    console.log("当前显示模式:", currentDisplayMode);
    
    // 模拟区块数据
    const blockCount = 15;
    const nodes = [];
    const links = [];
    
    // 创建节点和连接
    for (let i = 0; i < blockCount; i++) {
      // 添加区块节点
      nodes.push({
        id: `${i}`,
        name: `区块 #${i}`,
        value: Math.floor(Math.random() * 10) + 5, // 区块大小/重要性
        category: 0,
        symbolSize: 30 + Math.random() * 20, // 节点大小
        x: Math.random() * 100,
        y: Math.random() * 100,
        z: Math.random() * 100,
        // 模拟的区块数据
        itemStyle: {
          color: '#4e79a7'
        },
        tooltip: {
          formatter: function(params) {
            return `
              <div style="font-weight:bold;margin-bottom:5px;">区块 #${i}</div>
              <div>哈希: ${Math.random().toString(36).substring(2, 10)}</div>
              <div>时间戳: ${new Date().toLocaleString()}</div>
              <div>交易数: ${Math.floor(Math.random() * 50)}</div>
            `;
          }
        }
      });
      
      // 添加交易节点 (每个区块附带1-3个交易)
      const txCount = Math.floor(Math.random() * 3) + 1;
      for (let j = 0; j < txCount; j++) {
        const txId = `tx-${i}-${j}`;
        nodes.push({
          id: txId,
          name: `交易 ${j+1}`,
          value: Math.floor(Math.random() * 5) + 1,
          category: 1,
          symbolSize: 15,
          x: Math.random() * 100,
          y: Math.random() * 100,
          z: Math.random() * 100,
          itemStyle: {
            color: '#f28e2c'
          }
        });
        
        // 添加区块和交易之间的连接
        links.push({
          source: `${i}`,
          target: txId,
          lineStyle: {
            color: '#bbb',
            width: 1
          }
        });
      }
      
      // 添加区块链连接
      if (i > 0) {
        links.push({
          source: `${i-1}`,
          target: `${i}`,
          lineStyle: {
            color: '#767fe5',
            width: 3
          }
        });
      }
    }
    
    // 检查是否支持WebGL
    const useGraph3D = window.WebGLRenderingContext && document.createElement('canvas').getContext('webgl');
    console.log("WebGL支持状态:", useGraph3D ? "支持" : "不支持");
    
    // 根据浏览器能力选择不同的图表配置
    if (useGraph3D) {
      // 3D图表配置
      const option = {
        title: {
          text: '区块链3D可视化',
          subtext: `显示模式: ${currentDisplayMode}`,
          left: 'center'
        },
        tooltip: {
          formatter: function(params) {
            return params.name + '<br/>' + 
                  (params.value ? `值: ${params.value}` : '');
          }
        },
        legend: {
          data: ['区块', '交易'],
          bottom: 10
        },
        visualMap: {
          show: true,
          dimension: 2,
          min: 0,
          max: 30,
          inRange: {
            color: ['#1710c0', '#0b9df0', '#00fea8', '#00ff0d', '#f5f811', '#f09a09', '#fe0300']
          }
        },
        xAxis3D: {
          type: 'value',
          min: -20,
          max: 120
        },
        yAxis3D: {
          type: 'value',
          min: -20,
          max: 120
        },
        zAxis3D: {
          type: 'value',
          min: -20,
          max: 120
        },
        grid3D: {
          boxWidth: 220,
          boxDepth: 220,
          boxHeight: 180,
          viewControl: {
            // 初始视角
            distance: 300,
            alpha: 20,
            beta: 40,
            rotateSensitivity: 5,
            zoomSensitivity: 5,
            // 是否自动旋转
            autoRotate: true,
            autoRotateSpeed: 5
          },
          light: {
            main: {
              shadow: true,
              intensity: 1.5
            },
            ambient: {
              intensity: 0.3
            }
          }
        },
        series: [
          {
            name: '区块',
            type: 'scatter3D',
            symbolSize: 18,
            itemStyle: {
              opacity: 0.9,
              borderWidth: 1,
              borderColor: '#fff'
            },
            emphasis: {
              itemStyle: {
                color: '#f00',
                borderColor: '#fff',
                borderWidth: 2,
                opacity: 1
              }
            },
            data: nodes.filter(node => node.category === 0).map(node => ({
              name: node.name,
              value: [node.x, node.y, node.z],
              itemStyle: node.itemStyle
            }))
          },
          {
            name: '交易',
            type: 'scatter3D',
            symbolSize: 10,
            itemStyle: {
              opacity: 0.7,
              borderWidth: 1,
              borderColor: '#fff'
            },
            data: nodes.filter(node => node.category === 1).map(node => ({
              name: node.name,
              value: [node.x, node.y, node.z],
              itemStyle: node.itemStyle
            }))
          },
          {
            name: '连接',
            type: 'lines3D',
            effect: {
              show: true,
              trailWidth: 1,
              trailLength: 0.15,
              trailOpacity: 0.05,
              trailColor: '#0f0'
            },
            lineStyle: {
              width: 1,
              opacity: 0.3,
              curveness: 0.3
            },
            data: links.map(link => {
              // 找到源节点和目标节点
              const sourceNode = nodes.find(node => node.id === link.source);
              const targetNode = nodes.find(node => node.id === link.target);
              
              if (!sourceNode || !targetNode) return null;
              
              return {
                coords: [
                  [sourceNode.x, sourceNode.y, sourceNode.z],
                  [targetNode.x, targetNode.y, targetNode.z]
                ],
                lineStyle: link.lineStyle
              };
            }).filter(Boolean)
          }
        ]
      };
      
      myChart.setOption(option);
    } else {
      // 退化为2D图表 (为不支持WebGL的浏览器)
      console.log("浏览器不支持WebGL，使用2D图表备用方案");
      
      // 重新组织节点位置为2D
      const rearrangedNodes = nodes.map(node => {
        return {
          ...node,
          x: Math.random() * 200 - 100,
          y: Math.random() * 200 - 100
        };
      });
      
      const backupOption = {
        title: {
          text: '区块链网络图 (2D备用视图)',
          left: 'center'
        },
        tooltip: {},
        legend: [
          {
            data: ['区块', '交易'],
            left: 'left'
          }
        ],
        series: [
          {
            name: '区块链',
            type: 'graph',
            layout: 'none',
            data: rearrangedNodes,
            links: links,
            categories: [
              { name: '区块' },
              { name: '交易' }
            ],
            roam: true,
            label: {
              show: true,
              position: 'right',
              formatter: '{b}'
            },
            lineStyle: {
              color: 'source',
              curveness: 0.3
            },
            emphasis: {
              focus: 'adjacency'
            }
          }
        ]
      };
      
      myChart.setOption(backupOption);
    }
    
    // 添加窗口大小变化监听器
    window.addEventListener('resize', function() {
      if (myChart && !myChart.isDisposed()) {
        myChart.resize();
      }
    });
    
    // 标记初始化完成
    window.chartsInitialized.blockchain3D = true;
    console.log("区块链3D图表初始化完成");
    
    return myChart;
  } catch (error) {
    console.error("区块链3D图表初始化错误:", error);
    
    // 显示错误提示
    chartDom.innerHTML = `
      <div class="d-flex justify-content-center align-items-center" style="height:400px;">
        <div class="text-center">
          <i class="fas fa-exclamation-triangle text-warning mb-3" style="font-size: 3rem;"></i>
          <h4>3D可视化加载失败</h4>
          <p class="text-muted">${error.message}</p>
          <p class="small text-muted">请尝试使用支持WebGL的现代浏览器</p>
        </div>
      </div>
    `;
  }
}

// 初始化区块链问卷分析图表
function initSurveyAnalysisForBlockchain() {
  console.log('初始化区块链问卷分析图表');
  const chartContainer = document.getElementById('blockchain-survey-analysis-chart');
  
  if (!chartContainer) {
    console.warn('未找到图表容器 blockchain-survey-analysis-chart');
    return;
  }
  
  // 检查是否已经初始化
  if (window.chartsInitialized && window.chartsInitialized.surveyAnalysis) {
    console.log('问卷分析图表已初始化，更新数据');
    // 如果已经初始化，仅更新数据
    window.surveyAnalysisChart.resize();
    return;
  }
  
  // 显示加载中状态
  chartContainer.innerHTML = '<div class="chart-loading"><span class="spinner-border spinner-border-sm"></span> 加载图表中...</div>';
  
  try {
    // 初始化图表
    console.log('创建新的问卷分析图表');
    const chart = echarts.init(chartContainer);
    window.surveyAnalysisChart = chart;
    // 记录初始化状态
    if (window.chartsInitialized) {
      window.chartsInitialized.surveyAnalysis = true;
    }
    
    // 默认显示的图表类型
    let currentChartType = 'questionType';
    
    // 模拟问卷类型分布数据（使用更逼真的数据）
    const questionTypeData = [
      { value: 35, name: '用户体验' },
      { value: 25, name: '功能需求' },
      { value: 18, name: '性能问题' },
      { value: 12, name: '安全问题' },
      { value: 10, name: '其他' }
    ];
    
    // 模拟时间分布热力图数据
    const timeDistributionData = [];
    // 生成模拟数据：一周7天 x 24小时
    const days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    const hours = [];
    for (let i = 0; i < 24; i++) {
      hours.push(i + ':00');
    }
    
    // 创建更真实的热力图数据分布模式
    days.forEach((day, dayIndex) => {
      hours.forEach((hour, hourIndex) => {
        // 工作日的工作时间（9-18点）活跃度较高
        let value = 0;
        if (dayIndex < 5) { // 工作日
          if (hourIndex >= 9 && hourIndex < 18) { // 工作时间
            value = Math.floor(Math.random() * 50) + 50; // 50-100
          } else if ((hourIndex >= 7 && hourIndex < 9) || (hourIndex >= 18 && hourIndex < 22)) {
            // 早晨和晚上活跃度中等
            value = Math.floor(Math.random() * 30) + 20; // 20-50
          } else {
            // 深夜活跃度低
            value = Math.floor(Math.random() * 15) + 5; // 5-20
          }
        } else { // 周末
          if (hourIndex >= 10 && hourIndex < 20) { // 白天时间
            value = Math.floor(Math.random() * 40) + 30; // 30-70
          } else {
            value = Math.floor(Math.random() * 20) + 10; // 10-30
          }
        }
        
        timeDistributionData.push([dayIndex, hourIndex, value]);
      });
    });
    
    // 模拟季度区块关联数据
    const quarterlyData = {
      quarters: ['Q1 2023', 'Q2 2023', 'Q3 2023', 'Q4 2023', 'Q1 2024'],
      surveys: [125, 168, 214, 287, 346],
      blocks: [15243, 16851, 18492, 21358, 24125],
      transactions: [58962, 67543, 82467, 102483, 137261],
      correlationRate: [0.45, 0.52, 0.61, 0.72, 0.68]
    };
    
    // 按钮事件处理
    const questionTypeBtn = document.getElementById('questionTypeBtn');
    const timeDistributionBtn = document.getElementById('timeDistributionBtn');
    const correlationBtn = document.getElementById('correlationBtn');
    
    // 更新按钮状态
    function updateButtonState(type) {
      currentChartType = type;
      [questionTypeBtn, timeDistributionBtn, correlationBtn].forEach(btn => {
        if (btn) btn.classList.remove('active');
      });
      
      switch (type) {
        case 'questionType':
          if (questionTypeBtn) questionTypeBtn.classList.add('active');
          break;
        case 'timeDistribution':
          if (timeDistributionBtn) timeDistributionBtn.classList.add('active');
          break;
        case 'correlation':
          if (correlationBtn) correlationBtn.classList.add('active');
          break;
      }
    }
    
    // 更新图表函数
    function updateChart(type) {
      updateButtonState(type);
      
      let option;
      
      switch (type) {
        case 'questionType':
          // 问卷类型饼图配置
          option = {
            title: {
              text: '问卷类型分布',
              left: 'center'
            },
            tooltip: {
              trigger: 'item',
              formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
              orient: 'vertical',
              left: 'left',
              data: questionTypeData.map(item => item.name)
            },
            series: [
              {
                name: '问卷类型',
                type: 'pie',
                radius: ['40%', '70%'],
                avoidLabelOverlap: false,
                itemStyle: {
                  borderRadius: 10,
                  borderColor: '#fff',
                  borderWidth: 2
                },
                label: {
                  show: true,
                  formatter: '{b}: {c} ({d}%)'
                },
                emphasis: {
                  label: {
                    show: true,
                    fontSize: '16',
                    fontWeight: 'bold'
                  }
                },
                data: questionTypeData
              }
            ],
            color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de']
          };
          break;
          
        case 'timeDistribution':
          // 时间分布热力图配置
          option = {
            title: {
              text: '问卷提交时间分布',
              left: 'center'
            },
            tooltip: {
              position: 'top',
              formatter: function (params) {
                return `${days[params.data[0]]} ${hours[params.data[1]]}<br>数量: ${params.data[2]}`;
              }
            },
            grid: {
              top: '70',
              right: '25%'
            },
            xAxis: {
              type: 'category',
              data: hours,
              splitArea: {
                show: true
              },
              axisLabel: {
                interval: 3 // 每隔3小时显示一个标签
              }
            },
            yAxis: {
              type: 'category',
              data: days,
              splitArea: {
                show: true
              }
            },
            visualMap: {
              min: 0,
              max: 100,
              calculable: true,
              orient: 'horizontal',
              left: 'center',
              bottom: '5%',
              inRange: {
                color: ['#ebedf0', '#c6e48b', '#7bc96f', '#239a3b', '#196127']
              }
            },
            series: [
              {
                name: '问卷提交量',
                type: 'heatmap',
                data: timeDistributionData,
                label: {
                  show: false
                },
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                  }
                }
              }
            ]
          };
          break;
          
        case 'correlation':
          // 季度区块关联图表配置
          option = {
            title: {
              text: '问卷数据与区块链相关性分析',
              left: 'center'
            },
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'shadow'
              },
              formatter: function (params) {
                let tooltip = params[0].name + '<br/>';
                params.forEach(param => {
                  let marker = param.marker;
                  let seriesName = param.seriesName;
                  let value = param.value;
                  
                  // 为相关性指数添加百分比格式
                  if (seriesName === '相关性指数') {
                    value = (value * 100).toFixed(1) + '%';
                  } else if (seriesName === '区块数' || seriesName === '交易数' || seriesName === '问卷数') {
                    value = value.toLocaleString(); // 添加千位分隔符
                  }
                  
                  tooltip += marker + ' ' + seriesName + ': ' + value + '<br/>';
                });
                return tooltip;
              }
            },
            legend: {
              data: ['问卷数', '区块数', '交易数', '相关性指数'],
              top: 30
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '3%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              data: quarterlyData.quarters
            },
            yAxis: [
              {
                type: 'value',
                name: '数量',
                min: 0,
                position: 'left',
                axisLabel: {
                  formatter: '{value}'
                }
              },
              {
                type: 'value',
                name: '相关性',
                min: 0,
                max: 1,
                position: 'right',
                axisLabel: {
                  formatter: '{value * 100}%'
                }
              }
            ],
            series: [
              {
                name: '问卷数',
                type: 'bar',
                stack: 'total',
                barWidth: '40%',
                emphasis: {
                  focus: 'series'
                },
                data: quarterlyData.surveys
              },
              {
                name: '区块数',
                type: 'line',
                yAxisIndex: 0,
                symbol: 'circle',
                symbolSize: 8,
                lineStyle: {
                  width: 3,
                  shadowColor: 'rgba(0,0,0,0.3)',
                  shadowBlur: 10
                },
                emphasis: {
                  focus: 'series'
                },
                data: quarterlyData.blocks
              },
              {
                name: '交易数',
                type: 'line',
                yAxisIndex: 0,
                symbol: 'triangle',
                symbolSize: 8,
                lineStyle: {
                  width: 3,
                  shadowColor: 'rgba(0,0,0,0.3)',
                  shadowBlur: 10
                },
                emphasis: {
                  focus: 'series'
                },
                data: quarterlyData.transactions
              },
              {
                name: '相关性指数',
                type: 'line',
                yAxisIndex: 1,
                symbol: 'rect',
                symbolSize: 8,
                smooth: true,
                lineStyle: {
                  width: 4,
                  type: 'dashed'
                },
                label: {
                  show: true,
                  formatter: function(params) {
                    return (params.value * 100).toFixed(0) + '%';
                  }
                },
                emphasis: {
                  focus: 'series'
                },
                data: quarterlyData.correlationRate
              }
            ],
            color: ['#5470c6', '#91cc75', '#fac858', '#ee6666']
          };
          break;
      }
      
      // 应用图表配置
      try {
        chart.setOption(option, true);
        console.log('图表配置已应用: ', currentChartType);
      } catch (err) {
        console.error('应用图表配置失败', err);
        chartContainer.innerHTML = '<div class="alert alert-danger">应用图表配置失败: ' + err.message + '</div>';
      }
    }
    
    // 按钮点击事件
    if (questionTypeBtn) {
      questionTypeBtn.addEventListener('click', function() {
        updateChart('questionType');
      });
    }
    
    if (timeDistributionBtn) {
      timeDistributionBtn.addEventListener('click', function() {
        updateChart('timeDistribution');
      });
    }
    
    if (correlationBtn) {
      correlationBtn.addEventListener('click', function() {
        updateChart('correlation');
      });
    }
    
    // 初始化默认图表
    updateChart('questionType');
    
    // 窗口大小改变时调整图表
    window.addEventListener('resize', function() {
      if (chart) {
        chart.resize();
      }
    });
    
    // 显示问卷视图时调整图表大小
    const surveyTabLink = document.querySelector('a[data-view="surveyView"]');
    if (surveyTabLink) {
      surveyTabLink.addEventListener('shown.bs.tab', function() {
        setTimeout(() => {
          if (chart) {
            chart.resize();
          }
        }, 100);
      });
    }
    
    console.log('问卷分析图表初始化完成');
  } catch (err) {
    console.error('问卷分析图表初始化失败', err);
    chartContainer.innerHTML = '<div class="alert alert-danger">图表初始化失败: ' + err.message + '</div>';
  }
}

function updateChart(nodes, links) {
  console.log('更新区块链图表，使用高级2D可视化');
  
  if (!blockchainChart) {
    console.warn('图表未初始化，无法更新');
    return;
  }
  
  try {
    // 提取节点和链接数据
    const nodeData = nodes.map((node, index) => {
      return {
        id: node.id || index,
        name: node.type === 'block' ? `区块 #${node.id || index}` : `问卷 #${node.id || index}`,
        value: [node.x, node.y], // 2D坐标
        itemStyle: {
          color: getNodeColor(node.type, node.status),
          borderWidth: node.status === 'current' ? 4 : 2,
          borderColor: node.status === 'current' ? '#ff5722' : 'rgba(255,255,255,0.4)',
          shadowBlur: node.status === 'current' ? 20 : 5,
          shadowColor: node.status === 'current' ? 'rgba(255, 87, 34, 0.8)' : 'rgba(0,0,0,0.2)'
        },
        symbol: getNodeSymbol(node.type),
        symbolSize: getNodeSize(node.type, node.status),
        data: node.data || {},
        type: node.type
      };
    });
    
    const edgeData = links.map(link => {
      return {
        source: link.source,
        target: link.target,
        lineStyle: {
          width: 2,
          color: getEdgeColor(link.type),
          type: getLinkStyle(link.type),
          curveness: 0.2
        }
      };
    });
    
    // 准备图表配置
    const option = {
      title: {
        text: '区块链问卷网络可视化',
        subtext: '问卷数据与区块链关系',
        left: 'center',
        top: 10,
        textStyle: {
          fontSize: 18
        },
        subtextStyle: {
          fontSize: 12
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: function(params) {
          if (params.dataType === 'edge') {
            return `<div class="tooltip-item">
                      <b>连接关系</b><br/>
                      从: ${params.data.source}<br/>
                      到: ${params.data.target}<br/>
                    </div>`;
          }
          
          // 节点的详细信息
          const nodeData = params.data.data || {};
          let timeStr = nodeData.timestamp ? new Date(nodeData.timestamp).toLocaleString() : '未知';
          
          if (params.data.type === 'questionnaire') {
            // 问卷节点提示
            return `<div class="tooltip-item">
                      <div class="tooltip-title">${params.data.name}</div>
                      <div class="tooltip-content">
                        <p><b>类型:</b> ${nodeData.type || '未知'}</p>
                        <p><b>创建时间:</b> ${timeStr}</p>
                        <p><b>问题数:</b> ${nodeData.questions || 0}</p>
                        <p><b>填写人数:</b> ${nodeData.respondents || 0}</p>
                      </div>
                    </div>`;
          } else {
            // 区块节点提示
            let hashStr = nodeData.hash ? `${nodeData.hash.substring(0, 8)}...` : '未知';
            
            return `<div class="tooltip-item">
                      <div class="tooltip-title">${params.data.name}</div>
                      <div class="tooltip-content">
                        <p><b>创建时间:</b> ${timeStr}</p>
                        <p><b>哈希值:</b> ${hashStr}</p>
                        <p><b>包含问卷:</b> ${nodeData.questionnaires || 0} 份</p>
                        <p><b>大小:</b> ${nodeData.size || 0} KB</p>
                      </div>
                    </div>`;
          }
        },
        backgroundColor: 'rgba(50,50,50,0.8)',
        borderColor: '#eee',
        borderWidth: 1,
        textStyle: {
          color: '#fff'
        },
        extraCssText: 'box-shadow: 0 3px 14px rgba(0,0,0,0.3); border-radius: 8px; padding: 10px;'
      },
      legend: {
        data: ['已确认区块', '待确认区块', '孤立区块', '问卷数据'],
        orient: 'horizontal',
        bottom: 10,
        textStyle: {
          fontSize: 12
        },
        itemWidth: 25,
        itemHeight: 14,
        selectedMode: 'multiple'
      },
      grid: {
        left: '5%',
        right: '5%',
        top: '15%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        name: '链位置',
        nameLocation: 'middle',
        nameGap: 25,
        nameTextStyle: {
          fontSize: 12,
          fontWeight: 'bold'
        },
        axisLine: {
          lineStyle: {
            color: '#666'
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            color: 'rgba(100,100,100,0.2)'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#888',
          showMinLabel: false,
          showMaxLabel: false
        }
      },
      yAxis: {
        type: 'value',
        name: '分支层级',
        nameLocation: 'middle',
        nameGap: 30,
        nameTextStyle: {
          fontSize: 12,
          fontWeight: 'bold'
        },
        axisLine: {
          lineStyle: {
            color: '#666'
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            color: 'rgba(100,100,100,0.2)'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#888',
          showMinLabel: false,
          showMaxLabel: false
        }
      },
      series: [
        {
          type: 'graph',
          layout: 'none', // 使用节点提供的坐标
          coordinateSystem: 'cartesian2d',
          symbolSize: 20,
          roam: true,
          label: {
            show: false
          },
          edgeSymbol: ['none', 'arrow'],
          edgeSymbolSize: [0, 8],
          edgeLabel: {
            fontSize: 12
          },
          data: nodeData,
          links: edgeData,
          lineStyle: {
            opacity: 0.8,
            width: 1.5,
            curveness: 0.2
          },
          emphasis: {
            focus: 'adjacency',
            lineStyle: {
              width: 5
            },
            itemStyle: {
              shadowBlur: 20,
              shadowColor: 'rgba(0, 166, 255, 0.6)'
            }
          },
          blur: {
            itemStyle: {
              opacity: 0.3
            },
            lineStyle: {
              opacity: 0.1
            }
          },
          animation: true,
          animationDuration: 1000,
          animationEasing: 'elasticOut'
        }
      ],
      toolbox: {
        feature: {
          saveAsImage: {
            title: '保存为图片',
            pixelRatio: 2
          },
          restore: {
            title: '还原'
          },
          dataZoom: {
            title: {
              zoom: '区域缩放',
              back: '缩放还原'
            }
          },
          dataView: {
            title: '数据视图',
            readOnly: true,
            lang: ['数据视图', '关闭', '刷新']
          }
        },
        right: 20,
        top: 20
      },
      dataZoom: [
        {
          type: 'inside',
          xAxisIndex: 0
        },
        {
          type: 'inside',
          yAxisIndex: 0
        }
      ],
      graphic: [
        {
          type: 'text',
          z: 100,
          right: 20,
          top: 60,
          style: {
            fill: '#999',
            text: '滚轮缩放 | 拖拽平移',
            font: '12px Arial'
          }
        }
      ]
    };
    
    // 使用主题
    const isDarkMode = document.body.classList.contains('dark-mode');
    const theme = isDarkMode ? 'dark' : null;
    
    // 应用配置
    blockchainChart.setOption(option, true);
    console.log('区块链图表更新完成');
    
    // 添加点击事件处理
    blockchainChart.off('click');
    blockchainChart.on('click', 'series.graph.data', function(params) {
      showBlockDetails(params.data);
    });
    
  } catch (err) {
    console.error('区块链图表更新失败:', err);
    const container = document.getElementById('blockchain-chart');
    if (container) {
      container.innerHTML = '<div class="alert alert-danger">区块链图表更新失败: ' + err.message + '</div>';
    }
  }
}

// 获取节点颜色
function getNodeColor(nodeType, status) {
  if (status === 'current') {
    return '#ff5722'; // 当前区块使用醒目的颜色
  }
  
  switch (nodeType) {
    case 'block':
      return status === 'confirmed' ? '#4caf50' : 
             status === 'pending' ? '#ffc107' : 
             status === 'orphaned' ? '#9e9e9e' : '#2196f3';
    case 'questionnaire':
      return '#9c27b0';
    case 'validator':
      return '#3f51b5';
    default:
      return '#2196f3';
  }
}

// 获取节点形状
function getNodeSymbol(nodeType) {
  switch (nodeType) {
    case 'block':
      return 'rect';
    case 'questionnaire':
      return 'pin';
    case 'validator':
      return 'triangle';
    default:
      return 'circle';
  }
}

// 获取节点大小
function getNodeSize(nodeType, status) {
  let baseSize = 20;
  
  // 当前节点放大显示
  if (status === 'current') {
    baseSize *= 1.5;
  }
  
  switch (nodeType) {
    case 'block':
      return baseSize;
    case 'questionnaire':
      return baseSize * 0.8;
    case 'validator':
      return baseSize * 0.9;
    default:
      return baseSize;
  }
}

// 获取连接线颜色
function getEdgeColor(edgeType) {
  switch (edgeType) {
    case 'main':
      return '#4caf50';
    case 'fork':
      return '#ffc107';
    case 'orphan':
      return '#9e9e9e';
    case 'questionnaire':
      return '#9c27b0';
    default:
      return '#2196f3';
  }
}

// 获取连接线样式
function getLinkStyle(edgeType) {
  switch (edgeType) {
    case 'main':
      return 'solid';
    case 'fork':
      return 'dashed';
    case 'orphan':
      return 'dotted';
    case 'mining':
      return [5, 5];
    default:
      return 'solid';
  }
}

// 显示区块详情
function showBlockDetails(nodeData) {
  console.log('显示区块详情:', nodeData);
  
  // 触发事件，通知其他组件显示详情
  const event = new CustomEvent('blockSelected', { detail: nodeData });
  document.dispatchEvent(event);
  
  // 更新详情面板
  const detailsContainer = document.getElementById('block-details-panel');
  if (!detailsContainer) return;
  
  const data = nodeData.data || {};
  
  if (nodeData.type === 'questionnaire') {
    // 问卷节点详情
    detailsContainer.innerHTML = `
      <div class="card">
        <div class="card-header bg-purple text-white">
          <h5 class="mb-0">问卷详情 #${nodeData.id}</h5>
        </div>
        <div class="card-body">
          <table class="table table-sm table-striped">
            <tbody>
              <tr>
                <th>问卷类型:</th>
                <td>${data.type || '未知'}</td>
              </tr>
              <tr>
                <th>创建时间:</th>
                <td>${data.timestamp ? new Date(data.timestamp).toLocaleString() : '未知'}</td>
              </tr>
              <tr>
                <th>问题数量:</th>
                <td>${data.questions || 0}</td>
              </tr>
              <tr>
                <th>填写人数:</th>
                <td>${data.respondents || 0}</td>
              </tr>
              <tr>
                <th>完成率:</th>
                <td>${data.completionRate ? (data.completionRate * 100).toFixed(1) + '%' : '0%'}</td>
              </tr>
              <tr>
                <th>关联区块:</th>
                <td>${data.blockId || '未知'}</td>
              </tr>
              <tr>
                <th>状态:</th>
                <td><span class="badge bg-${nodeData.status === 'active' ? 'success' : 'secondary'}">${nodeData.status === 'active' ? '活跃' : '已关闭'}</span></td>
              </tr>
            </tbody>
          </table>
          
          <button class="btn btn-sm btn-outline-primary mt-2" onclick="viewQuestionnaire(${nodeData.id})">
            查看问卷详情
          </button>
        </div>
      </div>
    `;
  } else {
    // 区块节点详情
    detailsContainer.innerHTML = `
      <div class="card">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">区块 #${nodeData.id} 详情</h5>
        </div>
        <div class="card-body">
          <table class="table table-sm table-striped">
            <tbody>
              <tr>
                <th>区块哈希:</th>
                <td><small class="text-monospace">${data.hash || '未知'}</small></td>
              </tr>
              <tr>
                <th>前一区块哈希:</th>
                <td><small class="text-monospace">${data.previousHash || '未知'}</small></td>
              </tr>
              <tr>
                <th>生成时间:</th>
                <td>${data.timestamp ? new Date(data.timestamp).toLocaleString() : '未知'}</td>
              </tr>
              <tr>
                <th>区块高度:</th>
                <td>${data.height || '未知'}</td>
              </tr>
              <tr>
                <th>包含问卷:</th>
                <td>${data.questionnaires || 0} 份</td>
              </tr>
              <tr>
                <th>区块大小:</th>
                <td>${data.size ? data.size + ' KB' : '未知'}</td>
              </tr>
              <tr>
                <th>难度系数:</th>
                <td>${data.difficulty || '未知'}</td>
              </tr>
              <tr>
                <th>状态:</th>
                <td><span class="badge ${nodeData.status === 'confirmed' ? 'bg-success' : 
                                     nodeData.status === 'pending' ? 'bg-warning' : 
                                     nodeData.status === 'orphaned' ? 'bg-secondary' : 'bg-primary'}">${
                                     nodeData.status === 'confirmed' ? '已确认' : 
                                     nodeData.status === 'pending' ? '待确认' : 
                                     nodeData.status === 'orphaned' ? '孤立区块' : '未知'}</span></td>
              </tr>
            </tbody>
          </table>
          
          ${data.questionnaires > 0 ? `
            <h6 class="mt-3">问卷列表 (${data.questionnaires}份)</h6>
            <div class="questionnaire-list">
              <button class="btn btn-sm btn-outline-primary" onclick="loadQuestionnaires(${nodeData.id})">
                加载问卷列表
              </button>
            </div>
          ` : ''}
        </div>
      </div>
    `;
  }
  
  // 显示详情面板
  detailsContainer.style.display = 'block';
  
  // 如果在小屏幕设备上，滚动到详情区域
  if (window.innerWidth < 992) {
    detailsContainer.scrollIntoView({ behavior: 'smooth' });
  }
}

// 初始化区块链2D视图
function initBlockchain2DView() {
  console.log('初始化区块链2D视图');
  
  const chartContainer = document.getElementById('blockchain-chart');
  if (!chartContainer) {
    console.warn('未找到图表容器 blockchain-chart');
    return;
  }
  
  // 检查是否已经初始化
  if (window.chartsInitialized && window.chartsInitialized.blockchain) {
    console.log('区块链图表已初始化，仅更新数据');
    return;
  }
  
  // 显示加载中状态
  chartContainer.innerHTML = '<div class="chart-loading"><span class="spinner-border spinner-border-sm"></span> 加载图表中...</div>';
  
  try {
    // 初始化图表
    console.log('创建新的区块链2D图表');
    blockchainChart = echarts.init(chartContainer);
    
    // 设置容器高度
    if (chartContainer.clientHeight < 500) {
      chartContainer.style.height = '500px';
    }
    
    // 初始化空的图表以显示加载状态
    const loadingOption = {
      title: {
        text: '区块链网络可视化',
        subtext: '数据加载中...',
        left: 'center'
      },
      graphic: [
        {
          type: 'group',
          left: 'center',
          top: 'center',
          children: [
            {
              type: 'text',
              style: {
                text: '正在加载区块数据...',
                font: '14px Arial',
                fill: '#999'
              }
            }
          ]
        }
      ]
    };
    
    blockchainChart.setOption(loadingOption);
    blockchainChart.showLoading({
      text: '加载区块数据中',
      color: '#4caf50',
      textColor: '#000',
      maskColor: 'rgba(255, 255, 255, 0.8)',
      zlevel: 0
    });
    
    // 记录初始化状态
    if (!window.chartsInitialized) {
      window.chartsInitialized = {};
    }
    window.chartsInitialized.blockchain = true;
    
    // 模拟区块数据
    simulateBlockchainData();
    
    // 窗口调整大小时重新调整图表
    window.addEventListener('resize', function() {
      if (blockchainChart) {
        blockchainChart.resize();
      }
    });
    
    // 标签页切换时调整图表大小
    const blockchainTabLink = document.querySelector('a[data-view="blockchainView"]');
    if (blockchainTabLink) {
      blockchainTabLink.addEventListener('shown.bs.tab', function() {
        setTimeout(() => {
          if (blockchainChart) {
            blockchainChart.resize();
          }
        }, 100);
      });
    }
    
    console.log('区块链2D图表初始化完成');
  } catch (err) {
    console.error('区块链2D图表初始化失败', err);
    chartContainer.innerHTML = '<div class="alert alert-danger">图表初始化失败: ' + err.message + '</div>';
  }
}

// 模拟区块链数据
function simulateBlockchainData() {
  console.log('生成模拟区块链数据');
  
  // 模拟区块节点
  const nodes = [];
  const links = [];
  
  // 主链区块
  const mainChainLength = 15;
  for (let i = 0; i < mainChainLength; i++) {
    // 添加主链区块
    nodes.push({
      id: i,
      type: 'block',
      status: i === mainChainLength - 1 ? 'current' : 'confirmed',
      x: i * 10,
      y: 0,
      data: {
        hash: generateRandomHash(),
        previousHash: i > 0 ? generateRandomHash() : null,
        timestamp: Date.now() - (mainChainLength - i) * 600000,
        height: i,
        transactions: Math.floor(Math.random() * 20) + 5,
        size: Math.floor(Math.random() * 100) + 100,
        difficulty: 4
      }
    });
    
    // 添加主链连接
    if (i > 0) {
      links.push({
        source: i - 1,
        target: i,
        type: 'main'
      });
    }
  }
  
  // 添加分叉
  const forkPoints = [5, 9]; // 在主链的这些点产生分叉
  for (let forkPoint of forkPoints) {
    const forkLength = Math.floor(Math.random() * 3) + 2;
    const forkStartId = nodes.length;
    
    for (let i = 0; i < forkLength; i++) {
      const nodeId = nodes.length;
      // 添加分叉区块
      nodes.push({
        id: nodeId,
        type: 'block',
        status: i === forkLength - 1 ? 'pending' : 'orphaned',
        x: (forkPoint + i + 1) * 10,
        y: (forkPoint === 5 ? 10 : -10), // 上下分布的分叉
        data: {
          hash: generateRandomHash(),
          previousHash: i > 0 ? nodes[forkStartId + i - 1].data.hash : nodes[forkPoint].data.hash,
          timestamp: Date.now() - (forkLength - i) * 300000,
          height: forkPoint + i + 1,
          transactions: Math.floor(Math.random() * 10) + 1,
          size: Math.floor(Math.random() * 50) + 80,
          difficulty: 4
        }
      });
      
      // 添加分叉连接
      if (i === 0) {
        links.push({
          source: forkPoint,
          target: nodeId,
          type: 'fork'
        });
      } else {
        links.push({
          source: forkStartId + i - 1,
          target: nodeId,
          type: 'orphan'
        });
      }
    }
  }
  
  // 添加矿工节点
  const minerCount = 4;
  for (let i = 0; i < minerCount; i++) {
    const nodeId = nodes.length;
    // 添加矿工节点
    nodes.push({
      id: nodeId,
      type: 'miner',
      status: 'active',
      x: (mainChainLength - 1) * 10 + 15, // 矿工节点围绕最新区块
      y: -15 + i * 10,
      data: {
        id: `矿工${i+1}`,
        hashRate: Math.floor(Math.random() * 500) + 100,
        successRate: Math.random() * 0.3 + 0.1
      }
    });
    
    // 连接到最新区块
    links.push({
      source: nodeId,
      target: mainChainLength - 1,
      type: 'mining'
    });
  }
  
  // 应用数据到图表
  setTimeout(() => {
    if (blockchainChart) {
      blockchainChart.hideLoading();
      updateChart(nodes, links);
    }
  }, 1000);  // 延迟1秒显示，增强加载效果
}

// 生成随机哈希
function generateRandomHash() {
  let result = '';
  const characters = 'abcdef0123456789';
  for (let i = 0; i < 64; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
}

// 页面加载时初始化所有图表
document.addEventListener('DOMContentLoaded', function() {
  console.log('页面加载完成，开始初始化图表');
  
  // 防止重复初始化
  if (window.chartsInitialized) {
    console.log('图表已初始化，跳过');
    return;
  }
  
  window.chartsInitialized = {
    blockchain: false,
    surveyAnalysis: false,
    surveyDistribution: false
  };
  
  // 设置默认显示的视图
  const defaultView = document.querySelector('.nav-link.active');
  if (defaultView) {
    const targetId = defaultView.getAttribute('data-view');
    const targetElement = document.getElementById(targetId);
    
    if (targetElement) {
      // 隐藏所有视图
      document.querySelectorAll('.view-content').forEach(el => {
        el.style.display = 'none';
      });
      
      // 显示默认视图
      targetElement.style.display = 'block';
    }
  } else {
    // 如果没有找到默认视图，显示最近区块视图
    const recentBlocksView = document.getElementById('recentBlocksView');
    if (recentBlocksView) {
      recentBlocksView.style.display = 'block';
    }
  }
  
  // 延迟初始化各种图表，确保DOM已完全渲染
  setTimeout(function() {
    try {
      // 初始化2D区块链图表（替代原来的3D视图）
      initBlockchain2DView();
      
      // 初始化问卷分析图表
      initSurveyAnalysisForBlockchain();
      
      // 初始化其他图表
      initSurveyDistributionChart();
      
      console.log('所有图表初始化完成');
    } catch (error) {
      console.error('初始化图表时出错:', error);
    }
  }, 500);
  
  // 为视图切换按钮添加事件监听器
  document.querySelectorAll('a[data-view]').forEach(button => {
    button.addEventListener('click', function(e) {
      e.preventDefault();
      
      // 获取目标视图ID
      const targetId = this.getAttribute('data-view');
      
      // 更新活动按钮状态
      document.querySelectorAll('a[data-view]').forEach(btn => {
        btn.classList.remove('active');
      });
      this.classList.add('active');
      
      // 隐藏所有视图
      document.querySelectorAll('.view-content').forEach(view => {
        view.style.display = 'none';
      });
      
      // 显示目标视图
      const targetView = document.getElementById(targetId);
      if (targetView) {
        targetView.style.display = 'block';
        
        // 如果是区块链视图，刷新图表
        if (targetId === 'blockchainView' && blockchainChart) {
          setTimeout(() => {
            blockchainChart.resize();
          }, 100);
        }
        
        // 如果是问卷分析视图，刷新图表
        if (targetId === 'surveyView' && window.surveyAnalysisChart) {
          setTimeout(() => {
            window.surveyAnalysisChart.resize();
          }, 100);
        }
      }
    });
  });
});

// 显示模式切换处理
document.addEventListener('DOMContentLoaded', function() {
  const displayModeSelect = document.getElementById('displayModeSelect');
  const refreshChartBtn = document.getElementById('refreshChartBtn');
  
  // 区块链图表变量
  let blockchainChart = null;
  
  // 默认显示模式
  let currentDisplayMode = 'chain';
  
  // 模式切换处理
  if (displayModeSelect) {
    displayModeSelect.addEventListener('change', function() {
      currentDisplayMode = this.value;
      console.log('切换显示模式为:', currentDisplayMode);
      refreshBlockchainView();
    });
  }
  
  // 刷新按钮处理
  if (refreshChartBtn) {
    refreshChartBtn.addEventListener('click', function() {
      console.log('手动刷新区块链视图');
      refreshBlockchainView();
    });
  }
  
  // 区块链数据视图切换处理
  const viewControls = document.getElementById('blockDataViewControls');
  if (viewControls) {
    const viewButtons = viewControls.querySelectorAll('button');
    
    viewButtons.forEach(button => {
      button.addEventListener('click', function() {
        const viewType = this.getAttribute('data-view');
        
        if (viewType === 'visual') {
          // 切换到可视化视图时，确保图表初始化并刷新
          setTimeout(() => {
            // 如果图表尚未初始化，则初始化它
            if (!blockchainChart) {
              initBlockchain2DView();
            } else {
              // 否则只刷新布局
              refreshBlockchainView();
            }
          }, 100);
        }
      });
    });
  }
  
  // 刷新区块链视图
  function refreshBlockchainView() {
    if (!blockchainChart) {
      console.warn('区块链图表未初始化，无法刷新');
      return;
    }
    
    // 根据当前显示模式生成不同布局的数据
    generateBlockchainData(currentDisplayMode);
  }
  
  // 生成不同模式的区块链数据
  function generateBlockchainData(mode) {
    console.log('生成区块链数据，模式:', mode);
    
    const nodes = [];
    const links = [];
    
    // 主链区块基本数据
    const mainChainLength = 15;
    
    // 根据不同模式生成不同的布局
    switch (mode) {
      case 'chain':
        // 线性链布局
        for (let i = 0; i < mainChainLength; i++) {
          nodes.push({
            id: i,
            type: 'block',
            status: i === mainChainLength - 1 ? 'current' : 'confirmed',
            x: i * 10,
            y: 0,
            data: generateBlockData(i)
          });
          
          if (i > 0) {
            links.push({
              source: i - 1,
              target: i,
              type: 'main'
            });
          }
        }
        
        // 添加少量分叉
        addForks(nodes, links, [5, 9], 2);
        break;
        
      case 'network':
        // 网络布局（环形 + 中心）
        const radius = 20;
        const center = { x: 50, y: 30 };
        
        // 创建中心节点（最新区块）
        nodes.push({
          id: mainChainLength - 1,
          type: 'block',
          status: 'current',
          x: center.x,
          y: center.y,
          data: generateBlockData(mainChainLength - 1)
        });
        
        // 围绕中心创建其他区块
        for (let i = 0; i < mainChainLength - 1; i++) {
          const angle = (2 * Math.PI * i) / (mainChainLength - 1);
          const x = center.x + radius * Math.cos(angle);
          const y = center.y + radius * Math.sin(angle);
          
          nodes.push({
            id: i,
            type: 'block',
            status: 'confirmed',
            x: x,
            y: y,
            data: generateBlockData(i)
          });
          
          // 连接到中心
          links.push({
            source: i + 1, // 因为中心节点是第一个添加的
            target: 0,
            type: 'main'
          });
        }
        
        // 添加矿工节点
        for (let i = 0; i < 5; i++) {
          const minerAngle = (2 * Math.PI * i) / 5;
          const x = center.x + (radius + 10) * Math.cos(minerAngle);
          const y = center.y + (radius + 10) * Math.sin(minerAngle);
          
          const minerId = nodes.length;
          nodes.push({
            id: minerId,
            type: 'miner',
            status: 'active',
            x: x,
            y: y,
            data: {
              id: `矿工${i+1}`,
              hashRate: Math.floor(Math.random() * 500) + 100,
              successRate: Math.random() * 0.3 + 0.1
            }
          });
          
          links.push({
            source: minerId,
            target: 0,
            type: 'mining'
          });
        }
        break;
        
      case 'fork':
        // 分叉视图 - 显示多条分叉
        // 创建主链
        for (let i = 0; i < mainChainLength; i++) {
          nodes.push({
            id: i,
            type: 'block',
            status: i === mainChainLength - 1 ? 'current' : 'confirmed',
            x: i * 10,
            y: 0,
            data: generateBlockData(i)
          });
          
          if (i > 0) {
            links.push({
              source: i - 1,
              target: i,
              type: 'main'
            });
          }
        }
        
        // 添加多个不同长度的分叉
        addForks(nodes, links, [2, 5, 8, 11], 4);
        break;
        
      case 'mining':
        // 挖矿视图 - 聚焦于矿工和最新区块
        // 只显示最近的几个区块
        const recentBlockCount = 5;
        for (let i = mainChainLength - recentBlockCount; i < mainChainLength; i++) {
          const blockIndex = i - (mainChainLength - recentBlockCount);
          nodes.push({
            id: i,
            type: 'block',
            status: i === mainChainLength - 1 ? 'current' : 'confirmed',
            x: blockIndex * 15,
            y: 20,
            data: generateBlockData(i)
          });
          
          if (blockIndex > 0) {
            links.push({
              source: nodes.length - 2,
              target: nodes.length - 1,
              type: 'main'
            });
          }
        }
        
        // 添加更多矿工节点
        const minerCount = 8;
        for (let i = 0; i < minerCount; i++) {
          const x = 60 + Math.cos(Math.PI * 2 * i / minerCount) * 25;
          const y = 20 + Math.sin(Math.PI * 2 * i / minerCount) * 25;
          
          const minerId = nodes.length;
          nodes.push({
            id: minerId,
            type: 'miner',
            status: 'active',
            x: x,
            y: y,
            data: {
              id: `矿工${i+1}`,
              hashRate: Math.floor(Math.random() * 500) + 100,
              successRate: Math.random() * 0.3 + 0.1
            }
          });
          
          // 连接到最新区块
          links.push({
            source: minerId,
            target: mainChainLength - 1,
            type: 'mining'
          });
        }
        
        // 添加一些待确认区块（竞争中的区块）
        for (let i = 0; i < 3; i++) {
          const pendingId = nodes.length;
          nodes.push({
            id: pendingId,
            type: 'block',
            status: 'pending',
            x: 60 + i * 5,
            y: 40 + i * 5,
            data: {
              hash: generateRandomHash(),
              previousHash: generateRandomHash(),
              timestamp: Date.now() - i * 10000,
              height: mainChainLength,
              transactions: Math.floor(Math.random() * 10) + 5,
              size: Math.floor(Math.random() * 50) + 80,
              difficulty: 4
            }
          });
          
          // 随机选择一个矿工连接
          const randomMiner = mainChainLength + Math.floor(Math.random() * minerCount);
          links.push({
            source: randomMiner,
            target: pendingId,
            type: 'mining'
          });
        }
        break;
    }
    
    // 更新统计数据
    updateBlockchainStats(nodes);
    
    // 更新图表
    updateChart(nodes, links);
  }
  
  // 生成区块数据
  function generateBlockData(index) {
    return {
      hash: generateRandomHash(),
      previousHash: index > 0 ? generateRandomHash() : null,
      timestamp: Date.now() - (15 - index) * 600000,
      height: index,
      transactions: Math.floor(Math.random() * 20) + 5,
      size: Math.floor(Math.random() * 100) + 100,
      difficulty: 4
    };
  }
  
  // 添加分叉
  function addForks(nodes, links, forkPoints, maxLength) {
    for (let forkPoint of forkPoints) {
      const forkLength = Math.floor(Math.random() * maxLength) + 1;
      const forkStartId = nodes.length;
      
      // y坐标偏移，交替上下方向
      const yOffset = (forkPoints.indexOf(forkPoint) % 2 === 0) ? 10 : -10;
      
      for (let i = 0; i < forkLength; i++) {
        const nodeId = nodes.length;
        // 添加分叉区块
        nodes.push({
          id: nodeId,
          type: 'block',
          status: i === forkLength - 1 ? 'pending' : 'orphaned',
          x: (forkPoint + i + 1) * 10,
          y: yOffset * (i > 0 ? 1 : 0.5), // 第一个节点靠近主链
          data: {
            hash: generateRandomHash(),
            previousHash: i > 0 ? generateRandomHash() : null,
            timestamp: Date.now() - (forkLength - i) * 300000,
            height: forkPoint + i + 1,
            transactions: Math.floor(Math.random() * 10) + 1,
            size: Math.floor(Math.random() * 50) + 80,
            difficulty: 4
          }
        });
        
        // 添加分叉连接
        if (i === 0) {
          links.push({
            source: forkPoint,
            target: nodeId,
            type: 'fork'
          });
        } else {
          links.push({
            source: forkStartId + i - 1,
            target: nodeId,
            type: 'orphan'
          });
        }
      }
    }
  }
  
  // 更新区块链统计数据
  function updateBlockchainStats(nodes) {
    const stats = {
      total: nodes.length,
      main: 0,
      pending: 0,
      orphaned: 0,
      questionnaires: 0
    };
    
    nodes.forEach(node => {
      if (node.type === 'block') {
        if (node.status === 'confirmed' || node.status === 'current') {
          stats.main++;
        } else if (node.status === 'pending') {
          stats.pending++;
        } else if (node.status === 'orphaned') {
          stats.orphaned++;
        }
      } else if (node.type === 'questionnaire') {
        stats.questionnaires++;
      }
    });
    
    // 更新DOM
    document.getElementById('total-blocks').textContent = stats.total - stats.questionnaires;
    document.getElementById('main-chain-length').textContent = stats.main;
    document.getElementById('pending-blocks').textContent = stats.pending;
    document.getElementById('orphaned-blocks').textContent = stats.orphaned;
    document.getElementById('active-miners').textContent = stats.questionnaires;
    
    // 更新标签
    const questionnairesLabel = document.querySelector('li:last-child .small');
    if (questionnairesLabel) {
      questionnairesLabel.textContent = '问卷数量';
    }
  }
});
</script>
{% endblock %} 