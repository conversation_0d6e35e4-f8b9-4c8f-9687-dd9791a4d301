{"name": "asset-transfer-secured-agreement", "version": "1.0.0", "description": "Javascript application that uses implicit private data collections, state-based endorsement, and organization-based ownership and access control to keep data private and securely transfer an asset with the consent of both the current owner and buyer", "engines": {"node": ">=12", "npm": ">=5"}, "engineStrict": true, "author": "Hyperledger", "license": "Apache-2.0", "dependencies": {"fabric-ca-client": "^2.2.4", "fabric-network": "^2.2.4"}}