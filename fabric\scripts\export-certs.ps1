# 设置工作目录
$FABRIC_DIR = "D:\admin\4-15\fabric"
Set-Location $FABRIC_DIR

# 创建导出目录
Write-Host "=== Creating Export Directories ==="
if (-not (Test-Path "export")) {
    Write-Host "Creating export directory..."
    New-Item -ItemType Directory -Path "export"
}

# 导出Orderer证书
Write-Host "=== Exporting Orderer Certificates ==="
Write-Host "Exporting orderer TLS certificate..."
docker cp orderer.example.com:/var/hyperledger/production/orderer/tls/server.crt export/orderer-tls.crt
docker cp orderer.example.com:/var/hyperledger/production/orderer/tls/server.key export/orderer-tls.key
docker cp orderer.example.com:/var/hyperledger/production/orderer/tls/ca.crt export/orderer-tls-ca.crt

# 导出Peer证书
Write-Host "=== Exporting Peer Certificates ==="
Write-Host "Exporting peer TLS certificate..."
docker cp peer0.org1.example.com:/var/hyperledger/production/peer/tls/server.crt export/peer-tls.crt
docker cp peer0.org1.example.com:/var/hyperledger/production/peer/tls/server.key export/peer-tls.key
docker cp peer0.org1.example.com:/var/hyperledger/production/peer/tls/ca.crt export/peer-tls-ca.crt

# 导出MSP证书
Write-Host "=== Exporting MSP Certificates ==="
Write-Host "Exporting Admin MSP certificate..."
docker cp peer0.org1.example.com:/var/hyperledger/production/peer/msp/signcerts/cert.pem export/admin-cert.pem
docker cp peer0.org1.example.com:/var/hyperledger/production/peer/msp/keystore/priv_sk export/admin-key.pem
docker cp peer0.org1.example.com:/var/hyperledger/production/peer/msp/cacerts/ca.org1.example.com-cert.pem export/ca-cert.pem

# 验证导出的文件
Write-Host "=== Verifying Exported Files ==="
Write-Host "Checking TLS certificates..."
if (-not (Test-Path "export/orderer-tls.crt")) {
    Write-Host "ERROR: Missing orderer-tls.crt"
    exit 1
}
if (-not (Test-Path "export/peer-tls.crt")) {
    Write-Host "ERROR: Missing peer-tls.crt"
    exit 1
}

Write-Host "Checking MSP certificates..."
if (-not (Test-Path "export/admin-cert.pem")) {
    Write-Host "ERROR: Missing admin-cert.pem"
    exit 1
}
if (-not (Test-Path "export/admin-key.pem")) {
    Write-Host "ERROR: Missing admin-key.pem"
    exit 1
}

Write-Host "=== Certificate Export Complete ==="
Write-Host "All certificates have been successfully exported to the export directory!" 