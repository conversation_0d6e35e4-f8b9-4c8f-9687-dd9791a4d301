/*
 * SPDX-License-Identifier: Apache-2.0
 */

package ledgerapi

import (
	"strings"
)

// <PERSON><PERSON><PERSON> splits a key on colon
func <PERSON><PERSON><PERSON>(key string) []string {
	return strings.Split(key, ":")
}

// <PERSON><PERSON><PERSON> joins key parts using colon
func <PERSON><PERSON><PERSON>(keyParts ...string) string {
	return strings.Join(keyParts, ":")
}

// StateInterface interface states must implement
// for use in a list
type StateInterface interface {
	// GetSplitKey return components that combine to form the key
	GetSplitKey() []string
	Serialize() ([]byte, error)
}
