from datetime import datetime
import json
from app import db
from app.blockchain.crypto import encrypt_data, decrypt_data

class Survey(db.Model):
    """问卷模型"""
    __tablename__ = 'surveys'

    id = db.<PERSON>umn(db.Integer, primary_key=True)
    title = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    expiry_date = db.Column(db.DateTime)
    is_active = db.Column(db.<PERSON><PERSON>an, default=True)
    is_anonymous = db.Column(db.<PERSON>, default=False)
    creator_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # 关系
    questions = db.relationship('Question', backref='survey', lazy='dynamic', cascade='all, delete-orphan')
    responses = db.relationship('Response', backref='survey', lazy='dynamic', cascade='all, delete-orphan')
    
    def is_expired(self):
        """检查问卷是否已过期"""
        if not self.expiry_date:
            return False
        return datetime.utcnow() > self.expiry_date
    
    def response_count(self):
        """获取回复数量"""
        return self.responses.count()
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'created_at': self.created_at.isoformat(),
            'expiry_date': self.expiry_date.isoformat() if self.expiry_date else None,
            'is_active': self.is_active,
            'is_anonymous': self.is_anonymous,
            'creator_id': self.creator_id,
            'response_count': self.response_count()
        }
    
    def __repr__(self):
        return f'<Survey {self.title}>'

class Question(db.Model):
    """问题模型"""
    __tablename__ = 'questions'

    id = db.Column(db.Integer, primary_key=True)
    survey_id = db.Column(db.Integer, db.ForeignKey('surveys.id'), nullable=False)
    content = db.Column(db.Text, nullable=False)
    type = db.Column(db.String(20), nullable=False)  # single_choice, multiple_choice, text
    required = db.Column(db.Boolean, default=False)
    order = db.Column(db.Integer, default=0)
    
    # 关系
    options = db.relationship('Option', backref='question', lazy='dynamic', cascade='all, delete-orphan')
    answers = db.relationship('Answer', backref='question', lazy='dynamic', cascade='all, delete-orphan')
    
    def to_dict(self):
        """转换为字典"""
        question_dict = {
            'id': self.id,
            'survey_id': self.survey_id,
            'content': self.content,
            'type': self.type,
            'required': self.required,
            'order': self.order
        }
        
        if self.type in ['single_choice', 'multiple_choice']:
            question_dict['options'] = [option.to_dict() for option in self.options.order_by(Option.order).all()]
            
        return question_dict
    
    def __repr__(self):
        return f'<Question {self.id}: {self.content[:20]}...>'

class Option(db.Model):
    """选项模型"""
    __tablename__ = 'options'

    id = db.Column(db.Integer, primary_key=True)
    question_id = db.Column(db.Integer, db.ForeignKey('questions.id'), nullable=False)
    content = db.Column(db.Text, nullable=False)
    order = db.Column(db.Integer, default=0)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'question_id': self.question_id,
            'content': self.content,
            'order': self.order
        }
    
    def __repr__(self):
        return f'<Option {self.id}: {self.content[:20]}...>'

class Response(db.Model):
    """问卷回复模型"""
    __tablename__ = 'responses'

    id = db.Column(db.Integer, primary_key=True)
    survey_id = db.Column(db.Integer, db.ForeignKey('surveys.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    submitted_at = db.Column(db.DateTime, default=datetime.utcnow)
    ip_address = db.Column(db.String(50))
    
    # 存储在区块链中的加密数据哈希
    blockchain_hash = db.Column(db.String(64))
    
    # 关系
    answers = db.relationship('Answer', backref='response', lazy='dynamic', cascade='all, delete-orphan')
    
    def save_to_blockchain(self, secret_key):
        """
        加密数据并保存到区块链
        
        应用场景与实现原理:
        1. 应急管理数据安全存储：
           - 在灾害、事故等应急场景下收集的问卷数据需要确保真实性和不可篡改性
           - 问卷数据一旦提交，通过区块链技术确保其不被篡改和伪造
        
        2. 实现流程：
           - 首先收集问卷回答的完整数据，包括问题和回答内容
           - 使用AES-256加密算法对数据进行加密，确保数据隐私
           - 通过Fabric区块链网络接口将加密数据写入区块链
           - 获取交易哈希并保存到数据库，作为区块链验证凭证
        
        3. 应急管理应用价值：
           - 提高应急管理数据的可信度，为决策提供可靠依据
           - 实现数据的分布式存储，在灾害情况下提高数据可用性
           - 防止恶意篡改应急响应和灾情评估的数据
           - 支持多部门协同的应急管理问卷调查，建立统一可信的数据平台
        """
        from app.blockchain.chain import add_transaction
        
        # 收集所有回答
        data = {
            'response_id': self.id,
            'survey_id': self.survey_id,
            'user_id': self.user_id,
            'submitted_at': self.submitted_at.isoformat(),
            'answers': [answer.to_dict() for answer in self.answers.all()]
        }
        
        # 加密数据
        encrypted_data = encrypt_data(json.dumps(data), secret_key)
        
        # 添加到区块链
        transaction_hash = add_transaction(encrypted_data)
        self.blockchain_hash = transaction_hash
        db.session.commit()
        
        return transaction_hash
    
    def get_blockchain_data(self, secret_key):
        """
        从区块链获取并解密数据
        
        应用场景与实现原理:
        1. 应急管理数据验证与审计：
           - 在对应急管理数据进行分析和决策前，需要验证数据的真实性
           - 通过区块链验证数据未被篡改，确保分析决策基于真实数据
        
        2. 实现流程：
           - 通过存储的交易哈希从区块链获取加密数据
           - 使用相同的密钥解密数据，恢复原始问卷回答
           - 验证数据的完整性和一致性
        
        3. 应急管理应用价值：
           - 对历史应急响应数据进行可信审计，评估应急响应效果
           - 为灾害损失评估提供可验证的数据支持
           - 支持跨部门、跨区域的应急管理数据共享与协作
           - 建立应急管理问卷调查的完整性追踪机制
        """
        from app.blockchain.chain import get_transaction_data
        
        if not self.blockchain_hash:
            return None
        
        # 从区块链获取加密数据
        encrypted_data = get_transaction_data(self.blockchain_hash)
        if not encrypted_data:
            return None
        
        # 解密数据
        try:
            decrypted_data = decrypt_data(encrypted_data, secret_key)
            return json.loads(decrypted_data)
        except Exception as e:
            print(f"解密错误: {e}")
            return None
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'survey_id': self.survey_id,
            'user_id': self.user_id,
            'submitted_at': self.submitted_at.isoformat(),
            'ip_address': self.ip_address,
            'blockchain_hash': self.blockchain_hash,
            'answers': [answer.to_dict() for answer in self.answers.all()]
        }
    
    def __repr__(self):
        return f'<Response {self.id} for Survey {self.survey_id}>'

class Answer(db.Model):
    """回答模型"""
    __tablename__ = 'answers'

    id = db.Column(db.Integer, primary_key=True)
    response_id = db.Column(db.Integer, db.ForeignKey('responses.id'), nullable=False)
    question_id = db.Column(db.Integer, db.ForeignKey('questions.id'), nullable=False)
    
    # 针对不同类型问题的答案存储
    text_answer = db.Column(db.Text)
    selected_options = db.Column(db.Text)  # 存储选项ID，用逗号分隔
    
    def set_selected_options(self, option_ids):
        """设置选项"""
        if isinstance(option_ids, list):
            self.selected_options = ','.join(map(str, option_ids))
        else:
            self.selected_options = str(option_ids)
    
    def get_selected_options(self):
        """获取选项列表"""
        if not self.selected_options:
            return []
        return [int(id) for id in self.selected_options.split(',')]
    
    def to_dict(self):
        """转换为字典"""
        answer_dict = {
            'id': self.id,
            'response_id': self.response_id,
            'question_id': self.question_id
        }
        
        question = Question.query.get(self.question_id)
        if question:
            if question.type == 'text':
                answer_dict['text_answer'] = self.text_answer
            else:
                answer_dict['selected_options'] = self.get_selected_options()
                
        return answer_dict
    
    def __repr__(self):
        return f'<Answer {self.id} for Question {self.question_id}>' 