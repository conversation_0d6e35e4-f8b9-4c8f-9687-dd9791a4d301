{% extends "base.html" %}

{% block title %}3D可视化测试 - 基于区块链的应急管理问卷调查系统{% endblock %}

{% block extra_css %}
<style>
    .test-container {
        width: 100%;
        height: 400px;
        background: #0e1621;
        margin-bottom: 20px;
        border-radius: 10px;
    }
    .test-info {
        padding: 15px;
        margin-bottom: 20px;
        background-color: #f8f9fa;
        border-radius: 5px;
    }
    .webgl-status {
        font-weight: bold;
    }
    .status-success {
        color: #28a745;
    }
    .status-error {
        color: #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <h1 class="mb-4">3D可视化兼容性测试</h1>
    
    <div class="test-info">
        <h4>浏览器信息</h4>
        <div id="browser-info">检测中...</div>
    </div>
    
    <div class="test-info">
        <h4>WebGL支持状态</h4>
        <div id="webgl-info">检测中...</div>
    </div>
    
    <div class="test-info">
        <h4>ECharts检测</h4>
        <div id="echarts-info">检测中...</div>
    </div>
    
    <div class="test-info">
        <h4>ECharts-GL检测</h4>
        <div id="echarts-gl-info">检测中...</div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h3 class="mb-0">WebGL测试</h3>
        </div>
        <div class="card-body">
            <canvas id="webgl-test" width="300" height="200" style="border:1px solid #ccc"></canvas>
            <div id="webgl-test-result" class="mt-2"></div>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h3 class="mb-0">ECharts 2D测试</h3>
        </div>
        <div class="card-body">
            <div class="test-container" id="echarts-2d-test"></div>
            <div id="echarts-2d-result" class="mt-2"></div>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h3 class="mb-0">ECharts 3D测试</h3>
        </div>
        <div class="card-body">
            <div class="test-container" id="echarts-3d-test"></div>
            <div id="echarts-3d-result" class="mt-2"></div>
        </div>
    </div>
    
    <div class="text-center">
        <a href="{{ url_for('main.blockchain_info') }}" class="btn btn-primary">返回区块链页面</a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- 引入 ECharts -->
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.2/dist/echarts.min.js"></script>
<!-- 引入 ECharts GL 用于3D图表 -->
<script src="https://cdn.jsdelivr.net/npm/echarts-gl@2.0.9/dist/echarts-gl.min.js"></script>

<script>
$(document).ready(function() {
    // 浏览器信息
    var browserInfo = "浏览器: " + navigator.userAgent;
    $("#browser-info").html(browserInfo);
    
    // 检测WebGL支持
    function detectWebGL() {
        var canvas = document.createElement("canvas");
        var gl = null;
        try {
            gl = canvas.getContext("webgl") || canvas.getContext("experimental-webgl");
        } catch(e) {}
        
        if (gl) {
            return {
                supported: true,
                renderer: gl.getParameter(gl.RENDERER),
                vendor: gl.getParameter(gl.VENDOR),
                version: gl.getParameter(gl.VERSION)
            };
        } else {
            return {
                supported: false
            };
        }
    }
    
    var webglInfo = detectWebGL();
    var webglStatus = "<div class='webgl-status " + 
                   (webglInfo.supported ? "status-success'>支持" : "status-error'>不支持") + 
                   "</div>";
                   
    if (webglInfo.supported) {
        webglStatus += "<div>渲染器: " + webglInfo.renderer + "</div>";
        webglStatus += "<div>供应商: " + webglInfo.vendor + "</div>";
        webglStatus += "<div>版本: " + webglInfo.version + "</div>";
    } else {
        webglStatus += "<div class='status-error'>您的浏览器不支持WebGL，这可能导致3D可视化无法使用。</div>";
        webglStatus += "<div>建议尝试更新浏览器或使用Chrome、Firefox、Edge等现代浏览器。</div>";
    }
    
    $("#webgl-info").html(webglStatus);
    
    // 测试WebGL
    function testWebGL() {
        var canvas = document.getElementById("webgl-test");
        var gl = null;
        
        try {
            gl = canvas.getContext("webgl") || canvas.getContext("experimental-webgl");
            if (!gl) {
                return "无法初始化WebGL，您的浏览器可能不支持";
            }
            
            // 设置清除颜色为蓝色
            gl.clearColor(0.0, 0.0, 0, 1.0);
            // 清除颜色缓冲区
            gl.clear(gl.COLOR_BUFFER_BIT);
            
            // 绘制一个简单的三角形
            var vertexShaderSource = `
                attribute vec4 a_position;
                void main() {
                    gl_Position = a_position;
                }
            `;
            
            var fragmentShaderSource = `
                precision mediump float;
                void main() {
                    gl_FragColor = vec4(1.0, 0.0, 0.0, 1.0);
                }
            `;
            
            var vertexShader = gl.createShader(gl.VERTEX_SHADER);
            gl.shaderSource(vertexShader, vertexShaderSource);
            gl.compileShader(vertexShader);
            
            var fragmentShader = gl.createShader(gl.FRAGMENT_SHADER);
            gl.shaderSource(fragmentShader, fragmentShaderSource);
            gl.compileShader(fragmentShader);
            
            var program = gl.createProgram();
            gl.attachShader(program, vertexShader);
            gl.attachShader(program, fragmentShader);
            gl.linkProgram(program);
            gl.useProgram(program);
            
            var positions = new Float32Array([
                0.0, 0.5, 0.0,
                -0.5, -0.5, 0.0,
                0.5, -0.5, 0.0
            ]);
            
            var positionBuffer = gl.createBuffer();
            gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
            gl.bufferData(gl.ARRAY_BUFFER, positions, gl.STATIC_DRAW);
            
            var positionAttributeLocation = gl.getAttribLocation(program, "a_position");
            gl.enableVertexAttribArray(positionAttributeLocation);
            gl.vertexAttribPointer(positionAttributeLocation, 3, gl.FLOAT, false, 0, 0);
            
            gl.drawArrays(gl.TRIANGLES, 0, 3);
            
            return "成功渲染WebGL三角形";
        } catch (e) {
            return "WebGL测试错误: " + e.message;
        }
    }
    
    $("#webgl-test-result").html(testWebGL());
    
    // 检测ECharts
    var echartsStatus = "";
    try {
        if (typeof echarts === 'object' && typeof echarts.init === 'function') {
            echartsStatus = "<div class='webgl-status status-success'>ECharts加载成功</div>";
            echartsStatus += "<div>版本: " + echarts.version + "</div>";
        } else {
            echartsStatus = "<div class='webgl-status status-error'>ECharts加载失败或未找到</div>";
        }
    } catch (e) {
        echartsStatus = "<div class='webgl-status status-error'>ECharts检测错误: " + e.message + "</div>";
    }
    
    $("#echarts-info").html(echartsStatus);
    
    // 检测ECharts-GL
    var echartsGLStatus = "";
    try {
        // 检查是否有graph3D类型支持
        if (typeof echarts === 'object' && 
            echarts.graphic && 
            echarts.graphic.registerShape &&
            typeof echarts.getMap === 'function') {
            echartsGLStatus = "<div class='webgl-status status-success'>ECharts-GL加载成功</div>";
        } else {
            echartsGLStatus = "<div class='webgl-status status-error'>ECharts-GL加载失败或未找到</div>";
        }
    } catch (e) {
        echartsGLStatus = "<div class='webgl-status status-error'>ECharts-GL检测错误: " + e.message + "</div>";
    }
    
    $("#echarts-gl-info").html(echartsGLStatus);
    
    // 测试ECharts 2D
    function test2DChart() {
        try {
            var chartDom = document.getElementById('echarts-2d-test');
            var myChart = echarts.init(chartDom);
            
            var option = {
                xAxis: {
                    type: 'category',
                    data: ['A', 'B', 'C', 'D', 'E']
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        data: [10, 20, 30, 40, 50],
                        type: 'bar'
                    }
                ]
            };
            
            myChart.setOption(option);
            return "成功渲染2D图表";
        } catch (e) {
            return "2D图表渲染错误: " + e.message;
        }
    }
    
    $("#echarts-2d-result").html(test2DChart());
    
    // 测试ECharts 3D
    function test3DChart() {
        try {
            var chartDom = document.getElementById('echarts-3d-test');
            var myChart = echarts.init(chartDom);
            
            // 创建简单的3D散点图
            var data = [];
            for (var i = 0; i < 50; i++) {
                data.push([
                    Math.random() * 100,
                    Math.random() * 100,
                    Math.random() * 100
                ]);
            }
            
            var option = {
                backgroundColor: '#0e1621',
                xAxis3D: {},
                yAxis3D: {},
                zAxis3D: {},
                grid3D: {},
                series: [{
                    type: 'scatter3D',
                    data: data,
                    symbolSize: 12,
                    itemStyle: {
                        color: function(params) {
                            var i = params.dataIndex;
                            return 'rgb(' + [
                                Math.round(i * 5),
                                Math.round(i * 2),
                                Math.round(255 - i * 5)
                            ].join(',') + ')';
                        }
                    }
                }]
            };
            
            myChart.setOption(option);
            return "成功渲染3D图表";
        } catch (e) {
            return "3D图表渲染错误: " + e.message + "<br>这表明您的浏览器可能不支持ECharts-GL的3D功能";
        }
    }
    
    $("#echarts-3d-result").html(test3DChart());
});
</script>
{% endblock %} 