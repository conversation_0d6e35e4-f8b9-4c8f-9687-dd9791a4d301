(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD
        define(['exports', 'echarts'], factory);
    } else if (typeof exports === 'object' && typeof exports.nodeName !== 'string') {
        // CommonJS
        factory(exports, require('echarts'));
    } else {
        // 全局变量
        factory({}, root.echarts);
    }
}(this, function (exports, echarts) {
    var map = {
        "type": "FeatureCollection",
        "features": [
            {
                "type": "Feature",
                "id": "710000",
                "properties": {
                    "id": "710000",
                    "cp": [121.509062, 25.044332],
                    "name": "台湾",
                    "childNum": 1
                },
                "geometry": {
                    "type": "MultiPolygon",
                    "coordinates": [
                        [
                            [
                                [121.951244, 22.006627],
                                [121.526386, 21.849984],
                                [121.874367, 22.113281],
                                [121.951244, 22.006627]
                            ]
                        ]
                    ]
                }
            },
            {
                "type": "Feature",
                "id": "110000",
                "properties": {
                    "id": "110000",
                    "cp": [116.405285, 39.904989],
                    "name": "北京",
                    "childNum": 1
                },
                "geometry": {
                    "type": "Polygon",
                    "coordinates": [
                        [
                            [116.695, 39.835],
                            [116.705, 39.835],
                            [116.705, 39.845],
                            [116.695, 39.845],
                            [116.695, 39.835]
                        ]
                    ]
                }
            },
            {
                "type": "Feature",
                "id": "120000",
                "properties": {
                    "id": "120000",
                    "cp": [117.190182, 39.125596],
                    "name": "天津",
                    "childNum": 1
                },
                "geometry": {
                    "type": "Polygon",
                    "coordinates": [
                        [
                            [117.190, 39.120],
                            [117.200, 39.120],
                            [117.200, 39.130],
                            [117.190, 39.130],
                            [117.190, 39.120]
                        ]
                    ]
                }
            },
            {
                "type": "Feature",
                "id": "310000",
                "properties": {
                    "id": "310000",
                    "cp": [121.472644, 31.231706],
                    "name": "上海",
                    "childNum": 1
                },
                "geometry": {
                    "type": "Polygon",
                    "coordinates": [
                        [
                            [121.470, 31.230],
                            [121.480, 31.230],
                            [121.480, 31.240],
                            [121.470, 31.240],
                            [121.470, 31.230]
                        ]
                    ]
                }
            },
            {
                "type": "Feature",
                "id": "500000",
                "properties": {
                    "id": "500000",
                    "cp": [106.504962, 29.533155],
                    "name": "重庆",
                    "childNum": 1
                },
                "geometry": {
                    "type": "Polygon",
                    "coordinates": [
                        [
                            [106.500, 29.530],
                            [106.510, 29.530],
                            [106.510, 29.540],
                            [106.500, 29.540],
                            [106.500, 29.530]
                        ]
                    ]
                }
            },
            {
                "type": "Feature",
                "id": "440000",
                "properties": {
                    "id": "440000",
                    "cp": [113.280637, 23.125178],
                    "name": "广东",
                    "childNum": 1
                },
                "geometry": {
                    "type": "Polygon",
                    "coordinates": [
                        [
                            [113.280, 23.120],
                            [113.290, 23.120],
                            [113.290, 23.130],
                            [113.280, 23.130],
                            [113.280, 23.120]
                        ]
                    ]
                }
            },
            {
                "type": "Feature",
                "id": "510000",
                "properties": {
                    "id": "510000",
                    "cp": [104.065735, 30.659462],
                    "name": "四川",
                    "childNum": 1
                },
                "geometry": {
                    "type": "Polygon",
                    "coordinates": [
                        [
                            [104.060, 30.650],
                            [104.070, 30.650],
                            [104.070, 30.660],
                            [104.060, 30.660],
                            [104.060, 30.650]
                        ]
                    ]
                }
            },
            {
                "type": "Feature",
                "id": "330000",
                "properties": {
                    "id": "330000",
                    "cp": [120.153576, 30.287459],
                    "name": "浙江",
                    "childNum": 1
                },
                "geometry": {
                    "type": "Polygon",
                    "coordinates": [
                        [
                            [120.150, 30.280],
                            [120.160, 30.280],
                            [120.160, 30.290],
                            [120.150, 30.290],
                            [120.150, 30.280]
                        ]
                    ]
                }
            }
        ]
    };

    echarts.registerMap('china', map);
})); 