{% extends "base.html" %}

{% block title %}问卷结果 - {{ survey.title }} - 基于区块链的应急管理问卷调查系统{% endblock %}

{% block head %}
{{ super() }}
<!-- 确保ECharts库在页面加载完成前可用 -->
{% endblock %}

{% block extra_css %}
<style>
    .chart-container {
        min-height: 300px;
        margin-bottom: 30px;
        background-color: #fff;
        border-radius: 5px;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }
    .question-card {
        margin-bottom: 30px;
        border-radius: 10px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        overflow: hidden;
    }
    .question-header {
        padding: 15px 20px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #eee;
        font-weight: 500;
    }
    .text-answers {
        max-height: 300px;
        overflow-y: auto;
        padding: 10px 15px;
    }
    .answer-item {
        border-bottom: 1px solid #eee;
        padding: 10px 0;
    }
    .answer-item:last-child {
        border-bottom: none;
    }
    .progress {
        height: 25px;
        border-radius: 12.5px;
        background-color: #f5f5f5;
        overflow: hidden;
    }
    .progress-bar {
        background-color: #3498db;
        color: #fff;
        font-weight: 500;
        text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        transition: width 0.6s ease;
    }
    .option-text {
        margin-bottom: 5px;
    }
    .option-statistics {
        padding: 0 15px;
    }
    
    /* 区块链统计模块样式 */
    .blockchain-section h2 {
        color: #2c3e50;
        position: relative;
        padding-bottom: 10px;
    }
    
    .blockchain-section h2:after {
        content: "";
        position: absolute;
        left: 0;
        bottom: 0;
        width: 50px;
        height: 3px;
        background-color: #3498db;
    }
    
    .blockchain-card {
        border: none;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }
    
    .blockchain-card:hover {
        transform: translateY(-5px);
    }
    
    .security-feature-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        display: inline-block;
        padding: 15px;
        border-radius: 50%;
        background-color: rgba(46, 204, 113, 0.1);
    }
    
    #blockchain-survey-trend-chart {
        border-radius: 5px;
        overflow: hidden;
    }
    
    .trend-days.active {
        font-weight: bold;
    }
    
    /* 响应式调整 */
    @media (max-width: 768px) {
        .blockchain-section h2 {
            font-size: 1.5rem;
        }
        
        .security-feature {
            margin-bottom: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="mb-2">{{ survey.title }}</h1>
        {% if survey.description %}
        <p class="lead">{{ survey.description }}</p>
        {% endif %}
    </div>
    <div class="col-md-4 text-end">
        <div class="btn-group" role="group">
            {% if current_user.is_authenticated and survey.creator_id == current_user.id %}
            <a href="{{ url_for('survey.edit_survey', survey_id=survey.id) }}" class="btn btn-outline-primary">
                <i class="fas fa-edit me-1"></i> 编辑问卷
            </a>
            <a href="{{ url_for('survey.export_survey', survey_id=survey.id) }}" class="btn btn-outline-success">
                <i class="fas fa-download me-1"></i> 导出数据
            </a>
            {% endif %}
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        <h3 class="mb-0">{{ stats.total_responses }}</h3>
                        <p class="text-muted">总回复数</p>
                    </div>
                    <div class="col-md-3 text-center">
                        <h3 class="mb-0">{{ stats.questions|length }}</h3>
                        <p class="text-muted">问题数</p>
                    </div>
                    <div class="col-md-3 text-center">
                        <h3 class="mb-0">{{ survey.created_at.strftime('%Y-%m-%d') }}</h3>
                        <p class="text-muted">创建日期</p>
                    </div>
                    <div class="col-md-3 text-center">
                        <h3 class="mb-0">
                            {% if survey.is_active %}
                            <span class="badge bg-success">活跃</span>
                            {% else %}
                            <span class="badge bg-secondary">已关闭</span>
                            {% endif %}
                        </h3>
                        <p class="text-muted">问卷状态</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

    <!-- 区块链数据信息 -->
<div class="row mt-4 blockchain-section">
    <div class="col-md-12">
        <h2 class="mb-3">区块链问卷参与统计</h2>
        <div class="card blockchain-card">
            <div class="card-header bg-primary text-white">
                <h3 class="mb-0"><i class="fas fa-chart-line me-2"></i>区块链问卷填写趋势</h3>
            </div>
            <div class="card-body">
                <p class="mb-3">通过区块链技术记录和验证的问卷参与情况，每一次提交都会生成一个区块链交易记录，确保数据的真实性和不可篡改性。</p>
                <div class="chart-container" id="blockchain-survey-trend-chart" style="height: 380px;">
                    <div class="text-center p-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">加载区块链数据中...</p>
                    </div>
                </div>
                <div class="text-center mt-3">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary btn-sm trend-days" data-days="7">
                            <i class="fas fa-calendar-week me-1"></i> 近7天
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm trend-days active" data-days="30">
                            <i class="fas fa-calendar-alt me-1"></i> 近30天
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm trend-days" data-days="90">
                            <i class="fas fa-calendar me-1"></i> 近90天
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% if stats.total_responses > 0 %}
<div class="row mb-4">
    <div class="col-md-12">
        <h2 class="mb-3">问卷统计结果</h2>
        
        {% for question in stats.questions %}
        <div class="question-card">
            <div class="question-header">
                <h4 class="mb-0">{{ question.content }}</h4>
                <small class="text-muted">问题类型: {% if question.type == 'single_choice' %}单选题{% elif question.type == 'multiple_choice' %}多选题{% else %}文本题{% endif %}</small>
            </div>
            <div class="card-body p-4">
                {% if question.type in ['single_choice', 'multiple_choice'] %}
                <div class="chart-container" id="chart-container-{{ question.id }}">
                    <div class="text-center p-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">加载图表中...</p>
                    </div>
                </div>
                
                <div class="option-statistics mt-4">
                    <h5 class="mb-3 pb-2 border-bottom">选项统计详情</h5>
                    {% for option in question.options %}
                    <div class="mb-3">
                        <div class="option-text d-flex justify-content-between">
                            <strong>{{ option.content }}</strong>
                            <span class="badge bg-primary ms-2">{{ option.count }} 次选择 ({{ option.percentage }}%)</span>
                        </div>
                        <div class="progress mt-2">
                            <div class="progress-bar" role="progressbar" style="width: {{ option.percentage }}%;" aria-valuenow="{{ option.percentage }}" aria-valuemin="0" aria-valuemax="100">{{ option.percentage }}%</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <div class="text-center mt-4">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary btn-sm chart-type" data-type="bar" data-question="{{ question.id }}">
                            <i class="fas fa-chart-bar me-1"></i> 柱状图
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm chart-type" data-type="pie" data-question="{{ question.id }}">
                            <i class="fas fa-chart-pie me-1"></i> 饼图
                        </button>
                    </div>
                </div>
                
                {% else %}
                <h5 class="mb-3">文本回答</h5>
                <div class="text-answers">
                    {% if question.answers %}
                    {% for answer in question.answers %}
                    <div class="answer-item">
                        <i class="fas fa-comment text-muted me-2"></i> {{ answer }}
                    </div>
                    {% endfor %}
                    {% else %}
                    <p class="text-muted">没有回答</p>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% else %}
<div class="row">
    <div class="col-md-12">
        <div class="alert alert-info" role="alert">
            <i class="fas fa-info-circle me-2"></i> 此问卷暂无回复数据。
        </div>
    </div>
</div>
{% endif %}



<!-- 区块链安全信息 -->
<div class="row mt-4 blockchain-section">
    <div class="col-md-12">
        <h2 class="mb-3">区块链数据安全保障</h2>
        <div class="card blockchain-card">
            <div class="card-header bg-success text-white">
                <h3 class="mb-0"><i class="fas fa-shield-alt me-2"></i>数据加密与安全机制</h3>
            </div>
            <div class="card-body">
                <p>本问卷的所有回答数据均通过区块链技术进行加密存储，确保数据的安全性和完整性。主要特点：</p>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-0 bg-light security-feature">
                            <div class="card-body text-center">
                                <i class="fas fa-lock security-feature-icon text-success"></i>
                                <h5>AES-256加密</h5>
                                <p class="mb-0">所有答卷数据均使用高强度AES-256算法加密，保护数据安全</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-0 bg-light security-feature">
                            <div class="card-body text-center">
                                <i class="fas fa-link security-feature-icon text-primary"></i>
                                <h5>区块链存储</h5>
                                <p class="mb-0">答卷数据通过区块链存储，采用分布式账本技术确保不可篡改</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-0 bg-light security-feature">
                            <div class="card-body text-center">
                                <i class="fas fa-key security-feature-icon text-warning"></i>
                                <h5>密钥授权</h5>
                                <p class="mb-0">只有具有正确密钥的授权用户才能解密和访问原始数据</p>
                            </div>
                        </div>
                    </div>
                </div>
                <p class="mt-3 mb-0">区块链技术确保了问卷数据的真实性和完整性，同时高级加密标准保护了回答者的隐私。您的数据安全是我们的首要任务。</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 加载问题图表
    {% for question in stats.questions %}
    {% if question.type in ['single_choice', 'multiple_choice'] %}
    loadChart({{ question.id }}, 'bar');
    {% endif %}
    {% endfor %}
    
    // 切换图表类型
    $('.chart-type').click(function() {
        var questionId = $(this).data('question');
        var chartType = $(this).data('type');
        loadChart(questionId, chartType);
    });
    
    // 加载问题图表函数
    function loadChart(questionId, chartType) {
        var container = $('#chart-container-' + questionId);
        container.html('<div class="text-center p-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">加载中...</span></div><p class="mt-2">加载图表中...</p></div>');
        
        // 添加随机参数避免缓存问题
        var cacheBuster = new Date().getTime();
        
        $.ajax({
            url: '/survey/{{ survey.id }}/chart/' + questionId + '?type=' + chartType + '&_=' + cacheBuster,
            type: 'GET',
            timeout: 30000, // 30秒超时
            success: function(response) {
                // 使用ECharts直接渲染图表
                container.empty();
                
                // 创建图表容器元素
                var chartDom = document.createElement('div');
                chartDom.style.width = '100%';
                chartDom.style.height = '300px';
                container.append(chartDom);
                
                // 初始化ECharts实例
                var myChart = echarts.init(chartDom);
                
                try {
                    // 准备数据
                    var labels = [];
                    var values = [];
                    var colors = ['#3498db', '#2ecc71', '#e74c3c', '#f39c12', '#9b59b6', '#1abc9c', '#34495e'];
                    
                    if (response.question_stats && response.question_stats.options) {
                        response.question_stats.options.forEach(function(option) {
                            labels.push(option.content);
                            values.push(option.count);
                        });
                    } else if (response.chart_data) {
                        // 兼容旧的API格式
                        labels = response.chart_data.labels || [];
                        values = response.chart_data.values || [];
                    }
                    
                    var option = {};
                    if (chartType === 'bar') {
                        // 柱状图配置
                        option = {
                            title: {
                                text: response.question || '选项统计',
                                left: 'center',
                                textStyle: {
                                    fontSize: 16
                                }
                            },
                            tooltip: {
                                trigger: 'axis',
                                axisPointer: {
                                    type: 'shadow'
                                },
                                formatter: function(params) {
                                    var param = params[0];
                                    return param.name + '<br/>' + 
                                           '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' + param.color + '"></span>' +
                                           '选择次数: ' + param.value + '<br/>' +
                                           '占比: ' + (param.value / values.reduce((a, b) => a + b, 0) * 100).toFixed(2) + '%';
                                }
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                bottom: '15%',
                                top: '60px',
                                containLabel: true
                            },
                            xAxis: {
                                type: 'category',
                                data: labels,
                                axisLabel: {
                                    interval: 0,
                                    rotate: 30,
                                    formatter: function(value) {
                                        if (value.length > 10) {
                                            return value.substring(0, 10) + '...';
                                        }
                                        return value;
                                    }
                                }
                            },
                            yAxis: {
                                type: 'value',
                                name: '选择次数',
                                minInterval: 1 // 确保y轴刻度为整数
                            },
                            series: [
                                {
                                    name: '选择次数',
                                    type: 'bar',
                                    data: values,
                                    itemStyle: {
                                        color: function(params) {
                                            // 返回不同的颜色
                                            return colors[params.dataIndex % colors.length];
                                        }
                                    },
                                    label: {
                                        show: true,
                                        position: 'top',
                                        formatter: '{c}'
                                    },
                                    barMaxWidth: 50
                                }
                            ],
                            toolbox: {
                                feature: {
                                    saveAsImage: { title: '保存为图片' },
                                    restore: { title: '还原' }
                                }
                            }
                        };
                    } else if (chartType === 'pie') {
                        // 饼图配置
                        var pieData = [];
                        for (var i = 0; i < labels.length; i++) {
                            if (values[i] > 0) { // 只包含有值的选项
                                pieData.push({
                                    name: labels[i],
                                    value: values[i]
                                });
                            }
                        }
                        
                        option = {
                            title: {
                                text: response.question || '选项统计',
                                left: 'center',
                                textStyle: {
                                    fontSize: 16
                                }
                            },
                            tooltip: {
                                trigger: 'item',
                                formatter: '{a} <br/>{b}: {c} ({d}%)'
                            },
                            legend: {
                                orient: 'horizontal',
                                bottom: 10,
                                data: labels,
                                type: 'scroll',
                                formatter: function(name) {
                                    if (name.length > 15) {
                                        return name.substring(0, 15) + '...';
                                    }
                                    return name;
                                }
                            },
                            series: [
                                {
                                    name: '选择情况',
                                    type: 'pie',
                                    radius: ['35%', '65%'],
                                    center: ['50%', '50%'],
                                    avoidLabelOverlap: true,
                                    itemStyle: {
                                        borderRadius: 5,
                                        borderColor: '#fff',
                                        borderWidth: 2
                                    },
                                    label: {
                                        formatter: '{b}: {c} ({d}%)',
                                        fontSize: 12
                                    },
                                    labelLine: {
                                        length: 15,
                                        length2: 10,
                                        smooth: true
                                    },
                                    data: pieData,
                                    emphasis: {
                                        itemStyle: {
                                            shadowBlur: 10,
                                            shadowOffsetX: 0,
                                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                                        }
                                    }
                                }
                            ],
                            toolbox: {
                                feature: {
                                    saveAsImage: { title: '保存为图片' },
                                    restore: { title: '还原' }
                                }
                            }
                        };
                    }
                    
                    // 应用配置项并渲染图表
                    myChart.setOption(option);
                    
                    // 响应容器大小变化
                    window.addEventListener('resize', function() {
                        myChart.resize();
                    });
                    
                    // 存储图表实例，便于后续操作
                    container.data('chart', myChart);
                    
                } catch (e) {
                    console.error('渲染图表错误:', e);
                    container.html(
                        '<div class="alert alert-danger">' +
                        '<i class="fas fa-exclamation-triangle me-2"></i>渲染图表失败' +
                        '<p class="small mt-2 mb-0">错误: ' + e.message + '</p>' +
                        '</div>' +
                        '<button class="btn btn-outline-primary btn-sm mt-2">重试</button>'
                    );
                    
                    // 绑定重试按钮
                    container.find('button').click(function() {
                        loadChart(questionId, chartType);
                    });
                }
            },
            error: function(xhr, status, error) {
                console.error('加载图表错误:', error, xhr.responseText);
                if (status === 'timeout') {
                    container.html(
                        '<div class="alert alert-warning">' +
                        '<i class="fas fa-clock me-2"></i>加载图表超时' +
                        '<p class="small mt-2 mb-0">请尝试刷新页面或稍后再试</p>' +
                        '</div>' +
                        '<button class="btn btn-outline-primary btn-sm mt-2">重试加载</button>'
                    );
                } else {
                    container.html(
                        '<div class="alert alert-danger">' +
                        '<i class="fas fa-exclamation-triangle me-2"></i>加载图表失败' +
                        '<p class="small mt-2 mb-0">错误: ' + (xhr.responseJSON?.message || error || '未知错误') + '</p>' +
                        '</div>' +
                        '<button class="btn btn-outline-primary btn-sm mt-2">重试加载</button>'
                    );
                }
                
                // 绑定重试按钮事件
                container.find('button').click(function() {
                    loadChart(questionId, chartType);
                });
            }
        });
    }
    
    // 区块链问卷趋势图表
    var surveyTrendChart = null;
    var selectedDays = 30; // 默认显示30天数据
    
    // 初始化区块链趋势图表
    function initTrendChart() {
        console.log('初始化区块链趋势图表...');
        var chartDom = document.getElementById('blockchain-survey-trend-chart');
        if (!chartDom) {
            console.error('找不到图表容器: blockchain-survey-trend-chart');
            return;
        }
        
        try {
            if (typeof echarts === 'undefined') {
                console.error('ECharts库未加载');
                $('#blockchain-survey-trend-chart').html('<div class="alert alert-warning">正在尝试加载图表库...</div>');
                loadEChartsLibrary(function() {
                    // 库加载完成后重新初始化
                    initTrendChart();
                });
                return;
            }
            
            surveyTrendChart = echarts.init(chartDom);
            console.log('ECharts实例创建成功');
            loadSurveyTrendData(selectedDays);
        } catch (e) {
            console.error('初始化ECharts实例失败:', e);
            $('#blockchain-survey-trend-chart').html('<div class="alert alert-danger">初始化图表失败: ' + e.message + '</div>');
        }
    }
    
    // 加载ECharts库
    function loadEChartsLibrary(callback) {
        // 避免重复加载
        if (document.querySelector('script[src*="echarts"]')) {
            console.log('已存在ECharts脚本标签，尝试重新加载');
            // 移除旧的脚本标签重新加载
            var oldScript = document.querySelector('script[src*="echarts"]');
            if (oldScript) {
                oldScript.parentNode.removeChild(oldScript);
            }
        }
        
        console.log('动态加载ECharts库');
        var script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js';
        // 移除integrity属性，避免校验错误
        script.onload = function() {
            console.log('ECharts库成功加载');
            if (callback && typeof callback === 'function') {
                callback();
            }
        };
        script.onerror = function() {
            console.error('加载ECharts库失败');
            $('#blockchain-survey-trend-chart').html(
                '<div class="alert alert-danger">' +
                '<i class="fas fa-exclamation-triangle me-2"></i>加载图表库失败' +
                '<p class="small mt-2 mb-0">请检查您的网络连接并刷新页面</p>' +
                '</div>'
            );
        };
        document.head.appendChild(script);
    }
    
    // 检查ECharts库是否已加载
    function checkEChartsLoaded() {
        if (typeof echarts !== 'undefined') {
            console.log('ECharts库已加载，初始化图表');
            initTrendChart();
        } else {
            console.log('ECharts库未找到，尝试加载');
            $('#blockchain-survey-trend-chart').html('<div class="alert alert-warning">正在加载图表库...</div>');
            loadEChartsLibrary(function() {
                initTrendChart();
            });
        }
    }
    
    // 页面加载后延迟检查ECharts可用性
    setTimeout(checkEChartsLoaded, 300);
    
    // 加载趋势数据
    function loadSurveyTrendData(days) {
        if (!surveyTrendChart) {
            console.error('surveyTrendChart未初始化');
            return;
        }
        
        console.log('加载问卷趋势数据, 天数:', days);
        
        surveyTrendChart.showLoading({
            text: '加载区块链数据中...',
            color: '#3498db',
            textColor: '#000',
            maskColor: 'rgba(255, 255, 255, 0.8)',
            zlevel: 0
        });
        
        // 从API获取数据
        $.ajax({
            url: '/api/blockchain/stats/survey/{{ survey.id }}?days=' + days,
            type: 'GET',
            success: function(data) {
                console.log('API返回数据:', data);
                if (data.success && data.survey) {
                    renderTrendChart(data.survey, data.dates);
                } else {
                    surveyTrendChart.hideLoading();
                    $('#blockchain-survey-trend-chart').html(
                        '<div class="alert alert-info mt-3 mb-0">' +
                        '<i class="fas fa-info-circle me-2"></i>此问卷暂无区块链数据记录' +
                        '<p class="small text-muted mb-0 mt-2">可能的原因：问卷尚未收到回复，或回复未成功写入区块链</p>' +
                        '</div>'
                    );
                }
            },
            error: function(xhr, status, error) {
                console.error('API请求失败:', error, xhr.responseText);
                surveyTrendChart.hideLoading();
                $('#blockchain-survey-trend-chart').html(
                    '<div class="alert alert-danger mt-3 mb-0">' +
                    '<i class="fas fa-exclamation-triangle me-2"></i>加载区块链数据失败' +
                    '<p class="small text-muted mb-0 mt-2">错误信息: ' + (xhr.responseJSON?.message || error || '未知错误') + '</p>' +
                    '</div>'
                );
            }
        });
    }
    
    // 渲染趋势图表
    function renderTrendChart(surveyData, dates) {
        if (!surveyTrendChart) {
            console.error('无法渲染图表: surveyTrendChart未初始化');
            return;
        }
        
        console.log('渲染趋势图表, 数据:', surveyData);
        
        try {
            // 提取数据
            var dailyCounts = surveyData.time_series.map(function(item) {
                return item.count;
            });
            
            var cumulativeCounts = surveyData.time_series.map(function(item) {
                return item.cumulative;
            });
            
            var blockchainCounts = surveyData.time_series.map(function(item) {
                return item.blockchain_count || 0;
            });
            
            var blockchainCumulative = surveyData.time_series.map(function(item) {
                return item.blockchain_cumulative || 0;
            });
            
            var dateLabels = dates || surveyData.time_series.map(function(item) {
                return item.date;
            });
            
            // 图表配置
            var option = {
                title: {
                    text: '问卷填写区块链记录趋势 - ' + surveyData.title,
                    left: 'center',
                    top: 0,
                    textStyle: {
                        fontSize: 16
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        label: {
                            backgroundColor: '#6a7985'
                        }
                    },
                    formatter: function(params) {
                        var date = params[0].axisValue;
                        var result = '<div style="font-weight:bold;margin-bottom:5px;">' + date + '</div>';
                        
                        params.forEach(function(param) {
                            var color = param.color;
                            var seriesName = param.seriesName;
                            var value = param.value;
                            
                            result += '<div style="display:flex;align-items:center;margin:3px 0;">' +
                                     '<span style="display:inline-block;width:10px;height:10px;background:' + color + ';border-radius:50%;margin-right:5px;"></span>' +
                                     '<span style="margin-right:10px;color:#666;">' + seriesName + ':</span>' +
                                     '<span style="font-weight:bold;">' + value + ' 份</span>' +
                                     '</div>';
                                     
                            // 如果是当天有提交的问卷，显示区块链信息
                            if (seriesName === '区块链确认' && value > 0) {
                                result += '<div style="font-size:12px;color:#888;margin-left:15px;">' +
                                         '<i class="fas fa-cube" style="font-size:8px;"></i> 区块确认率: 100%' +
                                         '</div>';
                            }
                        });
                        
                        return result;
                    }
                },
                legend: {
                    data: ['每日填写量', '累计填写量', '区块链确认'],
                    top: 'bottom'
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '60px',
                    top: '60px',
                    containLabel: true
                },
                toolbox: {
                    feature: {
                        saveAsImage: { title: '保存为图片' },
                        dataZoom: { 
                            title: { zoom: '区域缩放', back: '区域缩放还原' },
                            yAxisIndex: 'none'
                        },
                        restore: { title: '还原' }
                    }
                },
                dataZoom: [
                    {
                        type: 'inside',
                        start: 0,
                        end: 100
                    },
                    {
                        type: 'slider',
                        start: 0,
                        end: 100,
                        height: 20,
                        bottom: 10
                    }
                ],
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: dateLabels,
                    axisLine: {
                        lineStyle: {
                            color: '#999'
                        }
                    },
                    axisLabel: {
                        formatter: function(value) {
                            // 只显示月份和日期
                            var date = new Date(value);
                            return (date.getMonth() + 1) + '/' + date.getDate();
                        },
                        interval: Math.ceil(dateLabels.length / 10)
                    }
                },
                yAxis: [
                    {
                        type: 'value',
                        name: '每日填写量',
                        position: 'left',
                        axisLine: {
                            lineStyle: {
                                color: '#e74c3c'
                            }
                        },
                        axisLabel: {
                            formatter: '{value} 份'
                        },
                        splitLine: {
                            lineStyle: {
                                type: 'dashed'
                            }
                        }
                    },
                    {
                        type: 'value',
                        name: '累计填写量',
                        position: 'right',
                        axisLine: {
                            lineStyle: {
                                color: '#3498db'
                            }
                        },
                        axisLabel: {
                            formatter: '{value} 份'
                        },
                        splitLine: {
                            show: false
                        }
                    }
                ],
                series: [
                    {
                        name: '每日填写量',
                        type: 'bar',
                        yAxisIndex: 0,
                        data: dailyCounts,
                        itemStyle: {
                            color: '#e74c3c'
                        },
                        emphasis: {
                            focus: 'series'
                        },
                        barMaxWidth: 40
                    },
                    {
                        name: '累计填写量',
                        type: 'line',
                        yAxisIndex: 1,
                        smooth: true,
                        symbol: 'circle',
                        symbolSize: 8,
                        lineStyle: {
                            width: 3,
                            color: '#3498db'
                        },
                        itemStyle: {
                            color: '#3498db',
                            borderWidth: 2
                        },
                        emphasis: {
                            focus: 'series'
                        },
                        data: cumulativeCounts,
                        markPoint: {
                            data: [
                                { type: 'max', name: '最大值' }
                            ]
                        },
                        markLine: {
                            data: [
                                { type: 'average', name: '平均值' }
                            ]
                        }
                    },
                    {
                        name: '区块链确认',
                        type: 'line',
                        yAxisIndex: 0,
                        smooth: true,
                        symbol: 'diamond',
                        symbolSize: 8,
                        itemStyle: {
                            color: '#2ecc71'
                        },
                        lineStyle: {
                            width: 3,
                            type: 'dashed',
                            color: '#2ecc71'
                        },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                offset: 0,
                                color: 'rgba(46, 204, 113, 0.3)'
                            }, {
                                offset: 1,
                                color: 'rgba(46, 204, 113, 0.1)'
                            }])
                        },
                        emphasis: {
                            focus: 'series'
                        },
                        data: blockchainCounts
                    }
                ]
            };
            
            surveyTrendChart.setOption(option);
            surveyTrendChart.hideLoading();
            
            console.log('图表渲染完成');
        } catch (e) {
            console.error('渲染图表错误:', e);
            surveyTrendChart.hideLoading();
            $('#blockchain-survey-trend-chart').html('<div class="alert alert-danger">渲染图表失败: ' + e.message + '</div>');
        }
        
        // 响应窗口大小变化
        window.addEventListener('resize', function() {
            if (surveyTrendChart) {
                surveyTrendChart.resize();
            }
        });
    }
    
    // 绑定日期范围切换按钮
    $('.trend-days').click(function() {
        $('.trend-days').removeClass('active');
        $(this).addClass('active');
        
        selectedDays = parseInt($(this).data('days'));
        console.log('切换到', selectedDays, '天数据');
        loadSurveyTrendData(selectedDays);
    });
});
</script>
{% endblock %} 