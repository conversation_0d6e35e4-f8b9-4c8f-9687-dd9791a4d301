#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
交互式创建管理员账号脚本
用法: python create_admin_interactive.py
"""

import os
import sys
import getpass
from app import create_app, db
from app.models.user import User
from datetime import datetime
import re

def is_valid_email(email):
    """检查邮箱格式是否有效"""
    pattern = r'^[\w\.-]+@[\w\.-]+\.\w+$'
    return re.match(pattern, email) is not None

def create_admin_user():
    """交互式创建管理员用户"""
    print("=" * 50)
    print("       基于区块链的应急管理问卷调查系统")
    print("             管理员账号创建工具")
    print("=" * 50)
    
    # 获取用户输入
    while True:
        username = input("请输入管理员用户名 (至少3个字符): ")
        if len(username) < 3:
            print("错误: 用户名至少需要3个字符")
            continue
            
        # 检查用户名是否存在
        existing_user = User.query.filter_by(username=username).first()
        if existing_user:
            print(f"错误: 用户名 '{username}' 已存在")
            continue
        
        break
    
    while True:
        email = input("请输入管理员邮箱: ")
        if not is_valid_email(email):
            print("错误: 邮箱格式无效")
            continue
            
        # 检查邮箱是否存在
        existing_email = User.query.filter_by(email=email).first()
        if existing_email:
            print(f"错误: 邮箱 '{email}' 已被注册")
            continue
            
        break
    
    while True:
        password = getpass.getpass("请输入管理员密码 (至少6个字符): ")
        if len(password) < 6:
            print("错误: 密码至少需要6个字符")
            continue
            
        password_confirm = getpass.getpass("请再次输入密码确认: ")
        if password != password_confirm:
            print("错误: 两次输入的密码不一致")
            continue
            
        break
    
    # 确认信息
    print("\n请确认以下信息:")
    print(f"用户名: {username}")
    print(f"邮箱: {email}")
    print(f"角色: 管理员")
    
    confirm = input("\n确认创建账号? (y/n): ")
    if confirm.lower() != 'y':
        print("操作已取消")
        return False
    
    # 创建用户
    try:
        new_admin = User(
            username=username,
            email=email,
            role='admin',
            created_at=datetime.utcnow(),
            is_active=True
        )
        new_admin.password = password  # 密码将通过setter方法自动加密
        
        db.session.add(new_admin)
        db.session.commit()
        print("\n✅ 成功: 管理员账号已创建!")
        print(f"\n可以使用以下信息登录系统:")
        print(f"用户名: {username}")
        print(f"邮箱: {email}")
        print(f"角色: 管理员")
        return True
    except Exception as e:
        db.session.rollback()
        print(f"\n❌ 错误: 创建用户时发生异常 - {str(e)}")
        return False

def main():
    """主函数"""
    # 创建Flask应用上下文
    app = create_app()
    with app.app_context():
        create_admin_user()

if __name__ == "__main__":
    main() 