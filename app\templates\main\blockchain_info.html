{% extends "base.html" %}

{% block title %}区块链数据可视化平台 - 基于区块链的应急管理问卷调查系统{% endblock %}

{% block extra_css %}
<!-- 引入 ECharts -->
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.2/dist/echarts.min.js"></script>
<!-- 引入ECharts GL库，用于3D可视化 -->
<script src="https://cdn.jsdelivr.net/npm/echarts-gl@2.0.9/dist/echarts-gl.min.js"></script>
<!-- 引入ECharts水球图插件 -->
<script src="https://cdn.jsdelivr.net/npm/echarts-liquidfill@3.1.0/dist/echarts-liquidfill.min.js"></script>
<!-- 引入应急管理图表 -->
<script src="{{ url_for('static', filename='js/emergency_charts.js') }}"></script>
<!-- 引入FontAwesome图标库 -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<!-- 自定义样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/blockchain.css') }}">
<style>
  :root {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --dark-color: #34495e;
    --light-color: #ecf0f1;
    --gray-color: #95a5a6;
  }
  
  .blockchain-header {
    background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
  
  .data-card {
    border: none;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  
  .data-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
  }
  
  .emergency-section {
    background-color: rgba(231, 76, 60, 0.05);
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }
  
  .stat-card {
    background: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    height: 100%;
  }
  
  .stat-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }
  
  .chart-container {
    height: 300px;
    width: 100%;
    min-height: 250px;
  }
  
  .question-card {
    background-color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }
  
  .survey-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    color: var(--gray-color);
    font-size: 0.9rem;
  }
  
  .option-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
  }
  
  .option-percent {
    width: 3rem;
    text-align: right;
    padding-right: 0.75rem;
    font-weight: bold;
  }
  
  .option-bar {
    flex-grow: 1;
    height: 1.5rem;
    background-color: var(--light-color);
    border-radius: 0.75rem;
    overflow: hidden;
    position: relative;
  }
  
  .option-progress {
    height: 100%;
    background-color: var(--primary-color);
    border-radius: 0.75rem;
    position: absolute;
    top: 0;
    left: 0;
  }
  
  .option-text {
    width: 5rem;
    padding-left: 0.75rem;
  }
  
  .blockchain-info {
    font-size: 0.85rem;
    color: var(--gray-color);
  }

  .emergency-card .card-body {
    padding: 0;
  }
  
  .emergency-list .list-group-item {
    border-left: none;
    border-right: none;
    transition: background-color 0.2s ease;
  }
  
  .emergency-list .list-group-item:hover {
    background-color: rgba(231, 76, 60, 0.05);
  }
  
  /* 确保所有卡片内部样式一致 */
  .card-body {
    padding: 1.5rem;
  }
  
  /* 添加浏览器前缀 */
  @media (max-width: 768px) {
    .chart-container {
      height: 250px;
    }
    
    .survey-meta {
      flex-direction: column;
      gap: 0.5rem;
    }
    
    #emergency-survey-trends-chart {
      height: 350px !important;
    }
    
    #survey-legend-container {
      flex-direction: column;
      margin-bottom: 1rem;
    }
    
    .btn-group {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 0.5rem;
    }
    
    .btn-group .btn {
      flex: 1;
      padding: 0.25rem 0.5rem;
      font-size: 0.75rem;
    }
  }
</style>
{% endblock %}

{% block content %}
<!-- 区块链头部 -->
<div class="blockchain-header py-5">
  <div class="container">
    <div class="row align-items-center">
      <div class="col-md-8">
        <h1 class="display-4 fw-bold mb-3">区块链数据可视化平台</h1>
        <p class="lead mb-4">基于区块链技术构建的分布式应急管理问卷调查系统</p>
        <div class="d-flex align-items-center">
          <div class="me-4">
            <div class="d-flex align-items-center">
              <div class="spinner-grow spinner-grow-sm text-success me-2" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
              <span>区块链状态： <strong class="text-white">运行中</strong></span>
            </div>
          </div>
          <div>
            <div class="d-flex align-items-center">
              <span class="badge bg-light text-dark">区块总数：{{ block_count }}</span>
              <span class="badge bg-light text-dark ms-2">节点数：8</span>
              <span class="badge bg-light text-dark ms-2">共识算法：PBFT</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="container py-5">
  <!-- 应急管理统计概览 -->
  <div class="emergency-section">
    <h2 class="emergency-title">应急管理问卷调查统计</h2>
    <div class="row">
      <div class="col-md-3 mb-4">
        <div class="blockchain-stat-card">
          <div class="blockchain-stat-value">125</div>
          <div class="blockchain-stat-label">应急管理问卷数</div>
          <div class="small text-white mt-2">
            <i class="fas fa-cubes"></i> 已上链记录数：125
          </div>
        </div>
      </div>
      <div class="col-md-3 mb-4">
        <div class="blockchain-stat-card">
          <div class="blockchain-stat-value">4582</div>
          <div class="blockchain-stat-label">有效回复数</div>
          <div class="small text-white mt-2">
            <i class="fas fa-link"></i> 已确认交易：4582
          </div>
        </div>
      </div>
      <div class="col-md-3 mb-4">
        <div class="blockchain-stat-card">
          <div class="blockchain-stat-value">68%</div>
          <div class="blockchain-stat-label">应急预案覆盖率</div>
          <div class="small text-white mt-2">
            <i class="fas fa-check-circle"></i> 区块验证率：100%
          </div>
        </div>
      </div>
      <div class="col-md-3 mb-4">
        <div class="blockchain-stat-card">
          <div class="blockchain-stat-value">15分钟</div>
          <div class="blockchain-stat-label">平均应急响应时间</div>
          <div class="small text-white mt-2">
            <i class="fas fa-clock"></i> 区块生成时间：5秒
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 应急管理问卷调查统计可视化图表 -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card data-card">
        <div class="card-header bg-danger text-white">
          <h3 class="mb-0">应急管理问卷填写趋势</h3>
        </div>
        <div class="card-body">
          <div class="mb-3">
            <div class="btn-group" role="group">
              <button type="button" class="btn btn-outline-secondary btn-sm" onclick="updateSurveyPeriod(7)">最近一周</button>
              <button type="button" class="btn btn-outline-secondary btn-sm active" onclick="updateSurveyPeriod(30)">最近一月</button>
              <button type="button" class="btn btn-outline-secondary btn-sm" onclick="updateSurveyPeriod(90)">最近三月</button>
              <button type="button" class="btn btn-outline-secondary btn-sm" onclick="updateSurveyPeriod(180)">最近半年</button>
            </div>
            <div class="form-check form-switch d-inline-block ms-3">
              <input class="form-check-input" type="checkbox" id="cumulativeSwitch" checked onchange="toggleCumulativeMode()">
              <label class="form-check-label" for="cumulativeSwitch">累计模式</label>
            </div>
          </div>
          <div class="chart-container" id="emergency-survey-trends-chart" style="height: 400px"></div>
          <div class="d-flex justify-content-between align-items-center flex-wrap mt-3">
            <div class="d-flex flex-wrap" id="survey-legend-container">
              <!-- 问卷图例将通过JS动态生成 -->
            </div>
            <div>
              <span class="d-inline-block me-3 small">
                <i class="fas fa-cube me-1 text-primary"></i> 总问卷数: <span id="total-surveys-count">0</span>
              </span>
              <span class="d-inline-block me-3 small">
                <i class="fas fa-chart-line me-1 text-success"></i> 总回复数: <span id="total-responses-count">0</span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 区块链摘要信息 -->
  <div class="row mb-5">
    <div class="col-md-3">
      <div class="card data-card h-100">
        <div class="survey-stat">
          <div class="stat-value" id="total-surveys">{{ survey_count }}</div>
          <div class="stat-label">问卷总数</div>
          <small class="text-muted d-block mt-2">区块链确认记录</small>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card data-card h-100">
        <div class="survey-stat">
          <div class="stat-value" id="total-responses">{{ responses }}</div>
          <div class="stat-label">总回复数</div>
          <small class="text-muted d-block mt-2">链上交易数量</small>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card data-card h-100">
        <div class="survey-stat">
          <div class="stat-value" id="active-surveys">{{ active_surveys }}</div>
          <div class="stat-label">进行中的问卷</div>
          <small class="text-muted d-block mt-2">活跃合约数</small>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card data-card h-100">
        <div class="survey-stat">
          <div class="stat-value">{{ block_count }}</div>
          <div class="stat-label">区块总数</div>
          <small class="text-muted d-block mt-2">已确认数据块</small>
        </div>
      </div>
    </div>
  </div>

  <!-- 区块链问卷交易可视化 -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card data-card">
        <div class="card-header bg-primary text-white">
          <h3 class="mb-0">区块链问卷交易可视化</h3>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-4">
              <div class="card mb-3">
                <div class="card-body text-center">
                  <h5 class="card-title">问卷链上交易分布</h5>
                  <div class="mt-3">
                    <canvas id="transaction-distribution" height="200"></canvas>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-8">
              <div class="card">
                <div class="card-body">
                  <h5 class="card-title">最新区块链交易记录</h5>
                  <div class="table-responsive">
                    <table class="table table-sm table-hover">
                      <thead>
                        <tr>
                          <th>区块高度</th>
                          <th>交易哈希</th>
                          <th>问卷ID</th>
                          <th>参与人数</th>
                          <th>时间戳</th>
                          <th>状态</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td><code>{{ block_count }}</code></td>
                          <td><code class="small">0x9f8e7d6c5b...</code></td>
                          <td>SURVEY-2023-09-15-001</td>
                          <td>328</td>
                          <td>2023-09-15 14:32:18</td>
                          <td><span class="badge bg-success">已确认</span></td>
                        </tr>
                        <tr>
                          <td><code>{{ block_count - 1 }}</code></td>
                          <td><code class="small">0x8e7d6c5b4a...</code></td>
                          <td>SURVEY-2023-09-14-008</td>
                          <td>215</td>
                          <td>2023-09-15 14:28:05</td>
                          <td><span class="badge bg-success">已确认</span></td>
                        </tr>
                        <tr>
                          <td><code>{{ block_count - 2 }}</code></td>
                          <td><code class="small">0x7d6c5b4a39...</code></td>
                          <td>SURVEY-2023-09-14-007</td>
                          <td>187</td>
                          <td>2023-09-15 14:22:51</td>
                          <td><span class="badge bg-success">已确认</span></td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 应急管理问卷展示 -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card data-card">
        <div class="card-header bg-danger text-white">
          <h3 class="mb-0">热门应急管理问卷</h3>
        </div>
        <div class="card-body">
          <!-- 问卷1 -->
          <div class="question-card">
            <div class="survey-meta mb-2">
              <span><i class="fas fa-calendar-alt"></i> 发布时间: 2023-06-15</span>
              <span><i class="fas fa-users"></i> 参与人数: 1245人</span>
              <span><i class="fas fa-clipboard-check"></i> 状态: <span class="badge bg-success">进行中</span></span>
            </div>
            <h4>自然灾害应急管理调查问卷</h4>
            <p class="text-muted">本问卷旨在收集公众对自然灾害应急管理的看法和建议，帮助改进应急预案制定。</p>
            <div class="d-flex mt-3">
              <span class="badge bg-danger me-2">自然灾害</span>
              <span class="badge bg-primary me-2">应急预案</span>
              <span class="badge bg-info me-2">公众参与</span>
            </div>
            <hr>
            <div class="question-content">
              <p><strong>Q1: 您所在地区最常见的自然灾害是什么？</strong></p>
              <div class="option-item">
                <div class="option-percent">52%</div>
                <div class="option-bar">
                  <div class="option-progress" style="width: 52%"></div>
                </div>
                <div class="option-text">洪水</div>
              </div>
              <div class="option-item">
                <div class="option-percent">28%</div>
                <div class="option-bar">
                  <div class="option-progress" style="width: 28%"></div>
                </div>
                <div class="option-text">地震</div>
              </div>
              <div class="option-item">
                <div class="option-percent">12%</div>
                <div class="option-bar">
                  <div class="option-progress" style="width: 12%"></div>
                </div>
                <div class="option-text">台风</div>
              </div>
              <div class="option-item">
                <div class="option-percent">8%</div>
                <div class="option-bar">
                  <div class="option-progress" style="width: 8%"></div>
                </div>
                <div class="option-text">其他</div>
              </div>
            </div>
            <div class="d-flex justify-content-between mt-3">
              <div class="blockchain-info">
                <i class="fas fa-cubes me-1"></i> 区块高度: <code>{{ block_count - 25 }}</code>
              </div>
              <div class="blockchain-info">
                <i class="fas fa-link me-1"></i> 交易数: <code>1245</code>
              </div>
              <div class="blockchain-info">
                <i class="fas fa-fingerprint me-1"></i> 区块链记录哈希: <code class="small">0x8f7e3b5c9a2d...</code>
              </div>
            </div>
          </div>
          
          <!-- 问卷2 -->
          <div class="question-card">
            <div class="survey-meta mb-2">
              <span><i class="fas fa-calendar-alt"></i> 发布时间: 2023-08-23</span>
              <span><i class="fas fa-users"></i> 参与人数: 873人</span>
              <span><i class="fas fa-clipboard-check"></i> 状态: <span class="badge bg-success">进行中</span></span>
            </div>
            <h4>公共卫生事件应急响应能力评估</h4>
            <p class="text-muted">评估当前公共卫生应急响应系统的效能，为改进工作提供数据支持。</p>
            <div class="d-flex mt-3">
              <span class="badge bg-primary me-2">公共卫生</span>
              <span class="badge bg-warning me-2">应急响应</span>
              <span class="badge bg-dark me-2">系统评估</span>
            </div>
            <hr>
            <div class="question-content">
              <p><strong>Q1: 您对当地医疗机构的应急响应速度满意吗？</strong></p>
              <div class="option-item">
                <div class="option-percent">18%</div>
                <div class="option-bar">
                  <div class="option-progress" style="width: 18%"></div>
                </div>
                <div class="option-text">非常满意</div>
              </div>
              <div class="option-item">
                <div class="option-percent">42%</div>
                <div class="option-bar">
                  <div class="option-progress" style="width: 42%"></div>
                </div>
                <div class="option-text">比较满意</div>
              </div>
              <div class="option-item">
                <div class="option-percent">25%</div>
                <div class="option-bar">
                  <div class="option-progress" style="width: 25%"></div>
                </div>
                <div class="option-text">一般</div>
              </div>
              <div class="option-item">
                <div class="option-percent">15%</div>
                <div class="option-bar">
                  <div class="option-progress" style="width: 15%"></div>
                </div>
                <div class="option-text">不太满意</div>
              </div>
            </div>
            <div class="d-flex justify-content-between mt-3">
              <div class="blockchain-info">
                <i class="fas fa-cubes me-1"></i> 区块高度: <code>{{ block_count - 42 }}</code>
              </div>
              <div class="blockchain-info">
                <i class="fas fa-link me-1"></i> 交易数: <code>873</code>
              </div>
              <div class="blockchain-info">
                <i class="fas fa-fingerprint me-1"></i> 区块链记录哈希: <code class="small">0x3c9d2f8a5b7e...</code>
              </div>
            </div>
          </div>
        </div>
        <div class="card-footer text-center">
          <button class="btn btn-outline-danger">查看更多应急管理问卷</button>
        </div>
      </div>
    </div>
  </div>

  <!-- 应急类型分布和应急响应时间 -->
  <div class="row mb-4">
    <div class="col-md-6">
      <div class="card data-card emergency-card">
        <div class="card-header bg-danger text-white">
          <h3 class="mb-0">应急管理类型分布</h3>
        </div>
        <div class="card-body p-0">
          <div class="chart-container" id="emergency-types-chart"></div>
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="card data-card emergency-card">
        <div class="card-header bg-danger text-white">
          <h3 class="mb-0">应急响应时间分布</h3>
        </div>
        <div class="card-body p-0">
          <div class="chart-container" id="emergency-response-chart"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- 区块链数据表格 -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card data-card">
        <div class="card-header bg-primary text-white">
          <h3 class="mb-0">应急管理问卷区块链数据</h3>
        </div>
        <div class="card-body">
                  <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>区块高度</th>
                  <th>时间戳</th>
                  <th>问卷类型</th>
                  <th>交易数</th>
                  <th>哈希值</th>
                        </tr>
                      </thead>
                      <tbody>
                <tr>
                  <td><strong>{{ block_count }}</strong></td>
                  <td>2023-09-15 14:32:18</td>
                  <td><span class="badge bg-danger">自然灾害</span></td>
                  <td>18</td>
                  <td><code class="small">0x9f8e7d6c5b...</code></td>
                            </tr>
                <tr>
                  <td><strong>{{ block_count - 1 }}</strong></td>
                  <td>2023-09-15 14:28:05</td>
                  <td><span class="badge bg-primary">公共卫生</span></td>
                  <td>12</td>
                  <td><code class="small">0x8e7d6c5b4a...</code></td>
                </tr>
                <tr>
                  <td><strong>{{ block_count - 2 }}</strong></td>
                  <td>2023-09-15 14:22:51</td>
                  <td><span class="badge bg-warning">事故灾难</span></td>
                  <td>9</td>
                  <td><code class="small">0x7d6c5b4a39...</code></td>
                </tr>
                <tr>
                  <td><strong>{{ block_count - 3 }}</strong></td>
                  <td>2023-09-15 14:18:36</td>
                  <td><span class="badge bg-info">社会安全</span></td>
                  <td>15</td>
                  <td><code class="small">0x6c5b4a3928...</code></td>
                </tr>
                <tr>
                  <td><strong>{{ block_count - 4 }}</strong></td>
                  <td>2023-09-15 14:15:02</td>
                  <td><span class="badge bg-danger">自然灾害</span></td>
                  <td>11</td>
                  <td><code class="small">0x5b4a392817...</code></td>
                </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
  <!-- 问卷参与度趋势图 -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card data-card">
        <div class="card-header bg-primary text-white">
          <h3 class="mb-0">问卷参与度趋势</h3>
                        </div>
                        <div class="card-body p-0">
          <div class="chart-container" id="participation-trend-chart"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                      
  <!-- 问卷类型分布和响应时间分布 -->
              <div class="row mb-4">
                <div class="col-md-6">
      <div class="card data-card">
        <div class="card-header bg-primary text-white">
          <h3 class="mb-0">问卷类型分布</h3>
                    </div>
        <div class="card-body p-0">
          <div class="chart-container" id="survey-types-chart"></div>
                    </div>
                </div>
              </div>
              <div class="col-md-6">
      <div class="card data-card">
        <div class="card-header bg-primary text-white">
          <h3 class="mb-0">响应时间分布</h3>
                    </div>
        <div class="card-body p-0">
          <div class="chart-container" id="time-distribution-chart"></div>
                        </div>
                </div>
              </div>
  </div>

  <!-- 区块链应急管理数据安全性 -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card data-card">
        <div class="card-header bg-primary text-white">
          <h3 class="mb-0">区块链应急管理数据安全性</h3>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-4 text-center mb-3">
              <div class="display-6 text-success">100%</div>
              <p class="text-muted">数据完整性</p>
              <div class="progress">
                <div class="progress-bar bg-success" role="progressbar" style="width: 100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
              </div>
            </div>
            <div class="col-md-4 text-center mb-3">
              <div class="display-6 text-primary">95%</div>
              <p class="text-muted">数据不可篡改性</p>
              <div class="progress">
                <div class="progress-bar" role="progressbar" style="width: 95%" aria-valuenow="95" aria-valuemin="0" aria-valuemax="100"></div>
              </div>
            </div>
            <div class="col-md-4 text-center mb-3">
              <div class="display-6 text-info">97%</div>
              <p class="text-muted">数据可追溯性</p>
              <div class="progress">
                <div class="progress-bar bg-info" role="progressbar" style="width: 97%" aria-valuenow="97" aria-valuemin="0" aria-valuemax="100"></div>
              </div>
            </div>
          </div>
          <hr class="my-4">
          <div class="table-responsive">
            <table class="table table-sm">
              <thead>
                <tr>
                  <th>安全特性</th>
                  <th>评估方法</th>
                  <th>满足度</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>区块链数据可信度</td>
                  <td>共识机制验证</td>
                  <td><span class="badge bg-success">高</span></td>
                </tr>
                <tr>
                  <td>应急数据隐私保护</td>
                  <td>零知识证明</td>
                  <td><span class="badge bg-success">高</span></td>
                </tr>
                <tr>
                  <td>权限管理</td>
                  <td>多签名验证</td>
                  <td><span class="badge bg-primary">中高</span></td>
                </tr>
                <tr>
                  <td>数据恢复能力</td>
                  <td>分布式存储冗余</td>
                  <td><span class="badge bg-primary">中高</span></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 应急管理问卷其他图表 -->
  <div class="row mb-4">
    <div class="col-md-6">
      <div class="card data-card emergency-card">
        <div class="card-header bg-danger text-white">
          <h3 class="mb-0">应急管理问卷区域分布</h3>
        </div>
        <div class="card-body p-0">
          <div class="chart-container" id="survey-region-chart"></div>
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="card data-card emergency-card">
        <div class="card-header bg-danger text-white">
          <h3 class="mb-0">问卷完成率对比</h3>
        </div>
        <div class="card-body p-0">
          <div class="chart-container" id="survey-completion-chart"></div>
                        </div>
                </div>
              </div>
            </div>
              
  <!-- 最近响应记录 -->
  <div class="row">
    <div class="col-12">
      <div class="card data-card">
        <div class="card-header bg-primary text-white">
          <h3 class="mb-0">最近应急问卷响应记录</h3>
          </div>
                    <div class="card-body">
          <div class="list-group emergency-list">
            {% for response in recent_responses %}
            <div class="list-group-item">
              <div class="d-flex w-100 justify-content-between">
                <h5 class="mb-1">{{ response.survey_title }}</h5>
                <small class="text-muted">{{ response.submitted_at }}</small>
                    </div>
              <p class="mb-1">提交者: {{ response.user }}</p>
              {% if response.blockchain_hash %}
              <small class="text-muted">区块链交易哈希: {{ response.blockchain_hash[:20] }}...</small>
              {% endif %}
              <div class="mt-2">
                <span class="badge bg-danger me-2">应急管理</span>
                <span class="badge bg-warning me-2">高优先级</span>
              </div>
                  </div>
            {% endfor %}
                </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
<script>
  /* 参与度趋势图 */
  function createParticipationTrendChart() {
  const chartDom = document.getElementById('participation-trend-chart');
  const myChart = echarts.init(chartDom);
  
    const trendDates = {{ trend_dates|safe }};
    const trendCounts = {{ trend_counts|safe }};
    
  const option = {
    tooltip: {
        trigger: 'axis'
    },
    xAxis: {
      type: 'category',
        data: trendDates,
        axisLabel: {
          rotate: 45
        }
    },
    yAxis: {
        type: 'value',
        name: '问卷响应数'
    },
    series: [
      {
          name: '问卷响应数',
        type: 'line',
          smooth: true,
        data: trendCounts,
          areaStyle: {}
      }
    ]
  };
  
  myChart.setOption(option);
  window.addEventListener('resize', function() {
    myChart.resize();
  });
}

  /* 问卷类型分布图 */
  function createSurveyTypesChart() {
    const chartDom = document.getElementById('survey-types-chart');
  const myChart = echarts.init(chartDom);
  
    const surveyTypes = {{ survey_types|safe }};
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '问卷类型',
        type: 'pie',
          radius: ['50%', '70%'],
        data: surveyTypes
      }
    ]
  };
  
  myChart.setOption(option);
  window.addEventListener('resize', function() {
    myChart.resize();
  });
}

  /* 响应时间分布图 */
  function createTimeDistributionChart() {
    const chartDom = document.getElementById('time-distribution-chart');
  const myChart = echarts.init(chartDom);
  
    const timeSlots = {{ time_slots|safe }};
    const timeDistribution = {{ time_distribution|safe }};
  
  const option = {
    tooltip: {
        trigger: 'axis'
    },
    xAxis: {
      type: 'category',
        data: timeSlots
    },
    yAxis: {
      type: 'value',
      name: '响应数量'
    },
    series: [
      {
      name: '响应数量',
      type: 'bar',
          data: timeDistribution
      }
    ]
  };
  
  myChart.setOption(option);
  window.addEventListener('resize', function() {
    myChart.resize();
    });
  }
  
  /* 问卷交易分布图 */
  function createTransactionDistributionChart() {
    const ctx = document.getElementById('transaction-distribution');
    if (!ctx) return;
    
    new Chart(ctx, {
      type: 'doughnut',
      data: {
        labels: ['自然灾害', '事故灾难', '公共卫生', '社会安全', '其他'],
        datasets: [{
          data: [35, 28, 22, 12, 3],
          backgroundColor: [
            '#e74c3c',
            '#f39c12',
            '#3498db',
            '#2ecc71',
            '#9b59b6'
          ],
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        plugins: {
          legend: {
            position: 'bottom'
          },
          title: {
            display: true,
            text: '问卷类型交易分布'
          }
        }
      }
    });
  }
  
  /* 页面加载完成后初始化图表 */
  document.addEventListener('DOMContentLoaded', function() {
    createParticipationTrendChart();
    createSurveyTypesChart();
    createTimeDistributionChart();
    createTransactionDistributionChart();
    createSurveyTrendsChart(30); // 默认显示30天的数据
    /* 应急管理图表由外部JS文件初始化 */
  });
  
  /* 问卷填写趋势图 */
  let surveyTrendsChart = null;
  let surveyData = [];
  let isCumulativeMode = true;
  
  function updateSurveyPeriod(days) {
    // 更新按钮样式
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
      btn.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // 重新加载图表
    createSurveyTrendsChart(days);
  }
  
  function toggleCumulativeMode() {
    isCumulativeMode = document.getElementById('cumulativeSwitch').checked;
    updateSurveyTrendsChart();
  }
  
  function createSurveyTrendsChart(days) {
    // 显示加载动画
    const chartDom = document.getElementById('emergency-survey-trends-chart');
    if (!chartDom) return;
    
    // 如果图表已存在，先销毁
    if (surveyTrendsChart) {
      surveyTrendsChart.dispose();
    }
    
    // 初始化图表
    surveyTrendsChart = echarts.init(chartDom);
    surveyTrendsChart.showLoading({
      text: '加载数据中...',
      color: '#e74c3c',
      textColor: '#000',
      maskColor: 'rgba(255, 255, 255, 0.8)',
      zlevel: 0
    });
    
    // 从API获取数据
    fetch(`/api/blockchain/stats/surveys?days=${days}`)
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          surveyData = data.surveys;
          updateSurveyTrendsChart();
          updateSurveyStats();
        } else {
          console.error('获取问卷数据失败');
          surveyTrendsChart.hideLoading();
        }
      })
      .catch(error => {
        console.error('API请求错误:', error);
        surveyTrendsChart.hideLoading();
      });
  }
  
  function updateSurveyTrendsChart() {
    if (!surveyTrendsChart || !surveyData.length) return;
    
    // 准备图表数据
    const series = [];
    const legendData = [];
    const colors = [
      '#e74c3c', '#3498db', '#2ecc71', '#f39c12', '#9b59b6', 
      '#1abc9c', '#34495e', '#e67e22', '#16a085', '#c0392b',
      '#8e44ad', '#27ae60', '#d35400', '#2980b9', '#7f8c8d'
    ];
    
    // 获取日期列表（所有问卷共用同一个时间轴）
    const allDates = surveyData.length > 0 ? 
      surveyData[0].time_series.map(item => item.date) : [];
    
    // 为每个问卷创建一个折线图序列
    surveyData.forEach((survey, index) => {
      const color = colors[index % colors.length];
      legendData.push(survey.title);
      
      // 准备数据点
      const dataPoints = survey.time_series.map(item => 
        isCumulativeMode ? item.cumulative : item.count
      );
      
      series.push({
        name: survey.title,
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        sampling: 'average',
        itemStyle: {
          color: color,
          borderWidth: 2
        },
        lineStyle: {
          width: 3,
          shadowColor: 'rgba(0,0,0,0.3)',
          shadowBlur: 10
        },
        areaStyle: {
          opacity: 0.15,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
            offset: 0,
            color: color
          }, {
            offset: 1,
            color: 'rgba(255, 255, 255, 0)'
          }])
        },
        data: dataPoints,
        markPoint: {
          data: [
            { type: 'max', name: '最大值' }
          ]
        }
      });
    });
    
    // 设置图表选项
    const option = {
      title: {
        text: isCumulativeMode ? '问卷填写累计趋势' : '问卷每日填写趋势',
        left: 'center',
        top: 0
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        },
        formatter: function(params) {
          let result = `<div style="font-weight:bold;margin-bottom:5px;">${params[0].axisValue}</div>`;
          
          // 按值从大到小排序
          params.sort((a, b) => b.value - a.value);
          
          params.forEach(param => {
            const survey = surveyData.find(s => s.title === param.seriesName);
            const color = param.color;
            
            result += `<div style="display:flex;align-items:center;margin:3px 0;">
              <span style="display:inline-block;width:10px;height:10px;background:${color};border-radius:50%;margin-right:5px;"></span>
              <span style="margin-right:10px;color:#666;">${param.seriesName}:</span>
              <span style="font-weight:bold;">${param.value} 份</span>
            </div>`;
            
            // 如果是当天有提交的问卷，显示区块链信息
            if (!isCumulativeMode && param.value > 0) {
              result += `<div style="font-size:12px;color:#888;margin-left:15px;">
                <i class="fas fa-cube" style="font-size:8px;"></i> 区块确认: 100%
              </div>`;
            }
          });
          return result;
        }
      },
      legend: {
        show: false // 不显示默认图例，使用自定义图例
      },
      // 添加工具箱组件
      toolbox: {
        feature: {
          saveAsImage: { title: '保存为图片' },
          restore: { title: '还原' },
          dataZoom: { 
            title: { zoom: '区域缩放', back: '区域缩放还原' },
            yAxisIndex: 'none'
          },
          dataView: { title: '数据视图', readOnly: true }
        }
      },
      // 添加数据缩放组件
      dataZoom: [
        {
          type: 'inside', // 支持鼠标滚轮或触摸板缩放
          start: 0,
          end: 100
        },
        {
          type: 'slider', // 底部滑动条
          show: true,
          start: 0,
          end: 100,
          height: 20
        }
      ],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%', // 增加底部空间放置缩放控件
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: allDates,
        axisLine: {
          lineStyle: {
            color: '#999'
          }
        },
        axisLabel: {
          formatter: function(value) {
            // 只显示月份和日期
            const date = new Date(value);
            return `${date.getMonth() + 1}/${date.getDate()}`;
          },
          interval: Math.floor(allDates.length / 10) // 根据日期数量动态调整间隔
        }
      },
      yAxis: {
        type: 'value',
        name: '问卷数量',
        min: 0,
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: 'rgba(0,0,0,0.1)'
          }
        },
        axisLabel: {
          formatter: '{value} 份'
        }
      },
      series: series
    };
    
    // 更新图表
    surveyTrendsChart.hideLoading();
    surveyTrendsChart.setOption(option);
    
    // 创建自定义图例
    updateCustomLegend(legendData, colors);
    
    // 响应窗口大小变化
    window.addEventListener('resize', function() {
      surveyTrendsChart.resize();
    });
  }
  
  function updateCustomLegend(legendData, colors) {
    const container = document.getElementById('survey-legend-container');
    if (!container) return;
    
    // 清空现有图例
    container.innerHTML = '';
    
    // 创建每个问卷的图例项
    legendData.forEach((title, index) => {
      const color = colors[index % colors.length];
      const survey = surveyData[index];
      const totalResponses = survey.total_responses;
      
      const legendItem = document.createElement('div');
      legendItem.className = 'me-3 mb-2';
      legendItem.innerHTML = `
        <div class="d-flex align-items-center">
          <span class="d-inline-block me-1" style="width:12px;height:12px;background:${color};border-radius:50%;"></span>
          <span class="small">${title}: <strong>${totalResponses}</strong>份</span>
          <div class="form-check form-switch ms-2" style="margin-top:-3px;">
            <input class="form-check-input" type="checkbox" id="legend-${index}" checked 
              onchange="toggleSurveyLine(${index}, this.checked)">
          </div>
        </div>
      `;
      
      container.appendChild(legendItem);
    });
  }
  
  function toggleSurveyLine(index, visible) {
    if (!surveyTrendsChart) return;
    
    surveyTrendsChart.dispatchAction({
      type: visible ? 'legendSelect' : 'legendUnSelect',
      name: surveyData[index].title
    });
    
    // 修改图表中的系列可见性
    const option = surveyTrendsChart.getOption();
    if (option.series[index]) {
      option.series[index].lineStyle = {
        ...option.series[index].lineStyle,
        opacity: visible ? 1 : 0
      };
      option.series[index].areaStyle = {
        ...option.series[index].areaStyle,
        opacity: visible ? 0.15 : 0
      };
      option.series[index].itemStyle = {
        ...option.series[index].itemStyle,
        opacity: visible ? 1 : 0
      };
    }
    
    surveyTrendsChart.setOption(option);
  }
  
  function updateSurveyStats() {
    if (!surveyData.length) return;
    
    // 更新统计信息
    const totalSurveysCount = surveyData.length;
    const totalResponsesCount = surveyData.reduce((sum, survey) => sum + survey.total_responses, 0);
    
    document.getElementById('total-surveys-count').textContent = totalSurveysCount;
    document.getElementById('total-responses-count').textContent = totalResponsesCount;
  }
</script>
{% endblock %} 