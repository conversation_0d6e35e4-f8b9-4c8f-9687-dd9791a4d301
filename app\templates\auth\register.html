{% extends "base.html" %}

{% block title %}注册 - 基于区块链的应急管理问卷调查系统{% endblock %}

{% block extra_css %}
<style>
    .register-card {
        max-width: 500px;
        margin: 50px auto;
        border-radius: 10px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    .register-header {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 10px 10px 0 0;
        text-align: center;
    }
    .register-body {
        padding: 30px;
    }
    .form-floating {
        margin-bottom: 20px;
    }
    .register-footer {
        text-align: center;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 0 0 10px 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="register-card">
            <div class="register-header">
                <h2><i class="fas fa-user-plus me-2"></i>用户注册</h2>
                <p class="text-muted">创建一个新账号以使用系统功能</p>
            </div>
            <div class="register-body">
                <form method="POST" action="{{ url_for('auth.register') }}">
                    <div class="form-floating">
                        <input type="text" class="form-control" id="username" name="username" placeholder="用户名" required>
                        <label for="username">用户名</label>
                    </div>
                    <div class="form-floating">
                        <input type="email" class="form-control" id="email" name="email" placeholder="邮箱地址" required>
                        <label for="email">邮箱地址</label>
                    </div>
                    <div class="form-floating">
                        <input type="password" class="form-control" id="password" name="password" placeholder="密码" required>
                        <label for="password">密码</label>
                    </div>
                    <div class="form-floating">
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" placeholder="确认密码" required>
                        <label for="confirm_password">确认密码</label>
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-success btn-lg">注册</button>
                    </div>
                </form>
            </div>
            <div class="register-footer">
                <p>已有账号？ <a href="{{ url_for('auth.login') }}">立即登录</a></p>
            </div>
        </div>
    </div>
</div>
{% endblock %} 