#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
生成模拟数据脚本
生成大量模拟数据用于测试区块链数据可视化功能
"""

import sys
import os
import random
import hashlib
import string
import json
from datetime import datetime, timedelta
import traceback

# 确保能够导入Flask应用
sys.path.append(os.path.abspath(os.path.dirname(__file__)))
from app import db, create_app, bcrypt
from app.models.user import User
from app.models.survey import Survey, Question, Option, Response, Answer
from app.models.blockchain import Block, Transaction
from app.blockchain.chain import get_blockchain, get_chain, add_transaction
from app.blockchain.crypto import encrypt_data

# 配置变量
NUM_USERS = 100  # 用户数量
NUM_SURVEYS = 50  # 问卷数量
MIN_QUESTIONS_PER_SURVEY = 5  # 每个问卷最少问题数
MAX_QUESTIONS_PER_SURVEY = 15  # 每个问卷最多问题数
MIN_OPTIONS_PER_QUESTION = 2  # 每个选择题最少选项数
MAX_OPTIONS_PER_QUESTION = 6  # 每个选择题最多选项数
NUM_RESPONSES = 5000  # 问卷回复数量

# 创建Flask应用
app = create_app()

# 问卷类型列表
SURVEY_TYPES = [
    '应急管理', '满意度调查', '信息收集', '意见反馈', '其他'
]

# 问卷标题模板
SURVEY_TITLES = [
    # 应急管理
    "突发公共卫生事件应急准备调查",
    "地震灾害应急响应能力评估",
    "洪水灾害风险感知调查",
    "社区防灾减灾意识调查",
    "企业应急管理体系评估",
    "学校应急演练效果调查",
    "应急物资储备现状调查",
    "疫情防控措施满意度调查",
    "灾害预警系统评估调查",
    "应急避难场所设施调查",
    
    # 满意度调查
    "政府部门服务满意度调查",
    "公共交通服务满意度评价",
    "医疗服务质量满意度调查",
    "社区服务满意度评估",
    "教育服务满意度调查",
    "环境卫生满意度调查",
    "公共安全满意度评价",
    "水电气暖服务满意度调查",
    "市政设施维护满意度调查",
    "网络服务质量满意度调查",
    
    # 信息收集
    "居民健康状况信息收集",
    "社区人口结构调查",
    "受灾情况信息收集",
    "居民出行方式调查",
    "能源使用习惯调查",
    "垃圾分类情况调查",
    "居民消费习惯调查",
    "网络使用习惯调查",
    "社区安全隐患信息收集",
    "居民健康码使用情况调查",
    
    # 意见反馈
    "城市规划意见征集",
    "公共服务改进建议收集",
    "社区建设意见反馈",
    "疫情防控措施意见收集",
    "政策实施效果反馈",
    "环境保护建议征集",
    "交通改善意见收集",
    "教育资源分配意见反馈",
    "医疗服务改进建议",
    "社区活动意见收集",
    
    # 其他
    "居民阅读习惯调查",
    "体育锻炼习惯调查",
    "互联网使用行为调查",
    "社区文化活动参与度调查",
    "生活方式与健康关系调查",
    "老年人生活状况调查",
    "青少年心理健康调查",
    "社区邻里关系调查",
    "居民环保意识调查",
    "社区志愿服务参与意愿调查"
]

# 问题模板
QUESTION_TEMPLATES = {
    'single_choice': [
        "您对{topic}的满意度如何？",
        "您认为{topic}的重要性是？",
        "您多久会参与一次{topic}？",
        "您对{topic}的了解程度是？",
        "您如何评价当前的{topic}？",
        "{topic}对您的影响程度是？",
        "您是否支持{topic}相关措施？",
        "您更倾向于哪种{topic}方式？",
        "您认为{topic}的主要问题是什么？",
        "您对{topic}的期望是？"
    ],
    'multiple_choice': [
        "您认为{topic}应该包含哪些方面？（可多选）",
        "您希望通过哪些渠道了解{topic}信息？（可多选）",
        "您认为{topic}需要改进的地方有哪些？（可多选）",
        "在{topic}方面，您最关心哪些问题？（可多选）",
        "您认为提高{topic}效果的方法有哪些？（可多选）",
        "您愿意通过哪些方式参与{topic}？（可多选）",
        "您认为{topic}应该优先考虑哪些因素？（可多选）",
        "您获取{topic}相关信息的途径有哪些？（可多选）",
        "您遇到过{topic}方面的哪些困难？（可多选）",
        "在{topic}中，您最重视哪些方面？（可多选）"
    ],
    'text': [
        "请详细描述您对{topic}的看法。",
        "您对改进{topic}有哪些具体建议？",
        "请分享您在{topic}方面的经验或故事。",
        "您认为{topic}面临的最大挑战是什么？请详述。",
        "请提出您对{topic}的意见和建议。",
        "您对{topic}的未来发展有什么期待？",
        "请描述您理想中的{topic}是什么样子。",
        "您认为当前{topic}存在哪些问题？请详述。",
        "您如何看待{topic}的发展趋势？",
        "请分享您对{topic}的任何其他想法。"
    ]
}

# 选项模板
OPTION_TEMPLATES = {
    '满意度': ["非常满意", "比较满意", "一般", "不太满意", "非常不满意"],
    '重要性': ["非常重要", "比较重要", "一般", "不太重要", "完全不重要"],
    '频率': ["每天", "每周", "每月", "每季度", "每年", "从不"],
    '了解程度': ["非常了解", "比较了解", "一般了解", "不太了解", "完全不了解"],
    '评价': ["非常好", "较好", "一般", "较差", "非常差"],
    '影响程度': ["影响很大", "有一定影响", "影响一般", "影响较小", "没有影响"],
    '是否支持': ["非常支持", "比较支持", "中立", "不太支持", "坚决反对"],
    '问题': ["资源不足", "管理不善", "技术落后", "信息不透明", "缺乏协调", "其他"],
    '期望': ["提高效率", "增加资源", "加强管理", "改进技术", "加强宣传", "其他"],
    '渠道': ["官方网站", "社交媒体", "电视广播", "报纸杂志", "社区宣传", "手机应用", "短信通知", "亲友推荐"],
    '参与方式': ["现场参与", "网络参与", "电话参与", "填写问卷", "参加讨论", "志愿服务"],
    '方式': ["线上", "线下", "混合模式", "自助服务", "人工服务"],
    '因素': ["安全性", "便捷性", "经济性", "时效性", "公平性", "透明度", "专业性"],
    '途径': ["互联网", "电视", "广播", "报纸", "社区公告", "政府通知", "亲友告知", "学校/单位通知"],
    '困难': ["信息不足", "操作复杂", "时间冲突", "资源匮乏", "技术障碍", "沟通不畅"],
    '重视方面': ["质量", "效率", "成本", "安全", "创新", "服务", "环保", "社会责任"]
}

# 常用话题
TOPICS = [
    "应急管理", "灾害防范", "社区安全", "公共服务", "医疗卫生", 
    "教育资源", "环境保护", "交通出行", "社区建设", "政务服务",
    "垃圾分类", "节能减排", "文化活动", "体育锻炼", "老年服务",
    "青少年教育", "社区关系", "志愿服务", "网络安全", "信息技术"
]

def generate_hash(data):
    """生成SHA-256哈希值"""
    if isinstance(data, str):
        data = data.encode('utf-8')
    elif not isinstance(data, bytes):
        data = str(data).encode('utf-8')
    return hashlib.sha256(data).hexdigest()

def random_string(length=10):
    """生成随机字符串"""
    letters = string.ascii_letters + string.digits
    return ''.join(random.choice(letters) for _ in range(length))

def create_users():
    """创建模拟用户"""
    print("创建模拟用户...")
    
    # 确保有一个管理员账户
    admin = User.query.filter_by(email='<EMAIL>').first()
    if not admin:
        admin = User(
            username='admin',
            email='<EMAIL>',
            role='admin'
        )
        admin.password = 'admin123'
        db.session.add(admin)
        db.session.commit()
        print("创建管理员用户: admin")
    
    users = []
    for i in range(NUM_USERS):
        username = f"user{i+1}"
        email = f"user{i+1}@example.com"
        
        # 检查用户是否已存在（同时检查用户名和邮箱）
        if User.query.filter_by(username=username).first() or User.query.filter_by(email=email).first():
            print(f"用户 {username} 或邮箱 {email} 已存在，跳过...")
            continue
            
        user = User(
            username=username,
            email=email,
            role='user'
        )
        user.password = f"password{i+1}"
        users.append(user)
        
    db.session.add_all(users)
    db.session.commit()
    print(f"创建了 {len(users)} 个普通用户")
    
    # 返回所有用户，包括已存在的用户
    return [admin] + users + list(User.query.filter(User.username != 'admin', User.id != admin.id).all())

def create_surveys(users):
    """创建模拟问卷"""
    print("创建模拟问卷...")
    surveys = []
    
    for i in range(NUM_SURVEYS):
        # 随机选择一个创建者
        creator = random.choice(users)
        
        # 随机选择过期日期（部分问卷已过期，部分仍在进行中）
        days_range = random.randint(-30, 60)  # -30表示过期30天，60表示60天后过期
        expiry_date = datetime.utcnow() + timedelta(days=days_range)
        
        # 随机选择创建日期（在过去90天内）
        created_delta = random.randint(1, 90)
        created_at = datetime.utcnow() - timedelta(days=created_delta)
        
        # 随机选择问卷标题
        title = random.choice(SURVEY_TITLES)
        
        # 确定问卷类型
        survey_type = ""
        if "应急" in title or "灾害" in title or "防灾" in title:
            survey_type = "应急管理"
        elif "满意度" in title or "评价" in title:
            survey_type = "满意度调查"
        elif "信息" in title or "收集" in title:
            survey_type = "信息收集"
        elif "意见" in title or "建议" in title or "反馈" in title:
            survey_type = "意见反馈"
        else:
            survey_type = "其他"
        
        # 生成描述
        description = f"这是一份关于{title.replace('调查', '').replace('评估', '')}的{survey_type}问卷。" \
                     f"本问卷由{creator.username}创建，用于收集相关信息和意见。" \
                     f"感谢您的参与！"
        
        # 创建问卷
        survey = Survey(
            title=title,
            description=description,
            created_at=created_at,
            expiry_date=expiry_date,
            is_active=days_range > 0,  # 如果过期日期在未来，则问卷处于活动状态
            is_anonymous=random.choice([True, False]),
            creator_id=creator.id
        )
        
        surveys.append(survey)
    
    db.session.add_all(surveys)
    db.session.commit()
    print(f"创建了 {len(surveys)} 份问卷")
    
    # 创建问题和选项
    for survey in surveys:
        create_questions(survey)
    
    return surveys

def create_questions(survey):
    """为问卷创建问题和选项"""
    # 提取调查主题
    title_parts = survey.title.replace("调查", "").replace("评估", "").split()
    topic = random.choice(title_parts) if title_parts else random.choice(TOPICS)
    
    # 决定问题数量
    num_questions = random.randint(MIN_QUESTIONS_PER_SURVEY, MAX_QUESTIONS_PER_SURVEY)
    
    questions = []
    for i in range(num_questions):
        # 随机选择问题类型
        question_type = random.choice(['single_choice', 'multiple_choice', 'text'])
        
        # 随机选择问题模板并填充
        template = random.choice(QUESTION_TEMPLATES[question_type])
        content = template.format(topic=topic)
        
        # 创建问题
        question = Question(
            survey_id=survey.id,
            content=content,
            type=question_type,
            required=random.choice([True, False]),
            order=i+1
        )
        
        questions.append(question)
    
    db.session.add_all(questions)
    db.session.commit()
    
    # 为选择题创建选项
    for question in questions:
        if question.type in ['single_choice', 'multiple_choice']:
            create_options(question)

def create_options(question):
    """为问题创建选项"""
    # 确定选项类型
    content_lower = question.content.lower()
    option_type = None
    
    for key in OPTION_TEMPLATES.keys():
        if key in content_lower:
            option_type = key
            break
    
    if not option_type:
        option_type = random.choice(list(OPTION_TEMPLATES.keys()))
    
    # 获取选项列表
    option_list = OPTION_TEMPLATES[option_type]
    
    # 决定选项数量
    num_options = min(len(option_list), random.randint(MIN_OPTIONS_PER_QUESTION, MAX_OPTIONS_PER_QUESTION))
    
    # 随机选择选项
    selected_options = random.sample(option_list, num_options)
    
    options = []
    for i, content in enumerate(selected_options):
        option = Option(
            question_id=question.id,
            content=content,
            order=i+1
        )
        options.append(option)
    
    db.session.add_all(options)
    db.session.commit()

def create_blockchain():
    """初始化或检查区块链"""
    print("初始化区块链...")
    
    # 检查是否已存在区块链
    blocks = Block.query.all()
    if not blocks:
        # 创建创世区块 - 使用get_blockchain()获取区块链实例，它会自动创建创世区块
        blockchain = get_blockchain()
        genesis_block = Block.query.first()  # 获取第一个区块，即创世区块
        if genesis_block:
            print(f"创建创世区块: {genesis_block.hash}")
    else:
        print(f"区块链已存在，当前区块数: {len(blocks)}")

def create_responses(users, surveys):
    """创建问卷回复和区块链数据"""
    print("创建问卷回复和区块链数据...")
    
    # 获取区块链实例
    blockchain = get_blockchain()
    
    # 计算日期分布权重 - 使最近日期的回复更多
    date_weights = []
    for i in range(30):
        # 越近的日期权重越大
        weight = (i + 1) ** 2
        date_weights.append(weight)
    
    responses_created = 0
    transactions_created = 0
    blocks_mined = 0
    
    # 创建问卷回复
    for _ in range(NUM_RESPONSES):
        try:
            # 随机选择用户和问卷
            user = random.choice(users)
            survey = random.choice(surveys)
            
            # 决定提交日期（偏向最近日期）
            days_ago = random.choices(range(30), weights=date_weights, k=1)[0]
            date = datetime.utcnow() - timedelta(days=days_ago)
            
            # 如果问卷在这个日期之后创建，则使用问卷创建日期之后的日期
            if date < survey.created_at:
                date = survey.created_at + timedelta(hours=random.randint(1, 48))
            
            # 如果问卷在这个日期之前过期，则使用过期日期之前的日期
            if survey.expiry_date and date > survey.expiry_date:
                # 如果问卷已过期
                if survey.expiry_date < datetime.utcnow():
                    date = survey.expiry_date - timedelta(hours=random.randint(1, 24))
                else:
                    # 如果问卷未过期，则使用当前日期
                    date = datetime.utcnow() - timedelta(hours=random.randint(0, 72))
            
            # 创建回复
            response = Response(
                survey_id=survey.id,
                user_id=user.id if not survey.is_anonymous else None,
                submitted_at=date,
                ip_address=f"192.168.1.{random.randint(1, 255)}"
            )
            
            db.session.add(response)
            db.session.flush()  # 获取ID但不提交
            
            # 获取问卷的所有问题
            questions = Question.query.filter_by(survey_id=survey.id).all()
            
            # 创建回答
            for question in questions:
                answer = Answer(
                    response_id=response.id,
                    question_id=question.id
                )
                
                if question.type == 'text':
                    # 文本回答
                    answer.text_answer = f"这是对问题「{question.content}」的回答。" \
                                         f"回答者是{user.username}，提交时间是{date.strftime('%Y-%m-%d %H:%M:%S')}。"
                else:
                    # 选择题回答
                    options = Option.query.filter_by(question_id=question.id).all()
                    if options:
                        if question.type == 'single_choice':
                            # 单选题
                            option_id = random.choice(options).id
                            answer.set_selected_options([option_id])
                        else:
                            # 多选题
                            num_selections = random.randint(1, len(options))
                            option_ids = [option.id for option in random.sample(options, num_selections)]
                            answer.set_selected_options(option_ids)
                
                db.session.add(answer)
            
            # 提交回复和回答
            db.session.commit()
            responses_created += 1
            
            # 将回复添加到区块链
            response_data = {
                "response_id": response.id,
                "survey_id": survey.id,
                "user_id": response.user_id,
                "timestamp": response.submitted_at.isoformat(),
                "answers": []
            }
            
            # 加密数据
            secret_key = random_string(32)  # 生成随机密钥
            encrypted_data = encrypt_data(json.dumps(response_data), secret_key)
            
            # 创建交易
            transaction_hash = generate_hash(encrypted_data + str(datetime.utcnow().timestamp()))
            transaction = Transaction(
                hash=transaction_hash,
                data=str(response.id),  # 存储响应ID
                timestamp=date - timedelta(minutes=random.randint(1, 60))
            )
            
            db.session.add(transaction)
            db.session.commit()
            transactions_created += 1
            
            # 每10个交易打包一个新区块
            if transactions_created % 10 == 0:
                # 使用区块链实例的方法来挖掘新区块
                blockchain.mine()
                blocks_mined += 1
            
            # 定期报告进度
            if responses_created % 100 == 0:
                print(f"已创建 {responses_created} 个回复, {transactions_created} 个交易, {blocks_mined} 个区块")
                
        except Exception as e:
            db.session.rollback()
            print(f"创建回复时出错: {e}")
            traceback.print_exc()
    
    print(f"总共创建了 {responses_created} 个回复, {transactions_created} 个交易, {blocks_mined} 个区块")

def cleanup_database():
    """清理数据库（谨慎使用）"""
    print("警告: 此操作将清空数据库中的所有数据!")
    confirm = input("确定要继续吗? (y/n): ")
    
    if confirm.lower() != 'y':
        print("操作已取消")
        return
    
    print("清理数据库...")
    
    try:
        # 删除所有非管理员用户
        User.query.filter(User.username != 'admin').delete()
        
        # 删除所有问卷和相关数据
        Survey.query.delete()
        
        # 删除所有区块链数据
        Block.query.delete()
        Transaction.query.delete()
        
        db.session.commit()
        print("数据库已清空")
    except Exception as e:
        db.session.rollback()
        print(f"清理数据库时出错: {e}")

def main():
    """主函数"""
    with app.app_context():
        try:
            # 询问是否清理数据库
            should_cleanup = input("是否要清空数据库重新生成数据? (y/n, 默认n): ")
            if should_cleanup.lower() == 'y':
                cleanup_database()
            
            # 初始化区块链
            create_blockchain()
            
            # 创建用户
            users = create_users()
            
            # 创建问卷
            surveys = create_surveys(users)
            
            # 创建回复和区块链数据
            create_responses(users, surveys)
            
            print("模拟数据生成完成!")
            
        except Exception as e:
            print(f"生成模拟数据时出错: {e}")
            traceback.print_exc()

if __name__ == "__main__":
    main() 