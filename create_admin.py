#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
创建管理员账号脚本
用法: python create_admin.py username email password
"""

import sys
import os
from app import create_app, db
from app.models.user import User
from datetime import datetime

def create_admin_user(username, email, password):
    """创建管理员用户"""
    # 检查用户名和邮箱是否已存在
    existing_user_by_name = User.query.filter_by(username=username).first()
    if existing_user_by_name:
        print(f"错误: 用户名 '{username}' 已存在")
        return False
        
    existing_user_by_email = User.query.filter_by(email=email).first()
    if existing_user_by_email:
        print(f"错误: 邮箱 '{email}' 已被注册")
        return False
    
    # 创建新管理员用户
    new_admin = User(
        username=username,
        email=email,
        role='admin',
        created_at=datetime.utcnow(),
        is_active=True
    )
    new_admin.password = password  # 密码将通过setter方法自动加密
    
    try:
        db.session.add(new_admin)
        db.session.commit()
        print(f"成功: 管理员用户 '{username}' 已创建!")
        print(f"用户名: {username}")
        print(f"邮箱: {email}")
        print(f"角色: 管理员")
        return True
    except Exception as e:
        db.session.rollback()
        print(f"错误: 创建用户时发生异常 - {str(e)}")
        return False

def main():
    """主函数"""
    # 检查参数
    if len(sys.argv) != 4:
        print("用法: python create_admin.py <用户名> <邮箱> <密码>")
        print("示例: python create_admin.<NAME_EMAIL> admin_password")
        sys.exit(1)
    
    username = sys.argv[1]
    email = sys.argv[2]
    password = sys.argv[3]
    
    # 参数基本验证
    if len(username) < 3:
        print("错误: 用户名至少需要3个字符")
        sys.exit(1)
    if '@' not in email or '.' not in email:
        print("错误: 邮箱格式无效")
        sys.exit(1)
    if len(password) < 6:
        print("错误: 密码至少需要6个字符")
        sys.exit(1)
    
    # 创建Flask应用上下文
    app = create_app()
    with app.app_context():
        if create_admin_user(username, email, password):
            print("可以使用以下信息登录系统:")
            print(f"用户名: {username}")
            print(f"密码: {password}")
            sys.exit(0)
        else:
            sys.exit(1)

if __name__ == "__main__":
    main() 