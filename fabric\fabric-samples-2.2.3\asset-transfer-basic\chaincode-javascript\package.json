{"name": "asset-transfer-basic", "version": "1.0.0", "description": "Asset-Transfer-Basic contract implemented in JavaScript", "main": "index.js", "engines": {"node": ">=12", "npm": ">=5"}, "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "nyc mocha --recursive", "start": "fabric-chaincode-node start"}, "engineStrict": true, "author": "Hyperledger", "license": "Apache-2.0", "dependencies": {"fabric-contract-api": "^2.0.0", "fabric-shim": "^2.0.0"}, "devDependencies": {"chai": "^4.1.2", "eslint": "^4.19.1", "mocha": "^8.0.1", "nyc": "^14.1.1", "sinon": "^6.0.0", "sinon-chai": "^3.2.0"}, "nyc": {"exclude": ["coverage/**", "test/**", "index.js", ".eslintrc.js"], "reporter": ["text-summary", "html"], "all": true, "check-coverage": true, "statements": 100, "branches": 100, "functions": 100, "lines": 100}}