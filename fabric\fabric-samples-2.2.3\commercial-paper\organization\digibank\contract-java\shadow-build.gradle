/*
 * SPDX-License-Identifier: Apache-2.0
 */
plugins {
    id 'com.github.johnrengelman.shadow' version '5.1.0'
    id 'java'
}


version '0.0.1'

sourceCompatibility = 1.8

repositories {
    mavenLocal()
    mavenCentral()
    maven {
        url 'https://jitpack.io'
    }
}

dependencies {
    implementation group: 'org.hyperledger.fabric-chaincode-java', name: 'fabric-chaincode-shim', version: '2.+'
    implementation group: 'org.json', name: 'json', version: '20180813'
    testImplementation 'org.junit.jupiter:junit-jupiter:5.4.2'
    testImplementation 'org.assertj:assertj-core:3.11.1'
    testImplementation 'org.mockito:mockito-core:2.+'
}

test {
    useJUnitPlatform()
    testLogging {
        events "passed", "skipped", "failed"
    }
}

shadowJar {
    baseName = 'chaincode'
    version = null
    classifier = null

    manifest {
        attributes 'Main-Class': 'org.hyperledger.fabric.contract.ContractRouter'
    }
}


tasks.withType(JavaCompile) {
  options.compilerArgs << "-Xlint:unchecked" << "-Xlint:deprecation" << "-parameters"
}
