{% extends "base.html" %}

{% block title %}编辑用户 - 基于区块链的应急管理问卷调查系统{% endblock %}

{% block extra_css %}
<style>
    .user-edit-card {
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border-radius: 5px;
    }
    .user-avatar {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        background-color: #eee;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        color: #666;
        margin: 0 auto 1rem;
    }
    .form-section {
        margin-bottom: 2rem;
    }
    .form-section-title {
        border-bottom: 1px solid #eee;
        padding-bottom: 0.5rem;
        margin-bottom: 1rem;
    }
    .user-status-toggle .form-check-input:checked {
        background-color: #28a745;
        border-color: #28a745;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('auth.admin_users') }}">用户管理</a></li>
                    <li class="breadcrumb-item active" aria-current="page">编辑用户</li>
                </ol>
            </nav>
            <h2><i class="fas fa-user-edit me-2"></i>编辑用户</h2>
            <p class="text-muted">修改用户 <strong>{{ user.username }}</strong> 的信息</p>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4 mb-4">
            <div class="card user-edit-card">
                <div class="card-body text-center">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <h5 class="card-title">{{ user.username }}</h5>
                    <p class="card-text">{{ user.email }}</p>
                    
                    <p class="mb-1">
                        {% if user.role == 'admin' %}
                        <span class="badge bg-danger">管理员</span>
                        {% elif user.role == 'staff' %}
                        <span class="badge bg-warning">员工</span>
                        {% else %}
                        <span class="badge bg-secondary">普通用户</span>
                        {% endif %}
                    </p>
                    
                    <p class="mb-1">
                        {% if user.is_active %}
                        <span class="badge bg-success">激活</span>
                        {% else %}
                        <span class="badge bg-danger">未激活</span>
                        {% endif %}
                    </p>
                    
                    <p class="text-muted small">
                        注册时间: {{ user.created_at.strftime('%Y-%m-%d %H:%M') }}
                    </p>
                </div>
            </div>
        </div>
        
        <div class="col-md-8">
            <div class="card user-edit-card">
                <div class="card-body">
                    <form action="{{ url_for('auth.admin_user_edit', user_id=user.id) }}" method="POST">
                        <!-- 基本信息 -->
                        <div class="form-section">
                            <h5 class="form-section-title">基本信息</h5>
                            <div class="mb-3">
                                <label for="username" class="form-label">用户名</label>
                                <input type="text" class="form-control" id="username" name="username" value="{{ user.username }}" required>
                            </div>
                            <div class="mb-3">
                                <label for="email" class="form-label">邮箱</label>
                                <input type="email" class="form-control" id="email" name="email" value="{{ user.email }}" required>
                            </div>
                        </div>
                        
                        <!-- 密码修改 -->
                        <div class="form-section">
                            <h5 class="form-section-title">密码修改 <small class="text-muted">(留空表示不修改)</small></h5>
                            <div class="mb-3">
                                <label for="password" class="form-label">新密码</label>
                                <input type="password" class="form-control" id="password" name="password">
                                <div class="form-text">如不修改密码请留空</div>
                            </div>
                            <div class="mb-3">
                                <label for="password_confirm" class="form-label">确认新密码</label>
                                <input type="password" class="form-control" id="password_confirm" name="password_confirm">
                            </div>
                        </div>
                        
                        <!-- 权限设置 -->
                        <div class="form-section">
                            <h5 class="form-section-title">权限设置</h5>
                            <div class="mb-3">
                                <label for="role" class="form-label">用户角色</label>
                                <select class="form-select" id="role" name="role">
                                    <option value="user" {% if user.role == 'user' %}selected{% endif %}>普通用户</option>
                                    <option value="staff" {% if user.role == 'staff' %}selected{% endif %}>员工</option>
                                    <option value="admin" {% if user.role == 'admin' %}selected{% endif %}>管理员</option>
                                </select>
                            </div>
                            
                            <div class="form-check form-switch user-status-toggle mb-3">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" {% if user.is_active %}checked{% endif %}>
                                <label class="form-check-label" for="is_active">
                                    账户激活
                                </label>
                            </div>
                        </div>
                        
                        <!-- 提交按钮 -->
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('auth.admin_users') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-1"></i>返回
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>保存更改
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // 密码确认验证
        $('form').on('submit', function(e) {
            const password = $('#password').val();
            const passwordConfirm = $('#password_confirm').val();
            
            if (password && password !== passwordConfirm) {
                e.preventDefault();
                alert('两次输入的密码不一致，请重新输入');
                return false;
            }
        });
    });
</script>
{% endblock %} 