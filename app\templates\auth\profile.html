{% extends "base.html" %}

{% block title %}个人资料 - 基于区块链的应急管理问卷调查系统{% endblock %}

{% block extra_css %}
<style>
    .profile-card {
        max-width: 700px;
        margin: 50px auto;
        border-radius: 10px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    .profile-header {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 10px 10px 0 0;
        text-align: center;
    }
    .profile-body {
        padding: 30px;
    }
    .form-floating {
        margin-bottom: 20px;
    }
    .profile-footer {
        text-align: center;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 0 0 10px 10px;
    }
    .profile-stats {
        display: flex;
        justify-content: space-around;
        margin-bottom: 20px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 10px;
    }
    .stat-item {
        text-align: center;
    }
    .stat-number {
        font-size: 1.5rem;
        font-weight: bold;
        color: #3498db;
    }
    .stat-label {
        font-size: 0.9rem;
        color: #7f8c8d;
    }
    .section-title {
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="profile-card">
            <div class="profile-header">
                <h2><i class="fas fa-user-circle me-2"></i>个人资料</h2>
                <p class="text-muted">查看和修改您的账户信息</p>
            </div>
            
            <div class="profile-stats">
                <div class="stat-item">
                    <div class="stat-number">{{ current_user.surveys.count() }}</div>
                    <div class="stat-label">创建的问卷</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ current_user.responses.count() }}</div>
                    <div class="stat-label">填写的问卷</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ current_user.created_at.strftime('%Y-%m-%d') }}</div>
                    <div class="stat-label">注册日期</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ current_user.last_login.strftime('%Y-%m-%d %H:%M') if current_user.last_login else '未记录' }}</div>
                    <div class="stat-label">上次登录</div>
                </div>
            </div>
            
            <div class="profile-body">
                <h4 class="section-title">账户信息</h4>
                <form method="POST" action="{{ url_for('auth.profile') }}">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="username" name="username" value="{{ current_user.username }}" placeholder="用户名" required>
                                <label for="username">用户名</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="email" class="form-control" value="{{ current_user.email }}" disabled placeholder="邮箱地址">
                                <label>邮箱地址 (不可修改)</label>
                            </div>
                        </div>
                    </div>
                    
                    <h4 class="section-title mt-4">修改密码</h4>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-floating">
                                <input type="password" class="form-control" id="current_password" name="current_password" placeholder="当前密码">
                                <label for="current_password">当前密码</label>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="password" class="form-control" id="new_password" name="new_password" placeholder="新密码">
                                <label for="new_password">新密码</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" placeholder="确认新密码">
                                <label for="confirm_password">确认新密码</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle me-2"></i>如果不需要修改密码，请将密码字段留空
                    </div>
                    
                    <div class="d-grid mt-4">
                        <button type="submit" class="btn btn-primary btn-lg">保存修改</button>
                    </div>
                </form>
            </div>
            
            <div class="profile-footer">
                <div class="row">
                    <div class="col-md-6 offset-md-3">
                        <div class="d-grid">
                            <a href="{{ url_for('main.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>返回首页
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 