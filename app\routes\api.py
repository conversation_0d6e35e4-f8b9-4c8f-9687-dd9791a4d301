from flask import Blueprint, jsonify, request
from flask_login import current_user, login_required
from datetime import datetime, timedelta
import json

from app import db
from app.models.survey import Survey, Question, Option, Response, Answer
from app.models.blockchain import Block, Transaction
from app.utils.helpers import generate_survey_stats
from app.blockchain.chain import get_chain, get_transaction_data

api_bp = Blueprint('api', __name__)

@api_bp.route('/surveys', methods=['GET'])
def get_surveys():
    """获取问卷列表API"""
    active_only = request.args.get('active', 'true').lower() == 'true'
    
    query = Survey.query
    if active_only:
        query = query.filter_by(is_active=True)
    
    surveys = query.order_by(Survey.created_at.desc()).all()
    return jsonify({
        'success': True,
        'surveys': [survey.to_dict() for survey in surveys]
    })

@api_bp.route('/survey/<int:survey_id>', methods=['GET'])
def get_survey(survey_id):
    """获取问卷详情API"""
    survey = Survey.query.get_or_404(survey_id)
    
    # 获取问题
    questions = Question.query.filter_by(survey_id=survey_id).order_by(Question.order).all()
    questions_data = [question.to_dict() for question in questions]
    
    survey_data = survey.to_dict()
    survey_data['questions'] = questions_data
    
    return jsonify({
        'success': True,
        'survey': survey_data
    })

@api_bp.route('/survey/<int:survey_id>/submit', methods=['POST'])
def submit_survey(survey_id):
    """提交问卷回答API"""
    survey = Survey.query.get_or_404(survey_id)
    
    # 检查问卷是否已过期或不活跃
    if not survey.is_active or (survey.expiry_date and survey.is_expired()):
        return jsonify({
            'success': False,
            'message': '此问卷已不可用'
        }), 400
    
    # 如果要求登录但用户未登录
    if not survey.is_anonymous and not current_user.is_authenticated:
        return jsonify({
            'success': False,
            'message': '请先登录后再回答此问卷'
        }), 401
    
    # 如果用户已经回答过此问卷，不允许再次填写
    if current_user.is_authenticated and Response.query.filter_by(
        survey_id=survey_id, user_id=current_user.id).first():
        return jsonify({
            'success': False,
            'message': '您已经回答过此问卷'
        }), 400
    
    # 获取回答数据
    answers_data = request.json.get('answers', {})
    
    # 创建回应
    response = Response(
        survey_id=survey_id,
        user_id=current_user.id if current_user.is_authenticated else None,
        submitted_at=datetime.utcnow(),
        ip_address=request.remote_addr
    )
    
    db.session.add(response)
    db.session.commit()
    
    # 处理每个问题的答案
    questions = Question.query.filter_by(survey_id=survey_id).all()
    for question in questions:
        if str(question.id) in answers_data:
            answer = Answer(
                response_id=response.id,
                question_id=question.id
            )
            
            answer_data = answers_data[str(question.id)]
            
            if question.type == 'text':
                answer.text_answer = answer_data
            elif question.type == 'single_choice':
                answer.set_selected_options(answer_data)
            elif question.type == 'multiple_choice':
                answer.set_selected_options(answer_data)
            
            db.session.add(answer)
    
    db.session.commit()
    
    # 将回答数据保存到区块链
    blockchain_hash = response.save_to_blockchain(survey.title + str(survey.id))
    
    return jsonify({
        'success': True,
        'message': '问卷提交成功',
        'response_id': response.id,
        'blockchain_hash': blockchain_hash
    })

@api_bp.route('/survey/<int:survey_id>/results', methods=['GET'])
def get_survey_results(survey_id):
    """获取问卷结果API"""
    survey = Survey.query.get_or_404(survey_id)
    
    # 如果问卷不是匿名的，检查权限
    if not survey.is_anonymous:
        if not current_user.is_authenticated or (
            survey.creator_id != current_user.id and 
            not current_user.is_admin() and
            not Response.query.filter_by(survey_id=survey_id, user_id=current_user.id).first()
        ):
            return jsonify({
                'success': False,
                'message': '您没有权限查看此问卷的结果'
            }), 403
    
    # 生成统计信息
    stats = generate_survey_stats(survey_id)
    
    return jsonify({
        'success': True,
        'stats': stats
    })

@api_bp.route('/blockchain/info', methods=['GET'])
def blockchain_info():
    """获取区块链信息API"""
    blocks = get_chain()
    
    return jsonify({
        'success': True,
        'block_count': len(blocks),
        'blocks': [block.to_dict() for block in blocks]
    })

@api_bp.route('/blockchain/transaction/<transaction_hash>', methods=['GET'])
@login_required
def transaction_info(transaction_hash):
    """获取交易信息API"""
    # 检查是否是管理员
    if not current_user.is_admin():
        return jsonify({
            'success': False,
            'message': '您没有权限查看交易信息'
        }), 403
    
    transaction = Transaction.query.filter_by(hash=transaction_hash).first()
    if not transaction:
        return jsonify({
            'success': False,
            'message': '交易不存在'
        }), 404
    
    encrypted_data = transaction.data
    
    return jsonify({
        'success': True,
        'transaction': transaction.to_dict(),
        'encrypted_data': encrypted_data
    })

@api_bp.route('/user/surveys', methods=['GET'])
@login_required
def user_surveys():
    """获取用户创建的问卷API"""
    surveys = Survey.query.filter_by(creator_id=current_user.id).order_by(Survey.created_at.desc()).all()
    
    return jsonify({
        'success': True,
        'surveys': [survey.to_dict() for survey in surveys]
    })

@api_bp.route('/user/responses', methods=['GET'])
@login_required
def user_responses():
    """获取用户回答的问卷API"""
    responses = Response.query.filter_by(user_id=current_user.id).order_by(Response.submitted_at.desc()).all()
    
    responses_data = []
    for response in responses:
        survey = Survey.query.get(response.survey_id)
        if survey:
            responses_data.append({
                'response_id': response.id,
                'survey_id': survey.id,
                'survey_title': survey.title,
                'submitted_at': response.submitted_at.isoformat(),
                'blockchain_hash': response.blockchain_hash
            })
    
    return jsonify({
        'success': True,
        'responses': responses_data
    })

@api_bp.route('/blockchain/stats/surveys', methods=['GET'])
def get_survey_stats_over_time():
    """获取所有问卷随时间变化的填写数据"""
    # 获取参数
    days = request.args.get('days', 30, type=int)
    
    # 计算开始日期
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=days)
    
    # 获取所有活跃问卷
    surveys = Survey.query.filter_by(is_active=True).all()
    survey_data = []
    
    # 为每个问卷计算回复数量随时间的变化
    for survey in surveys:
        # 查询这个问卷在指定时间段内的所有回复
        responses = Response.query.filter(
            Response.survey_id == survey.id,
            Response.submitted_at >= start_date,
            Response.submitted_at <= end_date
        ).order_by(Response.submitted_at).all()
        
        # 生成时间序列数据
        date_counts = {}
        for response in responses:
            # 将日期格式化为YYYY-MM-DD
            date_str = response.submitted_at.strftime('%Y-%m-%d')
            
            if date_str in date_counts:
                date_counts[date_str] += 1
            else:
                date_counts[date_str] = 1
        
        # 确保每一天都有数据点
        current_date = start_date
        time_series = []
        cumulative_count = 0
        
        while current_date <= end_date:
            date_str = current_date.strftime('%Y-%m-%d')
            
            # 如果这一天有数据，则累加
            if date_str in date_counts:
                cumulative_count += date_counts[date_str]
            
            time_series.append({
                'date': date_str,
                'count': date_counts.get(date_str, 0),
                'cumulative': cumulative_count
            })
            
            current_date += timedelta(days=1)
        
        # 收集该问卷的数据
        survey_data.append({
            'id': survey.id,
            'title': survey.title,
            'description': survey.description,
            'created_at': survey.created_at.strftime('%Y-%m-%d'),
            'total_responses': survey.response_count(),
            'time_series': time_series
        })
    
    return jsonify({
        'success': True,
        'surveys': survey_data,
        'dates': [item['date'] for item in time_series] if time_series else []
    })

@api_bp.route('/blockchain/stats/survey/<int:survey_id>', methods=['GET'])
def get_single_survey_stats_over_time(survey_id):
    """获取单个问卷随时间变化的填写数据"""
    try:
        # 获取参数
        days = request.args.get('days', 30, type=int)
        
        # 计算开始日期
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # 获取指定问卷
        survey = Survey.query.get_or_404(survey_id)
        
        # 查询这个问卷在指定时间段内的所有回复
        responses = Response.query.filter(
            Response.survey_id == survey_id,
            Response.submitted_at >= start_date,
            Response.submitted_at <= end_date
        ).order_by(Response.submitted_at).all()
        
        # 如果没有回复，返回空数据但不是错误
        if not responses:
            # 生成空的时间序列
            time_series = []
            current_date = start_date
            
            while current_date <= end_date:
                time_series.append({
                    'date': current_date.strftime('%Y-%m-%d'),
                    'count': 0,
                    'cumulative': 0,
                    'blockchain_count': 0,
                    'blockchain_cumulative': 0
                })
                current_date += timedelta(days=1)
            
            survey_data = {
                'id': survey.id,
                'title': survey.title,
                'description': survey.description,
                'created_at': survey.created_at.strftime('%Y-%m-%d'),
                'total_responses': 0,
                'time_series': time_series
            }
            
            return jsonify({
                'success': True,
                'survey': survey_data,
                'dates': [item['date'] for item in time_series]
            })
        
        # 生成时间序列数据
        date_counts = {}
        blockchain_counts = {}
        for response in responses:
            # 将日期格式化为YYYY-MM-DD
            date_str = response.submitted_at.strftime('%Y-%m-%d')
            
            # 记录总回复数
            if date_str in date_counts:
                date_counts[date_str] += 1
            else:
                date_counts[date_str] = 1
            
            # 记录区块链确认的回复数
            if response.blockchain_hash:
                if date_str in blockchain_counts:
                    blockchain_counts[date_str] += 1
                else:
                    blockchain_counts[date_str] = 1
        
        # 确保每一天都有数据点
        current_date = start_date
        time_series = []
        cumulative_count = 0
        blockchain_cumulative = 0
        
        while current_date <= end_date:
            date_str = current_date.strftime('%Y-%m-%d')
            
            # 如果这一天有数据，则累加
            daily_count = date_counts.get(date_str, 0)
            blockchain_daily = blockchain_counts.get(date_str, 0)
            
            cumulative_count += daily_count
            blockchain_cumulative += blockchain_daily
            
            time_series.append({
                'date': date_str,
                'count': daily_count,
                'cumulative': cumulative_count,
                'blockchain_count': blockchain_daily,
                'blockchain_cumulative': blockchain_cumulative
            })
            
            current_date += timedelta(days=1)
        
        # 收集该问卷的数据
        survey_data = {
            'id': survey.id,
            'title': survey.title,
            'description': survey.description,
            'created_at': survey.created_at.strftime('%Y-%m-%d'),
            'total_responses': survey.response_count(),
            'time_series': time_series
        }
        
        return jsonify({
            'success': True,
            'survey': survey_data,
            'dates': [item['date'] for item in time_series]
        })
    except Exception as e:
        # 捕获任何错误并返回友好的错误信息
        return jsonify({
            'success': False,
            'message': f'获取问卷统计数据错误: {str(e)}',
            'error': str(e)
        }), 500 