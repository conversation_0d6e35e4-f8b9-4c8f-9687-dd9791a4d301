#
# SPDX-License-Identifier: Apache-2.0
#

steps:
  - task: NodeTool@0
    inputs:
      versionSpec: $(NODE_VER)
    displayName: Install Node.js
  - script: curl -L --retry 5 --retry-delay 3 https://hyperledger.jfrog.io/hyperledger/fabric-binaries/hyperledger-fabric-linux-amd64-${FABRIC_VERSION}-stable.tar.gz | tar xz
    displayName: Download Fabric CLI
  - script: curl -L --retry 5 --retry-delay 3 https://hyperledger.jfrog.io/hyperledger/fabric-binaries/hyperledger-fabric-ca-linux-amd64-${FABRIC_VERSION}-stable.tar.gz | tar xz
    displayName: Download Fabric CA CLI
  - script: curl https://raw.githubusercontent.com/kadwanev/retry/master/retry -o ./bin/retry && chmod +x ./bin/retry
    displayName: Install retry CLI
  - script: ./ci/scripts/pullFabricImages.sh
    displayName: Pull Fabric Docker Imagess