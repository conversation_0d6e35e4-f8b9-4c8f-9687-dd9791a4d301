#
# SPDX-License-Identifier: Apache-2.0
#

trigger:
  - master
  - release-1.4

variables:
  FABRIC_VERSION: 2.2
  GO_BIN: $(Build.Repository.LocalPath)/bin
  GO_VER: 1.14.6
  NODE_VER: 12.x
  PATH: $(Build.Repository.LocalPath)/bin:/bin:/usr/bin:/sbin:/usr/sbin:/usr/local/bin:/usr/local/sbin

jobs:
  - job: CommercialPaper_Go
    displayName: Commercial Paper (Go)
    pool:
      vmImage: ubuntu-18.04
    steps:
      - template: templates/install-deps.yml
      - template: templates/commercial-paper/azure-pipelines-go.yml

  - job: CommercialPaper_Java
    displayName: Commercial Paper (Java)
    pool:
      vmImage: ubuntu-18.04
    steps:
      - template: templates/install-deps.yml
      - template: templates/commercial-paper/azure-pipelines-java.yml

  - job: CommercialPaper_JavaScript
    displayName: Commercial Paper (JavaScript)
    pool:
      vmImage: ubuntu-18.04
    steps:
      - template: templates/install-deps.yml
      - template: templates/commercial-paper/azure-pipelines-javascript.yml

  - job: FabCar_Go
    displayName: FabCar (Go)
    pool:
      vmImage: ubuntu-18.04
    steps:
      - template: templates/install-deps.yml
      - template: templates/fabcar/azure-pipelines-go.yml

  - job: FabCar_Java
    displayName: FabCar (Java)
    pool:
      vmImage: ubuntu-18.04
    steps:
      - template: templates/install-deps.yml
      - template: templates/fabcar/azure-pipelines-java.yml

  - job: FabCar_JavaScript
    displayName: FabCar (JavaScript)
    pool:
      vmImage: ubuntu-18.04
    steps:
      - template: templates/install-deps.yml
      - template: templates/fabcar/azure-pipelines-javascript.yml

  - job: Fabcar_TypeScript
    displayName: FabCar (TypeScript)
    pool:
      vmImage: ubuntu-18.04
    steps:
      - template: templates/install-deps.yml
      - template: templates/fabcar/azure-pipelines-typescript.yml

  - job: Lint
    displayName: Lint
    pool:
      vmImage: ubuntu-18.04
    strategy:
      matrix:
        Basic-Application-Go:
          DIRECTORY: asset-transfer-basic
          LANGUAGE: go
          TYPE: application
        Basic-Application-Java:
          DIRECTORY: asset-transfer-basic
          LANGUAGE: java
          TYPE: application
        Basic-Application-Javascript:
          DIRECTORY: asset-transfer-basic
          LANGUAGE: javascript
          TYPE: application
        Basic-Chaincode-Go:
          DIRECTORY: asset-transfer-basic
          LANGUAGE: go
          TYPE: chaincode
        Basic-Chaincode-Java:
          DIRECTORY: asset-transfer-basic
          LANGUAGE: java
          TYPE: chaincode
        Basic-Chaincode-Javascript:
          DIRECTORY: asset-transfer-basic
          LANGUAGE: javascript
          TYPE: chaincode
        Basic-Chaincode-Typescript:
          DIRECTORY: asset-transfer-basic
          LANGUAGE: typescript
          TYPE: chaincode
        Ledger-Application-Java:
          DIRECTORY: asset-transfer-ledger-queries
          LANGUAGE: java
          TYPE: application
        Ledger-Chaincode-Go:
          DIRECTORY: asset-transfer-ledger-queries
          LANGUAGE: go
          TYPE: chaincode
        Ledger-Chaincode-Javascript:
          DIRECTORY: asset-transfer-ledger-queries
          LANGUAGE: javascript
          TYPE: chaincode
        PrivateData-Application-Javascript:
          DIRECTORY: asset-transfer-private-data
          LANGUAGE: javascript
          TYPE: application
        PrivateData-Chaincode-Go:
          DIRECTORY: asset-transfer-private-data
          LANGUAGE: go
          TYPE: chaincode
        SBE-Chaincode-Typescript:
          DIRECTORY: asset-transfer-sbe
          LANGUAGE: typescript
          TYPE: chaincode
        SBE-Chaincode-Java:
          DIRECTORY: asset-transfer-sbe
          LANGUAGE: java
          TYPE: chaincode
        Secured-Chaincode-Go:
          DIRECTORY: asset-transfer-secured-agreement
          LANGUAGE: go
          TYPE: chaincode
    steps:
      - task: GoTool@0
        inputs:
          goBin: $(GO_BIN)
          version: $(GO_VER)
        displayName: Install GoLang
      - task: NodeTool@0
        inputs:
          versionSpec: $(NODE_VER)
        displayName: Install Node.js
      - script: ./ci/scripts/lint.sh
        displayName: Lint Code

  - job: TestNetworkBasic
    displayName: Test Network
    pool:
      vmImage: ubuntu-18.04
    strategy:
      matrix:
        Basic-Go:
          CHAINCODE_NAME: basic
          CHAINCODE_LANGUAGE: go
        Basic-Java:
          CHAINCODE_NAME: basic
          CHAINCODE_LANGUAGE: java
        Basic-Javascript:
          CHAINCODE_NAME: basic
          CHAINCODE_LANGUAGE: javascript
        Basic-Typescript:
          CHAINCODE_NAME: basic
          CHAINCODE_LANGUAGE: typescript
    steps:
      - template: templates/install-deps.yml
      - script: sudo apt-get install softhsm2
        displayName: Install SoftHSM
      - script: ../ci/scripts/run-test-network-basic.sh
        workingDirectory: test-network
        displayName: Run Test Network Basic Chaincode

  - job: TestNetworkLedger
    displayName: Test Network
    pool:
      vmImage: ubuntu-18.04
    strategy:
      matrix:
        Ledger-Go:
          CHAINCODE_NAME: ledger
          CHAINCODE_LANGUAGE: go
        Ledger-Javascript:
          CHAINCODE_NAME: ledger
          CHAINCODE_LANGUAGE: javascript
    steps:
      - template: templates/install-deps.yml
      - script: ../ci/scripts/run-test-network-ledger.sh
        workingDirectory: test-network
        displayName: Run Test Network Ledger Chaincode

  - job: TestNetworkPrivate
    displayName: Test Network
    pool:
      vmImage: ubuntu-18.04
    strategy:
      matrix:
        Private-Go:
          CHAINCODE_NAME: private
          CHAINCODE_LANGUAGE: go
    steps:
      - template: templates/install-deps.yml
      - script: ../ci/scripts/run-test-network-private.sh
        workingDirectory: test-network
        displayName: Run Test Network Private Chaincode

  - job: TestNetworkSBE
    displayName: Test Network
    pool:
      vmImage: ubuntu-18.04
    strategy:
      matrix:
        SBE-Typescript:
          CHAINCODE_NAME: sbe
          CHAINCODE_LANGUAGE: typescript
        SBE-Java:
          CHAINCODE_NAME: sbe
          CHAINCODE_LANGUAGE: java
    steps:
      - template: templates/install-deps.yml
      - script: ../ci/scripts/run-test-network-sbe.sh
        workingDirectory: test-network
        displayName: Run Test Network SBE Chaincode

  - job: TestNetworkSecured
    displayName: Test Network
    pool:
      vmImage: ubuntu-18.04
    strategy:
      matrix:
        Secured-Go:
          CHAINCODE_NAME: secured
          CHAINCODE_LANGUAGE: go
    steps:
      - template: templates/install-deps.yml
      - script: ../ci/scripts/run-test-network-secured.sh
        workingDirectory: test-network
        displayName: Run Test Network Secured Chaincode

  - job: TestNetworkEvents
    displayName: Test Network
    pool:
      vmImage: ubuntu-18.04
    strategy:
      matrix:
        Events-Javascript:
          CHAINCODE_NAME: events
          CHAINCODE_LANGUAGE: javascript
    steps:
      - template: templates/install-deps.yml
      - script: ../ci/scripts/run-test-network-events.sh
        workingDirectory: test-network
        displayName: Run Test Network Events Chaincode
