/*
 * SPDX-License-Identifier: Apache-2.0
 */

plugins {
    id 'application'
    id 'checkstyle'
    id 'jacoco'
}

group 'org.hyperledger.fabric.samples'
version '1.0-SNAPSHOT'

dependencies {
    implementation 'org.hyperledger.fabric-chaincode-java:fabric-chaincode-shim:2.2.+'
    implementation 'org.json:json:+'
    testImplementation 'org.hyperledger.fabric-chaincode-java:fabric-chaincode-shim:2.2.+'
}

repositories {
    maven {
        url "https://hyperledger.jfrog.io/hyperledger/fabric-maven"
    }
    jcenter()
    maven {
        url 'https://jitpack.io'
    }
}

application {
    mainClassName =  'org.hyperledger.fabric.contract.ContractRouter'
}


checkstyle {
    toolVersion '8.21'
    configFile file("config/checkstyle/checkstyle.xml")
}

checkstyleMain {
    source ='src/main/java'
}

checkstyleTest {
    source ='src/test/java'
}

jacocoTestReport {
    dependsOn test
}

installDist.dependsOn check
