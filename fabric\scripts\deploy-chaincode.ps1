# 设置工作目录
$FABRIC_DIR = "D:\admin\4-15\fabric"
Set-Location $FABRIC_DIR

# 检查必要的目录和文件
Write-Host "=== Checking Required Directories and Files ==="
if (-not (Test-Path "chaincode")) {
    Write-Host "Creating chaincode directory..."
    New-Item -ItemType Directory -Path "chaincode"
}

# 部署链码
Write-Host "=== Deploying Chaincode ==="
Write-Host "Installing chaincode on peer0.org1.example.com..."
docker exec -e CORE_PEER_MSPCONFIGPATH=/opt/gopath/src/github.com/hyperledger/fabric/peer/crypto/peerOrganizations/org1.example.com/users/<EMAIL>/msp `
    -e CORE_PEER_ADDRESS=peer0.org1.example.com:7051 `
    -e CORE_PEER_LOCALMSPID="Org1MSP" `
    -e CORE_PEER_TLS_ENABLED=true `
    -e CORE_PEER_TLS_CERT_FILE=/opt/gopath/src/github.com/hyperledger/fabric/peer/crypto/peerOrganizations/org1.example.com/peers/peer0.org1.example.com/tls/server.crt `
    -e CORE_PEER_TLS_KEY_FILE=/opt/gopath/src/github.com/hyperledger/fabric/peer/crypto/peerOrganizations/org1.example.com/peers/peer0.org1.example.com/tls/server.key `
    -e CORE_PEER_TLS_ROOTCERT_FILE=/opt/gopath/src/github.com/hyperledger/fabric/peer/crypto/peerOrganizations/org1.example.com/peers/peer0.org1.example.com/tls/ca.crt `
    cli peer chaincode install -n survey -v 1.0 -p github.com/chaincode/survey

if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Failed to install chaincode"
    exit 1
}

# 实例化链码
Write-Host "=== Instantiating Chaincode ==="
docker exec -e CORE_PEER_MSPCONFIGPATH=/opt/gopath/src/github.com/hyperledger/fabric/peer/crypto/peerOrganizations/org1.example.com/users/<EMAIL>/msp `
    -e CORE_PEER_ADDRESS=peer0.org1.example.com:7051 `
    -e CORE_PEER_LOCALMSPID="Org1MSP" `
    -e CORE_PEER_TLS_ENABLED=true `
    -e CORE_PEER_TLS_CERT_FILE=/opt/gopath/src/github.com/hyperledger/fabric/peer/crypto/peerOrganizations/org1.example.com/peers/peer0.org1.example.com/tls/server.crt `
    -e CORE_PEER_TLS_KEY_FILE=/opt/gopath/src/github.com/hyperledger/fabric/peer/crypto/peerOrganizations/org1.example.com/peers/peer0.org1.example.com/tls/server.key `
    -e CORE_PEER_TLS_ROOTCERT_FILE=/opt/gopath/src/github.com/hyperledger/fabric/peer/crypto/peerOrganizations/org1.example.com/peers/peer0.org1.example.com/tls/ca.crt `
    cli peer chaincode instantiate -o orderer.example.com:7050 `
    --tls --cafile /opt/gopath/src/github.com/hyperledger/fabric/peer/crypto/ordererOrganizations/example.com/orderers/orderer.example.com/msp/tlscacerts/tlsca.example.com-cert.pem `
    -C survey-channel -n survey -v 1.0 -c '{"Args":["init"]}' -P "AND('Org1MSP.member')"

if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Failed to instantiate chaincode"
    exit 1
}

# 等待链码实例化完成
Write-Host "Waiting for chaincode to be instantiated..."
Start-Sleep -Seconds 10

# 测试链码
Write-Host "=== Testing Chaincode ==="
Write-Host "Invoking chaincode..."
docker exec -e CORE_PEER_MSPCONFIGPATH=/opt/gopath/src/github.com/hyperledger/fabric/peer/crypto/peerOrganizations/org1.example.com/users/<EMAIL>/msp `
    -e CORE_PEER_ADDRESS=peer0.org1.example.com:7051 `
    -e CORE_PEER_LOCALMSPID="Org1MSP" `
    -e CORE_PEER_TLS_ENABLED=true `
    -e CORE_PEER_TLS_CERT_FILE=/opt/gopath/src/github.com/hyperledger/fabric/peer/crypto/peerOrganizations/org1.example.com/peers/peer0.org1.example.com/tls/server.crt `
    -e CORE_PEER_TLS_KEY_FILE=/opt/gopath/src/github.com/hyperledger/fabric/peer/crypto/peerOrganizations/org1.example.com/peers/peer0.org1.example.com/tls/server.key `
    -e CORE_PEER_TLS_ROOTCERT_FILE=/opt/gopath/src/github.com/hyperledger/fabric/peer/crypto/peerOrganizations/org1.example.com/peers/peer0.org1.example.com/tls/ca.crt `
    cli peer chaincode invoke -o orderer.example.com:7050 `
    --tls --cafile /opt/gopath/src/github.com/hyperledger/fabric/peer/crypto/ordererOrganizations/example.com/orderers/orderer.example.com/msp/tlscacerts/tlsca.example.com-cert.pem `
    -C survey-channel -n survey -c '{"Args":["createSurvey","survey1","Test Survey","This is a test survey"]}'

if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Failed to invoke chaincode"
    exit 1
}

# 查询链码
Write-Host "Querying chaincode..."
docker exec -e CORE_PEER_MSPCONFIGPATH=/opt/gopath/src/github.com/hyperledger/fabric/peer/crypto/peerOrganizations/org1.example.com/users/<EMAIL>/msp `
    -e CORE_PEER_ADDRESS=peer0.org1.example.com:7051 `
    -e CORE_PEER_LOCALMSPID="Org1MSP" `
    -e CORE_PEER_TLS_ENABLED=true `
    -e CORE_PEER_TLS_CERT_FILE=/opt/gopath/src/github.com/hyperledger/fabric/peer/crypto/peerOrganizations/org1.example.com/peers/peer0.org1.example.com/tls/server.crt `
    -e CORE_PEER_TLS_KEY_FILE=/opt/gopath/src/github.com/hyperledger/fabric/peer/crypto/peerOrganizations/org1.example.com/peers/peer0.org1.example.com/tls/server.key `
    -e CORE_PEER_TLS_ROOTCERT_FILE=/opt/gopath/src/github.com/hyperledger/fabric/peer/crypto/peerOrganizations/org1.example.com/peers/peer0.org1.example.com/tls/ca.crt `
    cli peer chaincode query -C survey-channel -n survey -c '{"Args":["querySurvey","survey1"]}'

if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Failed to query chaincode"
    exit 1
}

Write-Host "=== Chaincode Deployment Complete ==="
Write-Host "Chaincode has been successfully deployed and tested!" 