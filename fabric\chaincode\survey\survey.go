package main

import (
    "encoding/json"
    "fmt"
    "github.com/hyperledger/fabric-contract-api-go/contractapi"
)

// SurveyContract 应急管理问卷智能合约
// 
// 本智能合约在应急管理问卷系统中的作用:
// 1. 提供问卷数据的分布式存储功能，确保在灾害场景下数据的可靠性
// 2. 保证问卷数据的完整性和不可篡改性，为应急决策提供可信数据支持
// 3. 实现多方参与的数据共享与管理，支持不同应急管理部门之间的协作
// 4. 建立可追溯、可信任的应急管理数据库，提升应急决策的科学性
type SurveyContract struct {
    contractapi.Contract
}

// SurveyResponse 表示一个问卷回复记录
// 
// 在应急管理场景中的应用:
// - 存储灾害情况下的现场反馈信息
// - 记录应急预案执行情况的调查数据
// - 灾后评估与恢复计划的民意收集
// - 应急管理满意度与改进建议的收集
type SurveyResponse struct {
    ResponseID  string    `json:"response_id"`  // 问卷回复唯一标识
    SurveyID    string    `json:"survey_id"`    // 所属问卷ID
    UserID      string    `json:"user_id"`      // 填写用户ID（匿名问卷时为空）
    SubmittedAt string    `json:"submitted_at"` // 提交时间
    Answers     []Answer  `json:"answers"`      // 回答列表
}

// Answer 表示问卷中的一个问题回答
//
// 在应急管理中的典型应用:
// - 收集灾害现场情况描述
// - 记录受灾程度评估数据
// - 统计应急物资需求信息
// - 评估应急响应效率和质量
type Answer struct {
    QuestionID string `json:"question_id"` // 问题ID
    Answer     string `json:"answer"`      // 回答内容
}

// CreateResponse 将问卷回复数据存储到区块链
//
// 在应急管理系统中的应用:
// 1. 用户提交问卷后，数据被加密并调用此函数存储到区块链
// 2. 确保应急管理相关的问卷数据不被篡改，保持原始性
// 3. 关键应急数据的分布式备份，避免单点失效导致数据丢失
// 4. 为后续的应急决策提供可验证的数据基础
func (s *SurveyContract) CreateResponse(ctx contractapi.TransactionContextInterface, responseData string) error {
    var response SurveyResponse
    err := json.Unmarshal([]byte(responseData), &response)
    if err != nil {
        return fmt.Errorf("failed to unmarshal response: %v", err)
    }
    
    return ctx.GetStub().PutState(response.ResponseID, []byte(responseData))
}

// GetResponse 从区块链获取问卷回复数据
//
// 在应急管理系统中的应用:
// 1. 数据分析：获取原始问卷数据进行应急情况分析
// 2. 数据验证：验证问卷数据的真实性和完整性
// 3. 审计追踪：检查问卷数据是否被篡改过
// 4. 决策支持：为应急管理部门提供可信的决策依据
func (s *SurveyContract) GetResponse(ctx contractapi.TransactionContextInterface, responseID string) (string, error) {
    responseBytes, err := ctx.GetStub().GetState(responseID)
    if err != nil {
        return "", fmt.Errorf("failed to read response: %v", err)
    }
    if responseBytes == nil {
        return "", fmt.Errorf("response not found")
    }
    
    return string(responseBytes), nil
}

func main() {
    chaincode, err := contractapi.NewChaincode(&SurveyContract{})
    if err != nil {
        fmt.Printf("Error creating survey chaincode: %v", err)
        return
    }

    if err := chaincode.Start(); err != nil {
        fmt.Printf("Error starting survey chaincode: %v", err)
    }
} 