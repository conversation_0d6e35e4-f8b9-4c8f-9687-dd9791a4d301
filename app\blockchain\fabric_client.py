import requests
import json
import asyncio
from datetime import datetime

class FabricClient:
    def __init__(self):
        """
        初始化Fabric客户端
        
        本系统基于Hyperledger Fabric区块链网络构建，主要应用于：
        1. 应急管理问卷数据的安全存储与验证
        2. 分布式数据账本，确保灾害场景下数据的可用性
        3. 构建可信的应急管理数据库，支持决策分析
        
        Fabric网络配置：
        - 多组织结构：模拟多个应急管理部门共同维护同一账本
        - 专用通道：使用survey-channel专用通道处理问卷数据
        - 智能合约：使用survey链码处理问卷数据的存储与查询
        """
        # 设置Fabric网络配置
        self.config = {
            'peer_url': 'http://localhost:7051',
            'orderer_url': 'http://localhost:7050',
            'channel': 'survey-channel',
            'chaincode': 'survey'
        }
    
    async def connect(self):
        """
        连接到Fabric网络
        
        在系统中的应用：
        - 系统启动时自动连接到区块链网络
        - 提供与Hyperledger Fabric网络的通信接口
        - 维护应急管理问卷系统与区块链之间的连接状态
        """
        try:
            # 检查peer是否在线
            response = requests.get(f"{self.config['peer_url']}/healthz")
            if response.status_code != 200:
                raise Exception("Peer节点未就绪")
            
            # 检查orderer是否在线
            response = requests.get(f"{self.config['orderer_url']}/healthz")
            if response.status_code != 200:
                raise Exception("Orderer节点未就绪")
            
            return True
        except Exception as e:
            print(f"连接Fabric网络失败: {str(e)}")
            return False
    
    async def add_transaction(self, data):
        """
        添加交易到区块链
        
        在应急管理系统中的应用：
        1. 问卷提交流程：当用户完成问卷提交后，加密数据被提交到区块链
        2. 数据安全存储：确保应急管理问卷数据不被篡改
        3. 分布式存储：即使在灾害场景下，数据也能安全存储，避免单点故障
        4. 交易透明性：所有问卷数据上链操作可被审计追踪
        
        参数:
            data: 交易数据（加密的问卷回复信息）
        
        返回:
            transaction_id: 交易ID（用于后续查询验证）
        """
        try:
            # 准备交易数据
            transaction = {
                'data': data,
                'timestamp': datetime.utcnow().isoformat()
            }
            
            # 提交交易
            response = requests.post(
                f"{self.config['peer_url']}/channels/{self.config['channel']}/chaincodes/{self.config['chaincode']}",
                json={
                    'fcn': 'addTransaction',
                    'args': [json.dumps(transaction)]
                }
            )
            
            if response.status_code == 200:
                return response.json()['transaction_id']
            else:
                raise Exception(f"添加交易失败: {response.text}")
        except Exception as e:
            print(f"添加交易失败: {str(e)}")
            return None
    
    async def get_transaction(self, transaction_id):
        """
        获取交易信息
        
        在应急管理系统中的应用：
        1. 问卷数据验证：验证问卷回复的真实性和完整性
        2. 数据审计：管理员可审计特定问卷数据的处理过程
        3. 问卷数据展示：在可视化界面中获取和展示数据详情
        4. 应急决策支持：为应急情况下的决策提供可信数据支持
        
        参数:
            transaction_id: 交易ID
        
        返回:
            transaction: 交易数据
        """
        try:
            # 查询交易
            response = requests.get(
                f"{self.config['peer_url']}/channels/{self.config['channel']}/chaincodes/{self.config['chaincode']}",
                params={
                    'fcn': 'getTransaction',
                    'args': [transaction_id]
                }
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                raise Exception(f"获取交易失败: {response.text}")
        except Exception as e:
            print(f"获取交易失败: {str(e)}")
            return None
    
    async def get_blockchain(self):
        """
        获取区块链信息
        
        在应急管理系统中的应用：
        1. 区块链监控：实时监控区块链网络状态
        2. 系统健康检查：验证区块链服务可用性
        3. 数据统计分析：提供区块链上问卷数据的统计信息
        4. 可视化展示：在区块链数据可视化页面展示网络状态
        
        返回:
            blockchain: 区块链数据
        """
        try:
            # 查询区块链
            response = requests.get(
                f"{self.config['peer_url']}/channels/{self.config['channel']}/chaincodes/{self.config['chaincode']}",
                params={
                    'fcn': 'getBlockchain'
                }
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                raise Exception(f"获取区块链失败: {response.text}")
        except Exception as e:
            print(f"获取区块链失败: {str(e)}")
            return None
    
    async def validate_chain(self):
        """
        验证区块链的有效性
        
        在应急管理系统中的应用：
        1. 数据完整性检查：确保应急管理问卷数据未被篡改
        2. 系统安全审计：验证区块链账本的一致性
        3. 网络健康监测：检测区块链网络中的异常节点
        4. 数据可信度评估：为应急管理数据提供可信度评分
        
        返回:
            is_valid: 区块链是否有效
        """
        try:
            # 查询区块链验证状态
            response = requests.get(
                f"{self.config['peer_url']}/channels/{self.config['channel']}/chaincodes/{self.config['chaincode']}",
                params={
                    'fcn': 'validateChain'
                }
            )
            
            if response.status_code == 200:
                return response.json()['is_valid']
            else:
                raise Exception(f"验证区块链失败: {response.text}")
        except Exception as e:
            print(f"验证区块链失败: {str(e)}")
            return False
    
    async def close(self):
        """
        关闭连接
        """
        pass

# 创建全局Fabric客户端实例
_fabric_client = None

async def get_fabric_client():
    """
    获取Fabric客户端实例
    
    在应急管理系统中的作用：
    1. 提供统一的区块链访问接口
    2. 确保系统中只有一个活跃的区块链客户端连接
    3. 管理应急管理问卷系统与Hyperledger Fabric网络的通信
    
    返回:
        fabric_client: Fabric客户端实例
    """
    global _fabric_client
    if _fabric_client is None:
        _fabric_client = FabricClient()
        await _fabric_client.connect()
    return _fabric_client 