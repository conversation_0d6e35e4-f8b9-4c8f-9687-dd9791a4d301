import json
import asyncio
from datetime import datetime
from app.blockchain.fabric_client import get_fabric_client

class Blockchain:
    def __init__(self):
        """
        初始化区块链
        
        本系统使用区块链技术实现以下核心功能：
        1. 问卷数据不可篡改存储：所有提交的问卷回答都被加密后存储在区块链上
        2. 数据完整性验证：通过区块链的哈希链式结构确保数据未被篡改
        3. 分布式存储：避免中心化数据库可能带来的单点故障问题
        4. 应急管理数据的可信度保障：特别适用于应急情况下的问卷调查数据
        """
        self.client = None
        asyncio.run(self._init_client())
    
    async def _init_client(self):
        """
        初始化Fabric客户端
        
        连接到Hyperledger Fabric网络，该网络由多个组织节点组成，
        使用PBFT(实用拜占庭容错)共识算法确保网络安全和数据一致性
        """
        self.client = await get_fabric_client()
    
    async def add_transaction(self, data):
        """
        添加交易到区块链
        
        应用场景：
        - 当用户提交问卷回答时，系统对数据进行加密
        - 加密后的数据被包装成交易提交到区块链
        - 特别适用于应急管理问卷，确保在灾害、事故等紧急情况下收集的数据不会被篡改
        
        参数:
            data: 交易数据(已加密的问卷回答)
        
        返回:
            transaction_id: 交易ID(可用作数据验证凭证)
        """
        if not self.client:
            await self._init_client()
        return await self.client.add_transaction(data)
    
    async def get_transaction(self, transaction_id):
        """
        获取交易信息
        
        应用场景：
        - 数据审计：管理员验证特定问卷回答的真实性
        - 数据分析：从区块链获取原始数据进行可信分析
        - 应急响应：在应急情况下快速获取可信的问卷调查数据
        
        参数:
            transaction_id: 交易ID
        
        返回:
            transaction: 交易数据
        """
        if not self.client:
            await self._init_client()
        return await self.client.get_transaction(transaction_id)
    
    async def get_transaction_data(self, transaction_id):
        """
        获取交易数据
        
        应用场景：
        - 生成问卷分析报告时，从区块链获取可信数据源
        - 应急决策支持：为应急管理部门提供基于区块链的可信数据
        
        参数:
            transaction_id: 交易ID
        
        返回:
            data: 交易数据
        """
        if not self.client:
            await self._init_client()
        transaction = await self.client.get_transaction(transaction_id)
        if transaction:
            return transaction.get('data')
        return None
    
    async def get_blockchain(self):
        """
        获取区块链信息
        
        应用场景：
        - 系统监控：实时监控区块链网络状态
        - 数据可视化：为管理员提供区块链数据的可视化展示
        - 应急响应数据总览：查看应急管理相关问卷数据的整体情况
        
        返回:
            blockchain: 区块链数据
        """
        if not self.client:
            await self._init_client()
        return await self.client.get_blockchain()
    
    async def validate_chain(self):
        """
        验证区块链的有效性
        
        应用场景：
        - 系统安全审计：定期验证区块链数据的完整性
        - 应急数据可信度验证：确保应急管理问卷数据未被篡改
        
        返回:
            is_valid: 区块链是否有效
        """
        if not self.client:
            await self._init_client()
        return await self.client.validate_chain()
    
    async def close(self):
        """
        关闭连接
        """
        if self.client:
            await self.client.close()

# 创建全局区块链实例
_blockchain = None

async def get_blockchain():
    """
    获取区块链实例
    
    本系统中区块链技术的主要应用：
    1. 问卷数据防篡改存储：特别是应急管理相关问卷
    2. 数据可信审计：确保问卷调查结果的真实性
    3. 分布式数据管理：提高系统的可靠性和容错性
    4. 透明化管理：数据存储过程对管理员可见
    
    返回:
        blockchain: 区块链实例
    """
    global _blockchain
    if _blockchain is None:
        _blockchain = Blockchain()
    return _blockchain

async def add_transaction(data):
    """
    添加交易到区块链
    
    系统集成点：
    - 在问卷提交流程(Response.save_to_blockchain方法)中被调用
    - 将加密后的问卷回答数据写入区块链
    - 应急管理场景：确保收集的应急情况反馈数据不可篡改
    
    参数:
        data: 交易数据
    
    返回:
        transaction_id: 交易ID
    """
    blockchain = await get_blockchain()
    return await blockchain.add_transaction(data)

async def get_transaction(transaction_id):
    """
    获取交易信息
    
    系统集成点：
    - 在问卷结果验证流程中使用
    - 在区块链数据可视化页面中展示交易详情
    - 应急管理场景：验证特定应急问卷回复的真实性
    
    参数:
        transaction_id: 交易ID
    
    返回:
        transaction: 交易数据
    """
    blockchain = await get_blockchain()
    return await blockchain.get_transaction(transaction_id)

async def get_transaction_data(transaction_id):
    """
    获取交易数据
    
    系统集成点：
    - Response.get_blockchain_data方法中使用
    - 用于获取并解密存储在区块链上的问卷回答
    - 应急管理应用：提取历史应急事件的问卷调查数据进行分析
    
    参数:
        transaction_id: 交易ID
    
    返回:
        data: 交易数据
    """
    blockchain = await get_blockchain()
    return await blockchain.get_transaction_data(transaction_id)

async def get_chain():
    """
    获取完整区块链
    
    系统集成点：
    - 在区块链可视化页面(/blockchain路由)中使用
    - 用于展示整个区块链的健康状态和统计数据
    - 应急管理应用：总览所有与应急管理相关的问卷数据流转情况
    
    返回:
        chain: 区块链数据
    """
    blockchain = await get_blockchain()
    return await blockchain.get_blockchain()

async def validate_chain():
    """
    验证区块链的有效性
    
    系统集成点：
    - 在系统安全审计流程中定期调用
    - 在区块链状态监控页面中展示验证结果
    - 应急管理应用：确保应急响应数据的可信度和完整性
    
    返回:
        is_valid: 区块链是否有效
    """
    blockchain = await get_blockchain()
    return await blockchain.validate_chain()

async def close():
    """
    关闭区块链连接
    """
    global _blockchain
    if _blockchain:
        await _blockchain.close()
        _blockchain = None 