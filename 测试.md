# 第六章 系统测试

## 6.1 测试目的

本章对基于区块链的应急管理问卷调查系统进行全面测试，主要目的包括：

1. **功能验证**：确保系统各模块按照设计需求正常工作，包括用户管理、问卷管理、数据收集和区块链数据存储等核心功能。
2. **安全性测试**：验证区块链加密机制的有效性，确保问卷数据的安全性和不可篡改性。
3. **性能评估**：测试系统在不同负载条件下的性能表现，包括响应时间和并发处理能力。
4. **用户体验测试**：评估系统界面的易用性和交互设计的合理性。
5. **集成测试**：验证系统与Hyperledger Fabric区块链网络的集成效果。

## 6.2 测试方法

### 6.2.1 单元测试

对系统的各个组件和函数进行独立测试，确保其功能正确性。主要使用Python的unittest框架进行测试，针对以下模块：

- 用户认证模块
- 问卷管理模块
- 区块链加密模块
- 数据处理和统计模块

### 6.2.2 集成测试

测试系统各模块之间的协作，特别是Web应用与区块链网络的集成。主要关注点包括：

- Flask应用与Hyperledger Fabric的通信
- 数据库与区块链的数据一致性
- 前端界面与后端API的交互

### 6.2.3 安全测试

针对系统的安全机制进行专项测试，包括：

- 区块链数据加密和解密功能测试
- 交易数据完整性验证测试
- SQL注入和XSS攻击防护测试
- 用户权限和访问控制测试

### 6.2.4 性能测试

使用Apache JMeter等工具模拟多用户并发访问，测试系统性能：

- 并发用户处理能力
- 大规模问卷数据处理性能
- 区块链交易处理速度
- 系统资源占用情况

### 6.2.5 用户验收测试

邀请目标用户参与测试，收集反馈意见：

- 功能易用性评估
- 界面友好度评估
- 操作流程合理性评估

## 6.3 测试环境

### 6.3.1 硬件环境

- 服务器配置：Intel Core i7处理器，16GB RAM，256GB SSD
- 客户端配置：多种设备（PC、平板、手机）测试响应式设计

### 6.3.2 软件环境

- 操作系统：Ubuntu 20.04 LTS
- Web服务器：Nginx 1.18
- 数据库：MySQL 8.0
- 区块链网络：Hyperledger Fabric 2.2
- 浏览器：Chrome、Firefox、Safari、Edge最新版本

## 6.4 测试用例与结果

### 6.4.1 用户管理模块测试

#### 测试用例1：用户注册功能

**测试步骤：**
1. 访问系统注册页面
2. 输入用户信息（用户名、密码、邮箱等）
3. 提交注册表单
4. 验证注册结果

**预期结果：**
- 用户信息成功保存到数据库
- 系统返回注册成功提示
- 用户可以使用新账号登录

**测试结果：**
| 测试ID | 测试数据 | 预期结果 | 实际结果 | 是否通过 |
|--------|---------|----------|----------|----------|
| UT-001 | 有效用户信息 | 注册成功 | 注册成功 | ✓ |
| UT-002 | 已存在用户名 | 提示用户名已存在 | 提示用户名已存在 | ✓ |
| UT-003 | 无效邮箱格式 | 提示邮箱格式错误 | 提示邮箱格式错误 | ✓ |

#### 测试用例2：用户登录功能

**测试步骤：**
1. 访问系统登录页面
2. 输入用户名和密码
3. 提交登录表单
4. 验证登录结果

**测试结果：**
| 测试ID | 测试数据 | 预期结果 | 实际结果 | 是否通过 |
|--------|---------|----------|----------|----------|
| UT-004 | 有效凭据 | 登录成功，跳转到仪表盘 | 登录成功，跳转到仪表盘 | ✓ |
| UT-005 | 无效密码 | 提示密码错误 | 提示密码错误 | ✓ |
| UT-006 | 不存在用户 | 提示用户不存在 | 提示用户不存在 | ✓ |

### 6.4.2 问卷管理模块测试

#### 测试用例3：创建问卷功能

**测试步骤：**
1. 登录系统
2. 访问创建问卷页面
3. 填写问卷基本信息（标题、描述、过期时间等）
4. 添加不同类型的问题（单选、多选、文本）
5. 保存问卷

**测试结果：**
| 测试ID | 测试数据 | 预期结果 | 实际结果 | 是否通过 |
|--------|---------|----------|----------|----------|
| ST-001 | 完整问卷信息 | 创建成功 | 创建成功 | ✓ |
| ST-002 | 无标题问卷 | 提示标题不能为空 | 提示标题不能为空 | ✓ |
| ST-003 | 过期日期早于当前日期 | 提示日期无效 | 提示日期无效 | ✓ |

#### 测试用例4：回答问卷功能

**测试步骤：**
1. 访问问卷链接
2. 填写问卷回答
3. 提交问卷

**测试结果：**
| 测试ID | 测试数据 | 预期结果 | 实际结果 | 是否通过 |
|--------|---------|----------|----------|----------|
| ST-004 | 所有问题均已回答 | 提交成功 | 提交成功 | ✓ |
| ST-005 | 必填问题未回答 | 提示必填问题未回答 | 提示必填问题未回答 | ✓ |
| ST-006 | 已过期问卷 | 提示问卷已过期 | 提示问卷已过期 | ✓ |

### 6.4.3 区块链模块测试

#### 测试用例5：数据加密与区块链存储

**测试步骤：**
1. 创建问卷并收集回答
2. 检查回答数据是否加密
3. 验证数据是否成功存入区块链

**测试结果：**
| 测试ID | 测试场景 | 预期结果 | 实际结果 | 是否通过 |
|--------|---------|----------|----------|----------|
| BT-001 | 问卷回答提交 | 数据加密并添加到区块链 | 数据加密并添加到区块链 | ✓ |
| BT-002 | 查看区块链交易 | 能查询到加密后的交易数据 | 能查询到加密后的交易数据 | ✓ |
| BT-003 | 使用密钥解密数据 | 解密后数据与原始数据一致 | 解密后数据与原始数据一致 | ✓ |

#### 测试用例6：区块链数据验证

**测试步骤：**
1. 访问区块链数据验证页面
2. 输入交易哈希值
3. 验证交易数据完整性

**测试结果：**
| 测试ID | 测试场景 | 预期结果 | 实际结果 | 是否通过 |
|--------|---------|----------|----------|----------|
| BT-004 | 有效交易哈希 | 验证成功，显示交易信息 | 验证成功，显示交易信息 | ✓ |
| BT-005 | 无效交易哈希 | 提示交易不存在 | 提示交易不存在 | ✓ |
| BT-006 | 尝试篡改数据 | 验证失败，显示数据已被篡改 | 验证失败，显示数据已被篡改 | ✓ |

### 6.4.4 性能测试结果

#### 测试用例7：并发用户测试

**测试配置：**
- 测试工具：Apache JMeter
- 并发用户数：50、100、200、500
- 测试时长：30分钟

**测试结果：**
| 并发用户数 | 平均响应时间(ms) | 最大响应时间(ms) | 事务成功率 | 系统CPU使用率 | 内存使用率 |
|-----------|-----------------|-----------------|-----------|--------------|-----------|
| 50 | 245 | 789 | 100% | 35% | 42% |
| 100 | 380 | 1240 | 99.8% | 48% | 56% |
| 200 | 620 | 2350 | 99.5% | 65% | 72% |
| 500 | 1850 | 5280 | 97.2% | 88% | 91% |

**分析：**
- 系统在200并发用户以下表现良好，平均响应时间控制在500ms以内
- 并发用户达到500时，响应时间显著增加，但系统仍保持稳定运行
- 建议在实际部署中对系统进行水平扩展，以支持更高并发

### 6.4.5 安全测试结果

#### 测试用例8：数据加密安全性测试

**测试步骤：**
1. 使用不同强度的密钥进行加密
2. 尝试通过暴力破解方式解密数据
3. 测试加密算法对抗已知攻击的能力

**测试结果：**
| 测试ID | 测试场景 | 预期结果 | 实际结果 | 是否通过 |
|--------|---------|----------|----------|----------|
| ST-007 | 256位AES加密 | 无法在合理时间内破解 | 无法破解 | ✓ |
| ST-008 | 中间人攻击 | 数据传输过程安全 | 数据传输安全 | ✓ |
| ST-009 | SQL注入测试 | 系统能防御SQL注入 | 系统成功防御 | ✓ |

## 6.5 测试结论

通过对基于区块链的应急管理问卷调查系统的全面测试，得出以下结论：

1. **功能完整性**：系统各模块功能完整，能够满足应急管理问卷调查的业务需求。用户可以顺利完成注册、登录、创建和填写问卷等核心操作。

2. **安全可靠性**：区块链加密机制有效保障了问卷数据的安全性和不可篡改性。系统能够抵御常见的网络攻击，保护用户数据安全。

3. **系统性能**：在中等规模负载下（200并发用户以内），系统性能良好，能够满足日常使用需求。高负载情况下，通过适当的服务器扩展可以保持稳定运行。

4. **用户体验**：系统界面友好，操作流程清晰，用户反馈满意度高。

5. **区块链集成**：与Hyperledger Fabric的集成实现了预期功能，数据能够安全地存储在区块链网络中，并支持数据验证。

## 6.6 测试建议

基于测试结果，提出以下改进建议：

1. **性能优化**：
   - 优化数据库查询，提高大数据量下的查询效率
   - 实现适当的缓存机制，减轻数据库负载
   - 考虑使用负载均衡技术，提升高并发下的系统性能

2. **安全增强**：
   - 实现更完善的用户权限管理系统
   - 加强对敏感操作的日志记录和审计
   - 定期进行安全漏洞扫描和修复

3. **功能扩展**：
   - 增加问卷数据分析和挖掘功能
   - 开发移动端应用，提升用户访问便利性
   - 实现与其他应急管理系统的数据共享与集成

4. **测试完善**：
   - 建立自动化测试流程，提高测试效率
   - 扩大用户测试范围，收集更多实际使用反馈
   - 针对区块链部分进行更深入的安全测试 