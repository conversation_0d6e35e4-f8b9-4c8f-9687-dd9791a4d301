{% extends "base.html" %}

{% block title %}用户仪表盘 - 基于区块链的应急管理问卷调查系统{% endblock %}

{% block extra_css %}
<style>
    .dashboard-section {
        margin-bottom: 2.5rem;
    }
    .section-header {
        border-bottom: 2px solid #f0f0f0;
        margin-bottom: 1.5rem;
        padding-bottom: 0.5rem;
    }
    .dashboard-card {
        transition: transform 0.3s, box-shadow 0.3s;
        height: 100%;
    }
    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .dashboard-stats {
        background-color: #f8f9fa;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    .stat-item {
        text-align: center;
        padding: 1rem;
    }
    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        color: #0d6efd;
        display: block;
        margin-bottom: 0.5rem;
    }
    .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
        text-transform: uppercase;
    }
    .survey-date {
        font-size: 0.85rem;
        color: #6c757d;
    }
    .survey-status {
        position: absolute;
        top: 10px;
        right: 10px;
    }
    .badge-active {
        background-color: #28a745;
    }
    .badge-expired {
        background-color: #dc3545;
    }
    .badge-inactive {
        background-color: #6c757d;
    }
    .empty-state {
        text-align: center;
        padding: 3rem 2rem;
        background-color: #f8f9fa;
        border-radius: 0.5rem;
        margin-bottom: 2rem;
    }
    .welcome-message {
        background-color: #e9f7fe;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border-left: 4px solid #0d6efd;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2 mb-0">用户仪表盘</h1>
        <a href="{{ url_for('survey.create_survey') }}" class="btn btn-primary">
            <i class="fas fa-plus-circle me-1"></i> 创建新问卷
        </a>
    </div>

    <!-- 欢迎信息 -->
    <div class="welcome-message">
        <h4><i class="fas fa-user-circle me-2"></i>欢迎回来，{{ current_user.username }}!</h4>
        <p class="mb-0">这里是您的个人仪表盘，您可以管理创建的问卷并查看参与的调查。</p>
    </div>

    <!-- 统计信息 -->
    <div class="dashboard-stats">
        <div class="row">
            <div class="col-md-4 stat-item">
                <span class="stat-number">{{ user_surveys|length }}</span>
                <span class="stat-label">已创建问卷</span>
            </div>
            <div class="col-md-4 stat-item">
                <span class="stat-number">{{ participated_surveys|length }}</span>
                <span class="stat-label">已参与问卷</span>
            </div>
            <div class="col-md-4 stat-item">
                <span class="stat-number">{{ current_user.created_at.strftime('%Y-%m-%d') }}</span>
                <span class="stat-label">账号创建日期</span>
            </div>
        </div>
    </div>

    <!-- 我创建的问卷 -->
    <div class="dashboard-section">
        <h3 class="h4 section-header"><i class="fas fa-clipboard-list me-2"></i>我创建的问卷</h3>
        
        {% if user_surveys %}
        <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
            {% for survey in user_surveys %}
            <div class="col">
                <div class="card dashboard-card h-100">
                    <div class="survey-status">
                        {% if survey.is_active and (not survey.expiry_date or survey.expiry_date > now) %}
                        <span class="badge badge-active">活跃</span>
                        {% elif survey.expiry_date and survey.expiry_date <= now %}
                        <span class="badge badge-expired">已过期</span>
                        {% else %}
                        <span class="badge badge-inactive">未激活</span>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        <h5 class="card-title">{{ survey.title }}</h5>
                        <div class="survey-date mb-2">
                            <i class="far fa-calendar-alt me-1"></i> 创建于 {{ survey.created_at.strftime('%Y-%m-%d') }}
                        </div>
                        <p class="card-text">{{ survey.description|truncate(100) }}</p>
                        <div class="d-flex justify-content-between mt-3">
                            <small class="text-muted">
                                <i class="fas fa-users me-1"></i> {{ survey.responses.count() }} 人已参与
                            </small>
                            <small class="text-muted">
                                <i class="fas fa-question-circle me-1"></i> {{ survey.questions.count() }} 个问题
                            </small>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent border-top-0">
                        <div class="btn-group w-100" role="group">
                            <a href="{{ url_for('survey.edit_survey', survey_id=survey.id) }}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-edit me-1"></i> 编辑
                            </a>
                            <a href="{{ url_for('survey.survey_results', survey_id=survey.id) }}" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-chart-bar me-1"></i> 结果
                            </a>
                            <a href="{{ url_for('survey.view_survey', survey_id=survey.id) }}" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-eye me-1"></i> 预览
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="empty-state">
            <i class="fas fa-clipboard fa-3x mb-3 text-muted"></i>
            <h4 class="mb-2">暂无创建的问卷</h4>
            <p class="text-muted mb-3">您还没有创建任何问卷，点击下方按钮开始创建。</p>
            <a href="{{ url_for('survey.create_survey') }}" class="btn btn-primary">
                <i class="fas fa-plus-circle me-1"></i> 创建问卷
            </a>
        </div>
        {% endif %}
    </div>

    <!-- 我参与的问卷 -->
    <div class="dashboard-section">
        <h3 class="h4 section-header"><i class="fas fa-check-circle me-2"></i>我参与的问卷</h3>
        
        {% if participated_surveys %}
        <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
            {% for survey in participated_surveys %}
            <div class="col">
                <div class="card dashboard-card h-100">
                    <div class="card-body">
                        <h5 class="card-title">{{ survey.title }}</h5>
                        <div class="survey-date mb-2">
                            <i class="far fa-calendar-alt me-1"></i> 参与于 
                            {% set response_date_found = false %}
                            {% for response in survey.responses %}
                                {% if response.user_id == current_user.id and not response_date_found %}
                                {{ response.submitted_at.strftime('%Y-%m-%d') }}
                                {% set response_date_found = true %}
                                {% endif %}
                            {% endfor %}
                        </div>
                        <p class="card-text">{{ survey.description|truncate(100) }}</p>
                    </div>
                    <div class="card-footer bg-transparent border-top-0">
                        <a href="{{ url_for('survey.survey_results', survey_id=survey.id) }}" class="btn btn-outline-info btn-sm w-100">
                            <i class="fas fa-chart-bar me-1"></i> 查看结果
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="empty-state">
            <i class="fas fa-poll fa-3x mb-3 text-muted"></i>
            <h4 class="mb-2">暂无参与的问卷</h4>
            <p class="text-muted mb-3">您还没有参与任何问卷，可以浏览问卷列表开始参与。</p>
            <a href="{{ url_for('survey.surveys') }}" class="btn btn-primary">
                <i class="fas fa-list me-1"></i> 浏览问卷
            </a>
        </div>
        {% endif %}
    </div>

    <!-- 区块链信息提示 -->
    <div class="card bg-light mt-4 mb-4">
        <div class="card-body">
            <h5 class="card-title"><i class="fas fa-shield-alt me-2"></i>区块链数据安全</h5>
            <p class="card-text">
                您的所有问卷数据都受到区块链技术的保护。每份问卷回答都经过加密并在区块链上存储哈希值，保证数据的完整性和不可篡改性。
                <a href="{{ url_for('main.blockchain_info') }}">了解更多</a>
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // 为卡片添加渐入动画
        $('.dashboard-card').each(function(index) {
            $(this).delay(index * 100).animate({opacity: 1}, 500);
        });
    });
</script>
{% endblock %} 