from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_bcrypt import Bcrypt
from flask_login import <PERSON>ginMana<PERSON>
from flask_cors import CORS
import os

from config import config

# 初始化扩展，但不绑定app
db = SQLAlchemy()
migrate = Migrate()
bcrypt = Bcrypt()
login_manager = LoginManager()
login_manager.login_view = 'auth.login'
login_manager.login_message = '请先登录才能访问此页面'
login_manager.login_message_category = 'info'

def create_app(config_name=None):
    """创建Flask应用工厂函数"""
    if not config_name:
        config_name = os.environ.get('FLASK_ENV', 'default')
    
    # 初始化应用
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # 初始化扩展
    db.init_app(app)
    migrate.init_app(app, db)
    bcrypt.init_app(app)
    login_manager.init_app(app)
    CORS(app)
    
    # 注册自定义Jinja2过滤器
    app.jinja_env.filters['split'] = lambda value, delimiter: value.split(delimiter)
    
    # 注册蓝图
    from app.routes.auth import auth_bp
    from app.routes.main import main_bp
    from app.routes.survey import survey_bp
    from app.routes.api import api_bp
    
    app.register_blueprint(auth_bp)
    app.register_blueprint(main_bp)
    app.register_blueprint(survey_bp)
    app.register_blueprint(api_bp, url_prefix='/api')
    
    # 创建数据库表（仅在需要时使用）
    with app.app_context():
        db.create_all()
        
        # 初始化区块链（确保在应用上下文中进行）
        from app.blockchain.chain import get_blockchain
        get_blockchain()
        
    return app 