from app import create_app, db
from app.models.user import User
from app.models.survey import Survey, Question, Option, Response
from app.models.blockchain import Block

app = create_app()

@app.shell_context_processor
def make_shell_context():
    """为Flask shell命令添加上下文"""
    return {
        'db': db, 
        'User': User, 
        'Survey': Survey, 
        'Question': Question, 
        'Option': Option, 
        'Response': Response,
        'Block': Block
    }

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000) 