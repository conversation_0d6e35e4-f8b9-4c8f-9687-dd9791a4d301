@echo off
echo === Cleaning Previous Network ===
docker-compose -f test-network/docker-compose.yaml down --volumes --remove-orphans

echo === Generating Crypto Materials ===
docker run --rm -v %cd%:/fabric hyperledger/fabric-tools:2.2 cryptogen generate --config=/fabric/organizations/cryptogen/crypto-config.yaml --output=/fabric/organizations

echo === Generating Genesis Block ===
docker run --rm -v %cd%:/fabric -e FABRIC_CFG_PATH=/fabric/configtx hyperledger/fabric-tools:2.2 configtxgen -profile SurveyGenesis -channelID system-channel -outputBlock /fabric/system-genesis-block/genesis.block

echo === Generating Channel Configuration ===
docker run --rm -v %cd%:/fabric -e FABRIC_CFG_PATH=/fabric/configtx hyperledger/fabric-tools:2.2 configtxgen -profile SurveyChannel -channelID survey-channel -outputCreateChannelTx /fabric/channel-artifacts/survey-channel.tx

echo === Starting the Network ===
docker-compose -f test-network/docker-compose.yaml up -d

echo === Waiting for network to start ===
timeout /t 10

echo === Checking Container Status ===
docker ps 