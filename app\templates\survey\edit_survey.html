{% extends "base.html" %}

{% block title %}编辑问卷 - {{ survey.title }}{% endblock %}

{% block extra_css %}
<style>
    .question-item {
        margin-bottom: 1.5rem;
        border-radius: 0.5rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .question-item:hover {
        box-shadow: 0 5px 15px rgba(0,0,0,0.15);
    }
    .question-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        background-color: #f8f9fa;
        border-bottom: 1px solid rgba(0,0,0,0.125);
    }
    .question-content {
        padding: 1rem;
    }
    .question-type-badge {
        font-size: 0.8rem;
        padding: 0.3rem 0.6rem;
        border-radius: 1rem;
    }
    .question-actions {
        display: flex;
        gap: 0.5rem;
    }
    .question-required {
        background-color: #dc3545;
        color: white;
        font-size: 0.7rem;
        padding: 0.2rem 0.5rem;
        border-radius: 1rem;
        margin-left: 0.5rem;
    }
    .option-list {
        list-style-type: none;
        padding-left: 0;
    }
    .option-item {
        padding: 0.3rem 0;
        border-bottom: 1px dashed #e9ecef;
    }
    .option-item:last-child {
        border-bottom: none;
    }
    .empty-state {
        text-align: center;
        padding: 3rem 1rem;
        border: 2px dashed #dee2e6;
        border-radius: 0.5rem;
        margin: 2rem 0;
    }
    .survey-basic-info {
        background-color: #f8f9fa;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-lg-10 mx-auto">
            <!-- 页面标题和操作 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h2 mb-0"><i class="fas fa-edit me-2"></i>编辑问卷</h1>
                <a href="{{ url_for('survey.surveys') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i> 返回列表
                </a>
            </div>
            
            <!-- 问卷基本信息编辑表单 -->
            <div class="survey-basic-info mb-4">
                <h3 class="h4 mb-3">基本信息</h3>
                <form method="POST" action="{{ url_for('survey.edit_survey', survey_id=survey.id) }}">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="title" class="form-label">问卷标题*</label>
                            <input type="text" class="form-control" id="title" name="title" value="{{ survey.title }}" required>
                        </div>
                        <div class="col-md-6">
                            <label for="expiry_date" class="form-label">截止日期</label>
                            <input type="date" class="form-control" id="expiry_date" name="expiry_date" 
                                value="{{ survey.expiry_date.strftime('%Y-%m-%d') if survey.expiry_date else '' }}">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">问卷描述</label>
                        <textarea class="form-control" id="description" name="description" rows="3">{{ survey.description }}</textarea>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                    {% if survey.is_active %}checked{% endif %}>
                                <label class="form-check-label" for="is_active">启用问卷</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="is_anonymous" name="is_anonymous" 
                                    {% if survey.is_anonymous %}checked{% endif %}>
                                <label class="form-check-label" for="is_anonymous">匿名问卷</label>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex justify-content-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> 保存基本信息
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- 问题管理 -->
            <div class="mb-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h3 class="h4 mb-0">问题管理</h3>
                    <a href="{{ url_for('survey.add_question', survey_id=survey.id) }}" class="btn btn-success">
                        <i class="fas fa-plus-circle me-1"></i> 添加问题
                    </a>
                </div>
                
                {% if questions %}
                <div class="question-list">
                    {% for question in questions %}
                    <div class="card question-item">
                        <div class="question-header">
                            <div>
                                <span class="fw-bold">Q{{ loop.index }}:</span> 
                                {{ question.content }}
                                {% if question.required %}
                                <span class="question-required">必填</span>
                                {% endif %}
                                <span class="badge bg-info text-white question-type-badge ms-2">
                                    {% if question.type == 'text' %}
                                    文本题
                                    {% elif question.type == 'single_choice' %}
                                    单选题
                                    {% elif question.type == 'multiple_choice' %}
                                    多选题
                                    {% endif %}
                                </span>
                            </div>
                            <div class="question-actions">
                                {% if question.type in ['single_choice', 'multiple_choice'] %}
                                <a href="{{ url_for('survey.edit_options', question_id=question.id) }}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-list-ul me-1"></i> 编辑选项
                                </a>
                                {% endif %}
                                <a href="{{ url_for('survey.edit_question', question_id=question.id) }}" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-edit me-1"></i> 编辑
                                </a>
                                <form method="POST" action="{{ url_for('survey.delete_question', question_id=question.id) }}" 
                                    onsubmit="return confirm('确定要删除这个问题吗？');" style="display:inline;">
                                    <button type="submit" class="btn btn-outline-danger btn-sm">
                                        <i class="fas fa-trash-alt me-1"></i> 删除
                                    </button>
                                </form>
                            </div>
                        </div>
                        {% if question.type in ['single_choice', 'multiple_choice'] and question.options.count() > 0 %}
                        <div class="question-content">
                            <h6 class="mb-2">选项:</h6>
                            <ul class="option-list">
                                {% for option in question.options %}
                                <li class="option-item">{{ loop.index }}. {{ option.content }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="empty-state">
                    <i class="fas fa-question-circle fa-3x mb-3 text-muted"></i>
                    <h4>暂无问题</h4>
                    <p class="text-muted">点击"添加问题"按钮开始创建问题</p>
                </div>
                {% endif %}
            </div>
            
            <!-- 预览按钮 -->
            <div class="d-flex justify-content-between mb-5">
                <a href="{{ url_for('survey.survey_results', survey_id=survey.id) }}" class="btn btn-outline-info">
                    <i class="fas fa-chart-bar me-1"></i> 查看结果
                </a>
                <a href="{{ url_for('survey.view_survey', survey_id=survey.id) }}" class="btn btn-primary">
                    <i class="fas fa-eye me-1"></i> 预览问卷
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %} 