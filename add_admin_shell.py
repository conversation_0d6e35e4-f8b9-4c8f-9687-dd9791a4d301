#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
快速添加管理员账号脚本
用法: python add_admin_shell.py
"""

from app import create_app, db
from app.models.user import User
from datetime import datetime

# 管理员账号信息 (可以根据需要修改)
ADMIN_USERNAME = "admin"
ADMIN_EMAIL = "<EMAIL>"
ADMIN_PASSWORD = "admin123"

def add_admin():
    """添加管理员账号"""
    print("正在创建管理员账号...")
    
    # 检查是否已存在
    existing_user = User.query.filter_by(username=ADMIN_USERNAME).first()
    if existing_user:
        print(f"管理员账号 '{ADMIN_USERNAME}' 已存在，无需创建")
        return
    
    # 创建管理员用户
    admin = User(
        username=ADMIN_USERNAME,
        email=ADMIN_EMAIL,
        role='admin',
        created_at=datetime.utcnow(),
        is_active=True
    )
    admin.password = ADMIN_PASSWORD
    
    # 保存到数据库
    db.session.add(admin)
    db.session.commit()
    
    print(f"管理员账号创建成功!")
    print(f"用户名: {ADMIN_USERNAME}")
    print(f"邮箱: {ADMIN_EMAIL}")
    print(f"密码: {ADMIN_PASSWORD}")
    print("(请登录后立即修改默认密码)")

if __name__ == "__main__":
    app = create_app()
    with app.app_context():
        add_admin() 