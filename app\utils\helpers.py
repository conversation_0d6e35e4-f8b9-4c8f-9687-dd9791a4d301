import re
# 暂时注释掉这些导入，以解决兼容性问题
# import pandas as pd
# import matplotlib.pyplot as plt
import base64
from io import BytesIO
from flask import url_for
from sqlalchemy import func
from app import db
from app.models.survey import Survey, Question, Option, Response, Answer

def is_valid_email(email):
    """
    验证邮箱格式是否正确
    
    参数:
        email: 邮箱地址
    
    返回:
        is_valid: 是否有效
    """
    pattern = r'^[\w\.-]+@[\w\.-]+\.\w+$'
    return re.match(pattern, email) is not None

def sanitize_input(input_string):
    """
    清理输入，防止XSS攻击
    
    参数:
        input_string: 输入字符串
    
    返回:
        sanitized: 清理后的字符串
    """
    # 移除HTML标签
    cleaned = re.sub(r'<[^>]*>', '', input_string)
    # 移除脚本事件
    cleaned = re.sub(r'on\w+\s*=', '', cleaned)
    return cleaned

def generate_survey_stats(survey_id):
    """
    生成问卷统计信息
    
    参数:
        survey_id: 问卷ID
    
    返回:
        stats: 统计信息
    """
    survey = Survey.query.get(survey_id)
    if not survey:
        return None
    
    stats = {
        'total_responses': survey.responses.count(),
        'questions': []
    }
    
    for question in survey.questions.order_by(Question.order).all():
        question_stats = {
            'id': question.id,
            'content': question.content,
            'type': question.type
        }
        
        if question.type in ['single_choice', 'multiple_choice']:
            # 选择题统计
            options = []
            for option in question.options.order_by(Option.order).all():
                # 统计选择此选项的回答数量
                count = db.session.query(func.count(Answer.id)).filter(
                    Answer.question_id == question.id,
                    Answer.selected_options.like(f'%{option.id}%')
                ).scalar()
                
                options.append({
                    'id': option.id,
                    'content': option.content,
                    'count': count,
                    'percentage': round(count / max(1, stats['total_responses']) * 100, 2)
                })
            question_stats['options'] = options
        elif question.type == 'text':
            # 文本题直接获取所有回答
            answers = Answer.query.filter_by(question_id=question.id).all()
            question_stats['answers'] = [answer.text_answer for answer in answers if answer.text_answer]
        
        stats['questions'].append(question_stats)
    
    return stats

def generate_chart(data, chart_type='bar', title=''):
    """
    生成图表
    
    参数:
        data: 图表数据
        chart_type: 图表类型 (bar, pie, line等)
        title: 图表标题
    
    返回:
        img_str: 图表的base64编码字符串
    """
    try:
        import base64
        from io import BytesIO
        import matplotlib
        matplotlib.use('Agg')  # 使用非交互式后端
        import matplotlib.pyplot as plt
        import numpy as np
        
        # 创建一个干净的图形
        plt.clf()
        plt.close('all')
        plt.figure(figsize=(10, 6), dpi=100)
        
        # 处理中文标题
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial']  # 用来正常显示中文标签
        plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
        
        # 设置标题
        plt.title(title)
        
        # 处理空数据情况
        if not data or 'labels' not in data or 'values' not in data or len(data['labels']) == 0:
            plt.text(0.5, 0.5, '暂无数据', horizontalalignment='center', verticalalignment='center', transform=plt.gca().transAxes, fontsize=14)
        else:
            # 生成图表
            if chart_type == 'bar':
                # 准备数据
                labels = data['labels']
                values = data['values']
                
                # 创建柱状图
                bars = plt.bar(range(len(labels)), values, color='#3498db')
                plt.xticks(range(len(labels)), labels, rotation=45, ha='right')
                plt.xlabel(data.get('xlabel', ''))
                plt.ylabel(data.get('ylabel', ''))
                
                # 在柱子上方显示数值
                for bar in bars:
                    height = bar.get_height()
                    plt.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                            f'{height}', ha='center', va='bottom')
                
            elif chart_type == 'pie':
                # 只保留非零值用于绘制饼图
                labels = []
                values = []
                for i, v in enumerate(data['values']):
                    if v > 0:  # 只包括非零值
                        labels.append(data['labels'][i])
                        values.append(v)
                
                if not values:
                    plt.text(0.5, 0.5, '暂无数据', horizontalalignment='center', verticalalignment='center', transform=plt.gca().transAxes, fontsize=14)
                else:
                    # 创建饼图
                    plt.pie(values, labels=labels, autopct='%1.1f%%', 
                           shadow=False, startangle=90)
                    plt.axis('equal')  # 确保饼图为正圆形
                    
            elif chart_type == 'line':
                plt.plot(data['labels'], data['values'], marker='o', linestyle='-', color='#3498db')
                plt.xlabel(data.get('xlabel', ''))
                plt.ylabel(data.get('ylabel', ''))
                plt.xticks(rotation=45)
            
            plt.tight_layout()  # 确保标签不被裁剪
        
        # 保存图表到内存缓冲区
        buffer = BytesIO()
        plt.savefig(buffer, format='png', bbox_inches='tight')
        buffer.seek(0)
        
        # 将图像转换为base64字符串
        img_bytes = buffer.getvalue()
        img_str = base64.b64encode(img_bytes).decode('utf-8')
        
        # 关闭图表，释放资源
        plt.close()
        
        return img_str
    except Exception as e:
        import traceback
        print(f"生成图表错误: {e}")
        print(traceback.format_exc())
        
        # 返回一个简单的错误图像
        return "iVBORw0KGgoAAAANSUhEUgAAAfQAAAEsCAYAAAA1u0HIAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AkEEA4xANXBEAAAAB1pVFh0Q29tbWVudAAAAAAAQ3JlYXRlZCB3aXRoIEdJTVBkLmUHAAAIZ0lEQVR42uzYMQ0AAAzDsJU/6cHI4xKoFHnbzgEASG5KAABgdAAAwOgAABgdAACjAwBgdAAAjA4AgNEBADA6AABGBwDA6AAAGB0AAKMDAIzRAQAwOgAARgcAwOgAABgdAACjAwBgdAAAjA4AgNEBADA6AABGB4DcHgAA//8BAAD//3FCBAUAAAAMSP8arfoLhYqNjYSFAAAAAAAAAAAAAAB4DgAA//8BAAD//7qMIYsAAAQTSURBVHja7doxDcAwEARBGGMwNgbFJpDx10AFKJO8MFPtHbt3JwAAZb0SAACMDgCA0QEAMDoAAEYHAMDoAAAYHQAAowMAYHQAAIwOAIDRAQAwOgAARgcAwOgAABgdAGA9AAAA//8BAAD//7qMIYsAAAQTSURBVHja7doxDcAwEARBGGMwNgbFJpDx10AFKJO8MFPtHbt3JwAAZb0SAACMDgCA0QEAMDoAAEYHAMDoAAAYHQAAowMAYHQAAIwOAIDRAQAwOgAARgcAwOgAABgdAGA9AAAA//8BAAD//7qMIYsAAAQTSURBVHja7doxDcAwEARBGGMwNgbFJpDx10AFKJO8MFPtHbt3JwAAZb0SAACMDgCA0QEAMDoAAEYHAMDoAAAYHQAAowMAYHQAAIwOAIDRAQAwOgAARgcAwOgAABgdAGA9AAAA//8BAAD//wEAAP//uo0mowAABA9JREFU7d27EUMhEEVBhP1HAzVEQyEJNqxpbCLfBnQGqDsnb1oDAJDXlgAAwOgAABgdAACjAwBgdAAAjA4AgNEBADA6AABGBwDA6AAAGB0AAKMDAIzRAQAwOgAARgcAwOgAABgdAACjAwBgdAAAjA4AgNEBADA6AABGB4DsngAA//8BAAD//wEAAP//uo0mowAABA9JREFU7d27EUMhEEVBhP1HAzVEQyEJNqxpbCLfBnQGqDsnb1oDAJDXlgAAwOgAABgdAACjAwBgdAAAjA4AgNEBADA6AABGBwDA6AAAGB0AAKMDAIzRAQAwOgAARgcAwOgAABgdAACjAwBgdAAAjA4AgNEBADA6AABGB4DsngAA//8BAAD//wEAAP//uo0mowAABA9JREFU7d27EUMhEEVBhP1HAzVEQyEJNqxpbCLfBnQGqDsnb1oDAJDXlgAAwOgAABgdAACjAwBgdAAAjA4AgNEBADA6AABGBwDA6AAAGB0AAKMDAIzRAQAwOgAARgcAwOgAABgdAACjAwBgdAAAjA4AgNEBADA6AABGB4DsngAA//8BAAD//wEAAP//AQAA//8BAAD//wEAAP//k19nXwAABBRJREFU7Z2xbcNAEAXFxStYsOxMRpY4sXoQVegcNAkVYQMswYYNnC8WcHdz973y3/+79WoAAJ7kZQsA4LlHAQAAwOgAAIzRAQCMDgCAGh0AEqM37QYAgJHRO/YGAABGBgDwKAAAYHQAgNnWnVzjDQAAAAAAAAAAAAAAAAAAAAAAwL9l3ckRpgEAeD76vTcAADBydAAAAI8CAPC66QYAgO9H794bAAAYGQAA9tCnM9ftBgCAkQEA8CgAAGB0AIDZyB78dAYAgJEBAGAPPXrG9RcAAABGBgCA0QEAjA4AYHQAAIwOAGB0AACjAwBgdAAAowMAYHQAAKMDAGB0AACjAwBgdAAAowMAGB0AAKMDABgdAACjAwAYHQAAowMAYHQAAKMDAGB0AACjXzYQAGA2XAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACm8wEAAP//AwAA//9MN18kO1Z5kQAAAABJRU5ErkJggg=="

def export_survey_data(survey_id, format='csv'):
    """
    导出问卷数据
    
    参数:
        survey_id: 问卷ID
        format: 导出格式 (csv, excel等)
    
    返回:
        data: 导出的数据
        filename: 文件名
    """
    # 由于依赖问题，暂时返回错误信息
    return "导出功能暂时不可用", f"survey_{survey_id}_data.txt"
    
    # 以下代码因依赖问题注释掉
    '''
    survey = Survey.query.get(survey_id)
    if not survey:
        return None, None
    
    # 获取所有回答
    responses = Response.query.filter_by(survey_id=survey_id).all()
    data = []
    
    for response in responses:
        response_data = {
            'response_id': response.id,
            'submitted_at': response.submitted_at,
            'user_id': response.user_id,
        }
        
        # 获取所有问题的回答
        for question in survey.questions.all():
            answer = Answer.query.filter_by(response_id=response.id, question_id=question.id).first()
            if answer:
                if question.type == 'text':
                    response_data[f'Q{question.id}'] = answer.text_answer
                else:
                    # 对于选择题，获取选项内容
                    option_ids = answer.get_selected_options()
                    options = Option.query.filter(Option.id.in_(option_ids)).all()
                    response_data[f'Q{question.id}'] = ', '.join([option.content for option in options])
            else:
                response_data[f'Q{question.id}'] = ''
        
        data.append(response_data)
    
    # 创建DataFrame
    df = pd.DataFrame(data)
    filename = f"survey_{survey_id}_{survey.title.replace(' ', '_')}"
    
    if format == 'csv':
        output = df.to_csv(index=False)
        filename += '.csv'
        return output, filename
    elif format == 'excel':
        output = BytesIO()
        df.to_excel(output, index=False)
        output.seek(0)
        filename += '.xlsx'
        return output.getvalue(), filename
    
    return None, None
    '''

def paginate_items(items, page, per_page):
    """
    分页辅助函数
    
    参数:
        items: 要分页的项目列表
        page: 当前页码
        per_page: 每页项目数量
    
    返回:
        paginated_items: 分页后的项目
        total_pages: 总页数
    """
    total_items = len(items)
    total_pages = (total_items + per_page - 1) // per_page
    
    start_idx = (page - 1) * per_page
    end_idx = min(start_idx + per_page, total_items)
    
    paginated_items = items[start_idx:end_idx]
    
    return paginated_items, total_pages 