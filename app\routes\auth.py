from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_user, logout_user, login_required, current_user
from datetime import datetime

from app import db, bcrypt
from app.models.user import User
from app.utils.helpers import is_valid_email, sanitize_input

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    """用户注册"""
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))
    
    if request.method == 'POST':
        username = sanitize_input(request.form.get('username', ''))
        email = sanitize_input(request.form.get('email', ''))
        password = request.form.get('password', '')
        confirm_password = request.form.get('confirm_password', '')
        
        # 验证输入
        if not username or not email or not password:
            flash('请填写所有必填字段', 'danger')
            return render_template('auth/register.html')
        
        if password != confirm_password:
            flash('两次输入的密码不一致', 'danger')
            return render_template('auth/register.html')
        
        if not is_valid_email(email):
            flash('请输入有效的邮箱地址', 'danger')
            return render_template('auth/register.html')
        
        # 检查用户名和邮箱是否已被使用
        if User.query.filter_by(username=username).first():
            flash('用户名已被使用', 'danger')
            return render_template('auth/register.html')
        
        if User.query.filter_by(email=email).first():
            flash('邮箱已被注册', 'danger')
            return render_template('auth/register.html')
        
        # 创建新用户
        new_user = User(
            username=username,
            email=email,
            password=password,  # 会自动加密
            created_at=datetime.utcnow()
        )
        
        db.session.add(new_user)
        db.session.commit()
        
        flash('注册成功，请登录', 'success')
        return redirect(url_for('auth.login'))
    
    return render_template('auth/register.html')

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """用户登录"""
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))
    
    if request.method == 'POST':
        email = sanitize_input(request.form.get('email', ''))
        password = request.form.get('password', '')
        remember = request.form.get('remember', False) == 'on'
        
        # 验证输入
        if not email or not password:
            flash('请输入邮箱和密码', 'danger')
            return render_template('auth/login.html')
        
        # 查找用户
        user = User.query.filter_by(email=email).first()
        
        if user and user.verify_password(password):
            login_user(user, remember=remember)
            user.update_last_login()
            
            next_page = request.args.get('next')
            if next_page:
                return redirect(next_page)
            return redirect(url_for('main.index'))
        else:
            flash('邮箱或密码错误', 'danger')
    
    return render_template('auth/login.html')

@auth_bp.route('/logout')
@login_required
def logout():
    """用户登出"""
    logout_user()
    flash('已成功登出', 'info')
    return redirect(url_for('auth.login'))

@auth_bp.route('/profile', methods=['GET', 'POST'])
@login_required
def profile():
    """用户个人资料"""
    if request.method == 'POST':
        username = sanitize_input(request.form.get('username', ''))
        current_password = request.form.get('current_password', '')
        new_password = request.form.get('new_password', '')
        confirm_password = request.form.get('confirm_password', '')
        
        # 验证输入
        if not username:
            flash('用户名不能为空', 'danger')
            return render_template('auth/profile.html')
        
        # 检查是否更改用户名
        if username != current_user.username:
            if User.query.filter_by(username=username).first():
                flash('用户名已被使用', 'danger')
                return render_template('auth/profile.html')
            current_user.username = username
        
        # 如果要更改密码
        if current_password and new_password:
            if not current_user.verify_password(current_password):
                flash('当前密码错误', 'danger')
                return render_template('auth/profile.html')
            
            if new_password != confirm_password:
                flash('两次输入的新密码不一致', 'danger')
                return render_template('auth/profile.html')
            
            current_user.password = new_password
        
        db.session.commit()
        flash('个人资料已更新', 'success')
        return redirect(url_for('auth.profile'))
    
    return render_template('auth/profile.html')

@auth_bp.route('/admin/users')
@login_required
def admin_users():
    """管理员用户管理"""
    if not current_user.is_admin():
        flash('没有权限访问此页面', 'danger')
        return redirect(url_for('main.index'))
    
    users = User.query.all()
    return render_template('auth/admin_users.html', users=users)

@auth_bp.route('/admin/user/<int:user_id>', methods=['GET', 'POST'])
@login_required
def admin_user_edit(user_id):
    """管理员编辑用户"""
    if not current_user.is_admin():
        flash('没有权限访问此页面', 'danger')
        return redirect(url_for('main.index'))
    
    user = User.query.get_or_404(user_id)
    
    if request.method == 'POST':
        username = sanitize_input(request.form.get('username', ''))
        email = sanitize_input(request.form.get('email', ''))
        role = request.form.get('role', 'user')
        is_active = request.form.get('is_active', False) == 'on'
        new_password = request.form.get('new_password', '')
        
        # 验证输入
        if not username or not email:
            flash('用户名和邮箱不能为空', 'danger')
            return render_template('auth/admin_user_edit.html', user=user)
        
        # 检查用户名和邮箱是否已被其他用户使用
        if username != user.username and User.query.filter_by(username=username).first():
            flash('用户名已被使用', 'danger')
            return render_template('auth/admin_user_edit.html', user=user)
        
        if email != user.email and User.query.filter_by(email=email).first():
            flash('邮箱已被注册', 'danger')
            return render_template('auth/admin_user_edit.html', user=user)
        
        # 更新用户信息
        user.username = username
        user.email = email
        user.role = role
        user.is_active = is_active
        
        if new_password:
            user.password = new_password
        
        db.session.commit()
        flash('用户信息已更新', 'success')
        return redirect(url_for('auth.admin_users'))
    
    return render_template('auth/admin_user_edit.html', user=user)

@auth_bp.route('/admin/user/delete/<int:user_id>', methods=['POST'])
@login_required
def admin_user_delete(user_id):
    """管理员删除用户"""
    if not current_user.is_admin():
        flash('没有权限访问此页面', 'danger')
        return redirect(url_for('main.index'))
    
    user = User.query.get_or_404(user_id)
    
    # 不能删除自己
    if user.id == current_user.id:
        flash('不能删除当前登录用户', 'danger')
        return redirect(url_for('auth.admin_users'))
    
    db.session.delete(user)
    db.session.commit()
    
    flash('用户已删除', 'success')
    return redirect(url_for('auth.admin_users')) 