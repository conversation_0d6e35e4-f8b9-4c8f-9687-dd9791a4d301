﻿# 设置工作目录
$FABRIC_DIR = "D:\admin\4-15\fabric"
Set-Location $FABRIC_DIR

Write-Host "=== Environment Setup ==="
Write-Host "Working directory: $FABRIC_DIR"

# 创建必要的目录
New-Item -ItemType Directory -Force -Path "organizations\cryptogen"
New-Item -ItemType Directory -Force -Path "organizations\ordererOrganizations"
New-Item -ItemType Directory -Force -Path "organizations\peerOrganizations"
New-Item -ItemType Directory -Force -Path "system-genesis-block"
New-Item -ItemType Directory -Force -Path "channel-artifacts"

# 清理之前的网络
Write-Host "=== Cleaning Previous Network ==="
docker-compose -f test-network\docker-compose.yaml down
docker ps -a | Select-String "dev-peer|orderer|peer" | ForEach-Object { $_.Line.Split()[0] } | ForEach-Object { docker rm -f $_ }
docker images | Select-String "dev-peer|orderer|peer" | ForEach-Object { $_.Line.Split()[2] } | ForEach-Object { docker rmi -f $_ }

# 生成加密材料
Write-Host "=== Generating Crypto Materials ==="
$CRYPTO_CONFIG_PATH = "$FABRIC_DIR\organizations\cryptogen\crypto-config.yaml"
Write-Host "Using crypto-config.yaml from: $CRYPTO_CONFIG_PATH"

# 使用简单的路径映射
Write-Host "Creating temporary container for crypto generation..."
docker run --rm `
    -v "${FABRIC_DIR}:/fabric" `
    --workdir /fabric `
    hyperledger/fabric-tools:2.2 `
    cryptogen generate --config=organizations/cryptogen/crypto-config.yaml --output=organizations

if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Failed to generate crypto materials"
    exit 1
}

Write-Host "=== Verifying Generated Crypto Materials ==="
Get-ChildItem -Force organizations\peerOrganizations
Get-ChildItem -Force organizations\ordererOrganizations

# 生成创世区块
Write-Host "=== Generating Genesis Block ==="
Write-Host "Using configtx.yaml from: $FABRIC_DIR\configtx\configtx.yaml"
docker run --rm `
    -v "${FABRIC_DIR}:/fabric" `
    --workdir /fabric `
    -e FABRIC_CFG_PATH=configtx `
    hyperledger/fabric-tools:2.2 `
    configtxgen -profile SurveyGenesis -channelID system-channel -outputBlock system-genesis-block/genesis.block

if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Failed to generate genesis block"
    exit 1
}

# 生成通道配置
Write-Host "=== Generating Channel Configuration ==="
docker run --rm `
    -v "${FABRIC_DIR}:/fabric" `
    --workdir /fabric `
    -e FABRIC_CFG_PATH=configtx `
    hyperledger/fabric-tools:2.2 `
    configtxgen -profile SurveyChannel -channelID survey-channel -outputCreateChannelTx channel-artifacts/survey-channel.tx

if ($LASTEXITCODE -ne 0) {
    Write-Host "ERROR: Failed to generate channel configuration"
    exit 1
}

# 修改文件权限
Get-ChildItem -Recurse organizations, system-genesis-block, channel-artifacts | ForEach-Object { $_.Attributes = "Normal" }

# 启动网络
Write-Host "=== Starting the Network ==="
docker-compose -f test-network\docker-compose.yaml up -d

# 等待网络启动
Write-Host "Waiting for network to start..."
Start-Sleep -Seconds 10

# 检查容器状态
Write-Host "=== Checking Container Status ==="
docker ps

Write-Host "Network started successfully!"
