Maintainers
===========

fabric-samples uses a non-author code review policy, requiring a single approval from a non-author maintainer.

| Name                      | GitHub           | Chat           | email                               |
|---------------------------|------------------|----------------|-------------------------------------|
| <PERSON><PERSON><PERSON>            | lehor<PERSON>           | lehors         | <EMAIL>                   |
| <PERSON><PERSON>             | harrisob         | bretharrison   | <EMAIL>                 |
| <PERSON>              | christo4ferris   | cbf            | <EMAIL>              |
| <PERSON>              | denyeart         | dave.enyeart   | <EMAIL>                  |
| G<PERSON>                | mastersingh24    | mastersingh24  | <EMAIL>              |
| <PERSON>             | jyellick         | jyellick       | <EMAIL>                 |
| <PERSON>           | mbwhite          | mbwhite        | <EMAIL>                 |
| <PERSON><PERSON>              | nikhil550        | negupta        | <EMAIL>                |
| <PERSON>               | sstone1          | sstone1        | <EMAIL>                  |

Also: Please see the [Release Manager section](https://github.com/hyperledger/fabric/blob/master/MAINTAINERS.md)

<a rel="license" href="http://creativecommons.org/licenses/by/4.0/"><img alt="Creative Commons License" style="border-width:0" src="https://i.creativecommons.org/l/by/4.0/88x31.png" /></a><br />This work is licensed under a <a rel="license" href="http://creativecommons.org/licenses/by/4.0/">Creative Commons Attribution 4.0 International License</a>.
