{"name": "asset-transfer-basic", "version": "1.0.0", "description": "Asset Transfer Basic contract implemented in TypeScript", "main": "dist/index.js", "typings": "dist/index.d.ts", "engines": {"node": ">=12", "npm": ">=5"}, "scripts": {"lint": "tslint -c tslint.json 'src/**/*.ts'", "pretest": "npm run lint", "start": "npm run build && node dist/app.js", "build": "tsc", "build:watch": "tsc -w", "prepublishOnly": "npm run build"}, "engineStrict": true, "author": "Hyperledger", "license": "Apache-2.0", "dependencies": {"fabric-ca-client": "^2.2.4", "fabric-network": "^2.2.4"}, "devDependencies": {"tslint": "^5.11.0", "typescript": "^3.1.6"}, "nyc": {"extension": [".ts", ".tsx"], "exclude": ["coverage/**", "dist/**"], "reporter": ["text-summary", "html"], "all": true, "check-coverage": true, "statements": 100, "branches": 100, "functions": 100, "lines": 100}}