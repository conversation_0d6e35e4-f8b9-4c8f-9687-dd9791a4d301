{"name": "papernet-js", "version": "0.0.1", "description": "Papernet Contract", "main": "index.js", "engines": {"node": ">=8", "npm": ">=5"}, "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "nyc mocha test --recursive", "start": "fabric-chaincode-node start", "mocha": "mocha test --recursive"}, "engineStrict": true, "author": "hyperledger", "license": "Apache-2.0", "dependencies": {"fabric-contract-api": "^2.0.0", "fabric-shim": "^2.0.0"}, "devDependencies": {"chai": "^4.1.2", "chai-as-promised": "^7.1.1", "eslint": "^4.19.1", "mocha": "^5.2.0", "nyc": "^12.0.2", "sinon": "^6.0.0", "sinon-chai": "^3.2.0"}, "nyc": {"exclude": ["coverage/**", "test/**"], "reporter": ["text-summary", "html"], "all": true, "check-coverage": true, "statements": 100, "branches": 100, "functions": 100, "lines": 100}}