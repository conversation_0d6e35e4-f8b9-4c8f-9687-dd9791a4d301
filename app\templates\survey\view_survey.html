{% extends "base.html" %}

{% block title %}{{ survey.title }} - 基于区块链的应急管理问卷调查系统{% endblock %}

{% block extra_css %}
<style>
    .question-card {
        margin-bottom: 1.5rem;
        border-radius: 0.5rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .question-number {
        font-weight: bold;
        color: #0d6efd;
        margin-right: 0.5rem;
    }
    .question-required {
        color: #dc3545;
        font-size: 0.9rem;
        margin-left: 0.5rem;
    }
    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid rgba(0,0,0,0.125);
        padding: 1rem;
    }
    .survey-info {
        background-color: #e9f7fe;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 2rem;
    }
    .option-item {
        margin-bottom: 0.5rem;
    }
    .blockchain-info {
        background-color: #f8f9fa;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-top: 2rem;
        border-left: 4px solid #0d6efd;
    }
    .form-check-input:checked {
        background-color: #0d6efd;
        border-color: #0d6efd;
    }
    textarea.form-control {
        min-height: 120px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <!-- 问卷标题和描述 -->
            <div class="text-center mb-4">
                <h1 class="display-5 fw-bold">{{ survey.title }}</h1>
                {% if survey.description %}
                <p class="lead mt-3">{{ survey.description }}</p>
                {% endif %}
            </div>

            <!-- 问卷信息 -->
            <div class="survey-info">
                <div class="row">
                    <div class="col-md-6">
                        <p><i class="fas fa-question-circle me-2"></i>共 <strong>{{ questions|length }}</strong> 个问题</p>
                        <p><i class="fas fa-user me-2"></i>创建者: <strong>{{ survey.creator.username if survey.creator else '系统管理员' }}</strong></p>
                    </div>
                    <div class="col-md-6">
                        <p><i class="far fa-calendar-alt me-2"></i>创建日期: <strong>{{ survey.created_at.strftime('%Y-%m-%d') }}</strong></p>
                        {% if survey.expiry_date %}
                        <p><i class="fas fa-hourglass-end me-2"></i>截止日期: <strong>{{ survey.expiry_date.strftime('%Y-%m-%d') }}</strong></p>
                        {% endif %}
                    </div>
                </div>
                {% if survey.is_anonymous %}
                <div class="alert alert-success mt-2 mb-0">
                    <i class="fas fa-shield-alt me-2"></i>此为匿名问卷，您的个人信息将不会被记录
                </div>
                {% endif %}
            </div>

            <!-- 问卷表单 -->
            <form method="POST" action="{{ url_for('survey.submit_survey', survey_id=survey.id) }}">
                {% for question in questions %}
                <div class="card question-card">
                    <div class="card-header">
                        <span class="question-number">Q{{ loop.index }}.</span>
                        {{ question.content }}
                        {% if question.required %}
                        <span class="question-required">(必填)</span>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        {% if question.type == 'text' %}
                        <!-- 文本题 -->
                        <textarea class="form-control" name="question_{{ question.id }}" rows="3" {% if question.required %}required{% endif %}></textarea>
                        
                        {% elif question.type == 'single_choice' %}
                        <!-- 单选题 -->
                        {% for option in question.options %}
                        <div class="form-check option-item">
                            <input class="form-check-input" type="radio" name="question_{{ question.id }}" 
                                id="option_{{ question.id }}_{{ option.id }}" value="{{ option.id }}" 
                                {% if question.required %}required{% endif %}>
                            <label class="form-check-label" for="option_{{ question.id }}_{{ option.id }}">
                                {{ option.content }}
                            </label>
                        </div>
                        {% endfor %}
                        
                        {% elif question.type == 'multiple_choice' %}
                        <!-- 多选题 -->
                        {% for option in question.options %}
                        <div class="form-check option-item">
                            <input class="form-check-input" type="checkbox" name="question_{{ question.id }}" 
                                id="option_{{ question.id }}_{{ option.id }}" value="{{ option.id }}">
                            <label class="form-check-label" for="option_{{ question.id }}_{{ option.id }}">
                                {{ option.content }}
                            </label>
                        </div>
                        {% endfor %}
                        {% endif %}
                    </div>
                </div>
                {% endfor %}

                <!-- 区块链信息提示 -->
                <div class="blockchain-info mb-4">
                    <h5><i class="fas fa-lock me-2"></i>数据安全保障</h5>
                    <p class="mb-0">您提交的所有答案将使用AES-256加密算法进行加密，并在区块链上存储哈希值，保证数据的完整性和不可篡改性。</p>
                </div>

                <!-- 提交按钮 -->
                <div class="d-grid gap-2 d-md-flex justify-content-md-end mb-5">
                    <a href="{{ url_for('survey.surveys') }}" class="btn btn-outline-secondary me-md-2">
                        <i class="fas fa-arrow-left me-1"></i> 返回列表
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane me-1"></i> 提交答案
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 表单验证
    (function() {
        'use strict';
        
        var forms = document.querySelectorAll('form');
        
        Array.prototype.slice.call(forms).forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                    
                    // 查找第一个无效的输入并滚动到那里
                    var firstInvalid = form.querySelector(':invalid');
                    if (firstInvalid) {
                        firstInvalid.scrollIntoView({behavior: 'smooth', block: 'center'});
                    }
                }
                
                form.classList.add('was-validated');
            }, false);
        });
    })();
</script>
{% endblock %} 