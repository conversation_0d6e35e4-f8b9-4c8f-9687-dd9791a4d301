from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, send_file
from flask_login import login_required, current_user
from datetime import datetime
import json
from io import BytesIO

from app import db
from app.models.survey import Survey, Question, Option, Response, Answer
from app.utils.helpers import sanitize_input, generate_survey_stats, generate_chart, export_survey_data
from app.blockchain.crypto import encrypt_data, decrypt_data
from app.blockchain.chain import add_transaction

survey_bp = Blueprint('survey', __name__)

@survey_bp.route('/surveys')
def surveys():
    """问卷列表"""
    active_surveys = Survey.query.filter_by(is_active=True).order_by(Survey.created_at.desc()).all()
    return render_template('survey/surveys.html', surveys=active_surveys)

@survey_bp.route('/survey/create', methods=['GET', 'POST'])
@login_required
def create_survey():
    """创建问卷"""
    if request.method == 'POST':
        # 获取基本信息
        title = sanitize_input(request.form.get('title', ''))
        description = sanitize_input(request.form.get('description', ''))
        expiry_date_str = request.form.get('expiry_date', '')
        is_anonymous = request.form.get('is_anonymous', False) == 'on'
        
        # 验证输入
        if not title:
            flash('问卷标题不能为空', 'danger')
            return render_template('survey/create_survey.html')
        
        # 处理过期日期
        expiry_date = None
        if expiry_date_str:
            try:
                expiry_date = datetime.strptime(expiry_date_str, '%Y-%m-%d')
            except ValueError:
                flash('日期格式无效', 'danger')
                return render_template('survey/create_survey.html')
        
        # 创建问卷
        survey = Survey(
            title=title,
            description=description,
            created_at=datetime.utcnow(),
            expiry_date=expiry_date,
            is_active=True,
            is_anonymous=is_anonymous,
            creator_id=current_user.id
        )
        
        db.session.add(survey)
        db.session.commit()
        
        flash('问卷创建成功，请添加问题', 'success')
        return redirect(url_for('survey.edit_survey', survey_id=survey.id))
    
    return render_template('survey/create_survey.html')

@survey_bp.route('/survey/<int:survey_id>')
def view_survey(survey_id):
    """查看问卷"""
    survey = Survey.query.get_or_404(survey_id)
    
    # 检查问卷是否已过期或不活跃
    if not survey.is_active or (survey.expiry_date and survey.is_expired()):
        flash('此问卷已不可用', 'info')
        return redirect(url_for('survey.surveys'))
    
    # 如果用户已经回答过此问卷，不允许再次填写
    if current_user.is_authenticated and Response.query.filter_by(
        survey_id=survey_id, user_id=current_user.id).first():
        flash('您已经回答过此问卷', 'info')
        return redirect(url_for('survey.survey_results', survey_id=survey_id))
    
    # 获取所有问题及选项
    questions = Question.query.filter_by(survey_id=survey_id).order_by(Question.order).all()
    
    return render_template('survey/view_survey.html', survey=survey, questions=questions)

@survey_bp.route('/survey/<int:survey_id>/submit', methods=['POST'])
def submit_survey(survey_id):
    """提交问卷回答"""
    survey = Survey.query.get_or_404(survey_id)
    
    # 检查问卷是否已过期或不活跃
    if not survey.is_active or (survey.expiry_date and survey.is_expired()):
        flash('此问卷已不可用', 'info')
        return redirect(url_for('survey.surveys'))
    
    # 如果要求登录但用户未登录
    if not survey.is_anonymous and not current_user.is_authenticated:
        flash('请先登录后再回答此问卷', 'info')
        return redirect(url_for('auth.login', next=url_for('survey.view_survey', survey_id=survey_id)))
    
    # 如果用户已经回答过此问卷，不允许再次填写
    if current_user.is_authenticated and Response.query.filter_by(
        survey_id=survey_id, user_id=current_user.id).first():
        flash('您已经回答过此问卷', 'info')
        return redirect(url_for('survey.survey_results', survey_id=survey_id))
    
    # 创建回应
    response = Response(
        survey_id=survey_id,
        user_id=current_user.id if current_user.is_authenticated else None,
        submitted_at=datetime.utcnow(),
        ip_address=request.remote_addr
    )
    
    db.session.add(response)
    db.session.commit()
    
    # 处理每个问题的答案
    questions = Question.query.filter_by(survey_id=survey_id).all()
    for question in questions:
        answer = Answer(
            response_id=response.id,
            question_id=question.id
        )
        
        if question.type == 'text':
            answer.text_answer = sanitize_input(request.form.get(f'question_{question.id}', ''))
        elif question.type == 'single_choice':
            option_id = request.form.get(f'question_{question.id}')
            if option_id:
                answer.set_selected_options(option_id)
        elif question.type == 'multiple_choice':
            option_ids = request.form.getlist(f'question_{question.id}')
            if option_ids:
                answer.set_selected_options(option_ids)
        
        db.session.add(answer)
    
    db.session.commit()
    
    # 将回答数据保存到区块链
    response.save_to_blockchain(survey.title + str(survey.id))
    
    flash('问卷提交成功，感谢您的参与', 'success')
    return redirect(url_for('survey.survey_results', survey_id=survey_id))

@survey_bp.route('/survey/<int:survey_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_survey(survey_id):
    """编辑问卷"""
    survey = Survey.query.get_or_404(survey_id)
    
    # 检查权限
    if survey.creator_id != current_user.id and not current_user.is_admin():
        flash('您没有权限编辑此问卷', 'danger')
        return redirect(url_for('survey.surveys'))
    
    if request.method == 'POST':
        # 获取基本信息
        title = sanitize_input(request.form.get('title', ''))
        description = sanitize_input(request.form.get('description', ''))
        expiry_date_str = request.form.get('expiry_date', '')
        is_active = request.form.get('is_active', False) == 'on'
        is_anonymous = request.form.get('is_anonymous', False) == 'on'
        
        # 验证输入
        if not title:
            flash('问卷标题不能为空', 'danger')
            return render_template('survey/edit_survey.html', survey=survey)
        
        # 处理过期日期
        expiry_date = None
        if expiry_date_str:
            try:
                expiry_date = datetime.strptime(expiry_date_str, '%Y-%m-%d')
            except ValueError:
                flash('日期格式无效', 'danger')
                return render_template('survey/edit_survey.html', survey=survey)
        
        # 更新问卷
        survey.title = title
        survey.description = description
        survey.expiry_date = expiry_date
        survey.is_active = is_active
        survey.is_anonymous = is_anonymous
        
        db.session.commit()
        
        flash('问卷更新成功', 'success')
        return redirect(url_for('survey.edit_survey', survey_id=survey.id))
    
    # 获取所有问题及选项
    questions = Question.query.filter_by(survey_id=survey_id).order_by(Question.order).all()
    
    return render_template('survey/edit_survey.html', survey=survey, questions=questions)

@survey_bp.route('/survey/<int:survey_id>/add_question', methods=['GET', 'POST'])
@login_required
def add_question(survey_id):
    """添加问题"""
    survey = Survey.query.get_or_404(survey_id)
    
    # 检查权限
    if survey.creator_id != current_user.id and not current_user.is_admin():
        flash('您没有权限编辑此问卷', 'danger')
        return redirect(url_for('survey.surveys'))
    
    if request.method == 'POST':
        # 获取问题信息
        content = sanitize_input(request.form.get('content', ''))
        question_type = request.form.get('type', '')
        required = request.form.get('required', False) == 'on'
        
        # 验证输入
        if not content or not question_type:
            flash('请填写所有必填字段', 'danger')
            return render_template('survey/add_question.html', survey=survey)
        
        # 获取问题顺序
        order = Question.query.filter_by(survey_id=survey_id).count() + 1
        
        # 创建问题
        question = Question(
            survey_id=survey_id,
            content=content,
            type=question_type,
            required=required,
            order=order
        )
        
        db.session.add(question)
        db.session.commit()
        
        # 对于选择题，需要添加选项
        if question_type in ['single_choice', 'multiple_choice']:
            return redirect(url_for('survey.edit_options', question_id=question.id))
        
        flash('问题添加成功', 'success')
        return redirect(url_for('survey.edit_survey', survey_id=survey_id))
    
    return render_template('survey/add_question.html', survey=survey)

@survey_bp.route('/question/<int:question_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_question(question_id):
    """编辑问题"""
    question = Question.query.get_or_404(question_id)
    survey = Survey.query.get(question.survey_id)
    
    # 检查权限
    if survey.creator_id != current_user.id and not current_user.is_admin():
        flash('您没有权限编辑此问卷', 'danger')
        return redirect(url_for('survey.surveys'))
    
    if request.method == 'POST':
        # 获取问题信息
        content = sanitize_input(request.form.get('content', ''))
        required = request.form.get('required', False) == 'on'
        
        # 验证输入
        if not content:
            flash('问题内容不能为空', 'danger')
            return render_template('survey/edit_question.html', question=question, survey=survey)
        
        # 更新问题
        question.content = content
        question.required = required
        
        db.session.commit()
        
        flash('问题更新成功', 'success')
        
        # 对于选择题，可以编辑选项
        if question.type in ['single_choice', 'multiple_choice']:
            return redirect(url_for('survey.edit_options', question_id=question.id))
        
        return redirect(url_for('survey.edit_survey', survey_id=survey.id))
    
    return render_template('survey/edit_question.html', question=question, survey=survey)

@survey_bp.route('/question/<int:question_id>/delete', methods=['POST'])
@login_required
def delete_question(question_id):
    """删除问题"""
    question = Question.query.get_or_404(question_id)
    survey = Survey.query.get(question.survey_id)
    
    # 检查权限
    if survey.creator_id != current_user.id and not current_user.is_admin():
        flash('您没有权限编辑此问卷', 'danger')
        return redirect(url_for('survey.surveys'))
    
    db.session.delete(question)
    
    # 更新其他问题的顺序
    other_questions = Question.query.filter(
        Question.survey_id == survey.id,
        Question.order > question.order
    ).all()
    
    for q in other_questions:
        q.order -= 1
    
    db.session.commit()
    
    flash('问题已删除', 'success')
    return redirect(url_for('survey.edit_survey', survey_id=survey.id))

@survey_bp.route('/question/<int:question_id>/options', methods=['GET', 'POST'])
@login_required
def edit_options(question_id):
    """编辑选项"""
    question = Question.query.get_or_404(question_id)
    survey = Survey.query.get(question.survey_id)
    
    # 检查权限
    if survey.creator_id != current_user.id and not current_user.is_admin():
        flash('您没有权限编辑此问卷', 'danger')
        return redirect(url_for('survey.surveys'))
    
    # 确保问题类型是选择题
    if question.type not in ['single_choice', 'multiple_choice']:
        flash('此问题不是选择题', 'danger')
        return redirect(url_for('survey.edit_survey', survey_id=survey.id))
    
    if request.method == 'POST':
        # 获取所有选项
        option_contents = request.form.getlist('option_content')
        
        # 删除现有选项
        for option in question.options.all():
            db.session.delete(option)
        
        # 添加新选项
        for i, content in enumerate(option_contents):
            if content.strip():  # 忽略空选项
                option = Option(
                    question_id=question.id,
                    content=sanitize_input(content),
                    order=i + 1
                )
                db.session.add(option)
        
        db.session.commit()
        
        flash('选项已更新', 'success')
        return redirect(url_for('survey.edit_survey', survey_id=survey.id))
    
    options = Option.query.filter_by(question_id=question_id).order_by(Option.order).all()
    return render_template('survey/edit_options.html', question=question, survey=survey, options=options)

@survey_bp.route('/survey/<int:survey_id>/results')
def survey_results(survey_id):
    """查看问卷结果"""
    survey = Survey.query.get_or_404(survey_id)
    
    # 如果问卷不是匿名的，检查权限
    if not survey.is_anonymous:
        # 如果用户未登录或既不是创建者也不是管理员
        if not current_user.is_authenticated or (
            survey.creator_id != current_user.id and 
            not current_user.is_admin() and
            not Response.query.filter_by(survey_id=survey_id, user_id=current_user.id).first()
        ):
            flash('您没有权限查看此问卷的结果', 'danger')
            return redirect(url_for('survey.surveys'))
    
    # 生成统计信息
    stats = generate_survey_stats(survey_id)
    
    return render_template('survey/results.html', survey=survey, stats=stats)

@survey_bp.route('/survey/<int:survey_id>/chart/<int:question_id>')
def question_chart(survey_id, question_id):
    """生成问题图表"""
    survey = Survey.query.get_or_404(survey_id)
    question = Question.query.get_or_404(question_id)
    
    # 如果问卷不是匿名的，检查权限
    if not survey.is_anonymous:
        if not current_user.is_authenticated or (
            survey.creator_id != current_user.id and 
            not current_user.is_admin() and
            not Response.query.filter_by(survey_id=survey_id, user_id=current_user.id).first()
        ):
            return jsonify({'error': '没有权限'}), 403
    
    # 确保问题类型是选择题
    if question.type not in ['single_choice', 'multiple_choice']:
        return jsonify({'error': '此问题不是选择题'}), 400
    
    # 获取统计信息
    stats = generate_survey_stats(survey_id)
    
    # 找到问题的统计信息
    question_stats = None
    for q_stat in stats['questions']:
        if q_stat['id'] == question_id:
            question_stats = q_stat
            break
    
    if not question_stats:
        return jsonify({'error': '未找到问题统计信息'}), 404
    
    # 返回原始数据用于echarts渲染
    return jsonify({
        'success': True,
        'question': question.content,
        'question_stats': question_stats,
        'chart_data': {
            'labels': [option['content'] for option in question_stats['options']],
            'values': [option['count'] for option in question_stats['options']],
            'percentages': [option['percentage'] for option in question_stats['options']],
            'xlabel': '选项',
            'ylabel': '回答数量'
        }
    })

@survey_bp.route('/survey/<int:survey_id>/export')
@login_required
def export_survey(survey_id):
    """导出问卷数据"""
    survey = Survey.query.get_or_404(survey_id)
    
    # 检查权限
    if survey.creator_id != current_user.id and not current_user.is_admin():
        flash('您没有权限导出此问卷的数据', 'danger')
        return redirect(url_for('survey.surveys'))
    
    format = request.args.get('format', 'csv')
    data, filename = export_survey_data(survey_id, format)
    
    if not data or not filename:
        flash('导出失败', 'danger')
        return redirect(url_for('survey.survey_results', survey_id=survey_id))
    
    # 发送文件
    if format == 'csv':
        return data, 200, {
            'Content-Type': 'text/csv',
            'Content-Disposition': f'attachment; filename={filename}'
        }
    elif format == 'excel':
        return send_file(
            BytesIO(data),
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )
    
    flash('不支持的导出格式', 'danger')
    return redirect(url_for('survey.survey_results', survey_id=survey_id)) 