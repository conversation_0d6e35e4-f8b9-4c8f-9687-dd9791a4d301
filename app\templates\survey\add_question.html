{% extends "base.html" %}

{% block title %}添加问题 - {{ survey.title }}{% endblock %}

{% block extra_css %}
<style>
    .card {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border-radius: 0.5rem;
    }
    .question-types-container {
        display: flex;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }
    .question-type-card {
        border: 2px solid #dee2e6;
        border-radius: 0.5rem;
        padding: 1rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        flex: 1;
    }
    .question-type-card:hover {
        border-color: #6c757d;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    .question-type-card.selected {
        border-color: #0d6efd;
        background-color: #f0f7ff;
    }
    .question-type-icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;
        color: #495057;
    }
    .selected .question-type-icon {
        color: #0d6efd;
    }
    .form-section {
        margin-top: 2rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <!-- 页面标题和操作 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h2 mb-0"><i class="fas fa-plus-circle me-2"></i>添加问题</h1>
                <a href="{{ url_for('survey.edit_survey', survey_id=survey.id) }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i> 返回问卷编辑
                </a>
            </div>
            
            <!-- 问卷信息提示 -->
            <div class="alert alert-info mb-4">
                <i class="fas fa-info-circle me-2"></i>
                <strong>正在向问卷添加问题: </strong> {{ survey.title }}
            </div>
            
            <!-- 添加问题表单 -->
            <div class="card">
                <div class="card-body">
                    <form method="POST" action="{{ url_for('survey.add_question', survey_id=survey.id) }}">
                        <!-- 问题内容 -->
                        <div class="mb-4">
                            <label for="content" class="form-label">问题内容*</label>
                            <textarea class="form-control" id="content" name="content" rows="3" required></textarea>
                            <div class="form-text">请输入清晰、具体的问题描述，便于受访者理解</div>
                        </div>
                        
                        <!-- 问题类型选择 -->
                        <div class="mb-4">
                            <label class="form-label">问题类型*</label>
                            <div class="question-types-container">
                                <div class="question-type-card" data-type="text" onclick="selectQuestionType('text')">
                                    <i class="fas fa-align-left question-type-icon"></i>
                                    <h5>文本题</h5>
                                    <p class="text-muted small">开放式问题，供受访者填写文字回答</p>
                                </div>
                                <div class="question-type-card" data-type="single_choice" onclick="selectQuestionType('single_choice')">
                                    <i class="fas fa-check-circle question-type-icon"></i>
                                    <h5>单选题</h5>
                                    <p class="text-muted small">从提供的选项中选择一个答案</p>
                                </div>
                                <div class="question-type-card" data-type="multiple_choice" onclick="selectQuestionType('multiple_choice')">
                                    <i class="fas fa-tasks question-type-icon"></i>
                                    <h5>多选题</h5>
                                    <p class="text-muted small">从提供的选项中选择一个或多个答案</p>
                                </div>
                            </div>
                            <input type="hidden" id="type" name="type" required>
                            <div class="form-text">选择题类型后，可以在下一步设置选项（适用于单选和多选题）</div>
                        </div>
                        
                        <!-- 必答设置 -->
                        <div class="mb-4">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="required" name="required">
                                <label class="form-check-label" for="required">必答题</label>
                            </div>
                            <div class="form-text">设置为必答题，受访者必须回答才能提交问卷</div>
                        </div>
                        
                        <!-- 提交按钮 -->
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="submit" class="btn btn-primary" id="submitBtn" disabled>
                                <i class="fas fa-plus-circle me-1"></i> 添加问题
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- 提示信息 -->
            <div class="alert alert-light mt-4">
                <h5><i class="fas fa-lightbulb me-2"></i>提示</h5>
                <ul>
                    <li>添加单选题或多选题后，将自动跳转到编辑选项页面</li>
                    <li>问题添加后可随时编辑或删除</li>
                    <li>合理的问题顺序可以提高问卷的填写体验</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function selectQuestionType(type) {
        // 移除所有卡片的选中状态
        document.querySelectorAll('.question-type-card').forEach(card => {
            card.classList.remove('selected');
        });
        
        // 给当前选中的卡片添加选中状态
        document.querySelector(`.question-type-card[data-type="${type}"]`).classList.add('selected');
        
        // 设置隐藏字段的值
        document.getElementById('type').value = type;
        
        // 启用提交按钮
        document.getElementById('submitBtn').disabled = false;
    }
    
    // 检查表单提交
    document.querySelector('form').addEventListener('submit', function(event) {
        var content = document.getElementById('content').value.trim();
        var type = document.getElementById('type').value;
        
        if (!content || !type) {
            event.preventDefault();
            alert('请填写问题内容并选择问题类型');
        }
    });
</script>
{% endblock %} 