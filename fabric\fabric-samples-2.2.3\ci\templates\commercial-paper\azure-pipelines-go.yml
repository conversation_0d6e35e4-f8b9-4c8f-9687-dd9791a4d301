#
# SPDX-License-Identifier: Apache-2.0
#

steps:
  - script: go test ./...
    workingDirectory: commercial-paper/organization/magnetocorp/contract-go
    displayName: Unit Test MagnetoCorp Chaincode
  - script: go test ./...
    workingDirectory: commercial-paper/organization/digibank/contract-go
    displayName: Unit Test DigiBank Chaincode

  - script: go mod vendor
    workingDirectory: commercial-paper/organization/magnetocorp/contract-go
    displayName: Vendor MagnetoCorp Dependencies
  - script: go mod vendor
    workingDirectory: commercial-paper/organization/digibank/contract-go
    displayName: Vendor DigiBank Dependencies

  - script: |
      ./network.sh up createChannel -ca -s couchdb -i ${FABRIC_VERSION} # FABRIC_VERSION is set in ci/azure-pipelines.yml

      # Copy the connection profiles so they are in the correct organizations.
      cp "./organizations/peerOrganizations/org1.example.com/connection-org1.yaml" "../commercial-paper/organization/digibank/gateway/"
      cp "./organizations/peerOrganizations/org2.example.com/connection-org2.yaml" "../commercial-paper/organization/magnetocorp/gateway/"
    workingDirectory: test-network
    displayName: Start Fabric
  - script: |
      source <(./magnetocorp.sh)
      peer lifecycle chaincode package cp.tar.gz --lang golang --path ./contract-go --label cp_0
      peer lifecycle chaincode install cp.tar.gz

      export PACKAGE_ID=$(peer lifecycle chaincode queryinstalled --output json | jq -r '.installed_chaincodes[0].package_id')
      echo $PACKAGE_ID

      peer lifecycle chaincode approveformyorg  --orderer localhost:7050 --ordererTLSHostnameOverride orderer.example.com \
                                                --channelID mychannel  \
                                                --name papercontract  \
                                                -v 0  \
                                                --package-id $PACKAGE_ID \
                                                --sequence 1  \
                                                --tls  \
                                                --cafile $ORDERER_CA

      peer lifecycle chaincode checkcommitreadiness --channelID mychannel --name papercontract -v 0 --sequence 1
    workingDirectory: commercial-paper/organization/magnetocorp
    displayName: Setup Commercial Paper Contract
  - script: |
      source <(./digibank.sh)
      peer lifecycle chaincode package cp.tar.gz --lang golang --path ./contract-go --label cp_0
      peer lifecycle chaincode install cp.tar.gz

      export PACKAGE_ID=$(peer lifecycle chaincode queryinstalled --output json | jq -r '.installed_chaincodes[0].package_id')
      echo $PACKAGE_ID

      peer lifecycle chaincode approveformyorg  --orderer localhost:7050 --ordererTLSHostnameOverride orderer.example.com \
                                                --channelID mychannel  \
                                                --name papercontract  \
                                                -v 0  \
                                                --package-id $PACKAGE_ID \
                                                --sequence 1  \
                                                --tls  \
                                                --cafile $ORDERER_CA

      peer lifecycle chaincode checkcommitreadiness --channelID mychannel --name papercontract -v 0 --sequence 1

      peer lifecycle chaincode commit -o localhost:7050 \
                                --peerAddresses localhost:7051 --tlsRootCertFiles ${PEER0_ORG1_CA} \
                                --peerAddresses localhost:9051 --tlsRootCertFiles ${PEER0_ORG2_CA} \
                                --ordererTLSHostnameOverride orderer.example.com \
                                --channelID mychannel --name papercontract -v 0 \
                                --sequence 1 \
                                --tls --cafile $ORDERER_CA --waitForEvent

    workingDirectory: commercial-paper/organization/digibank
    displayName: Setup Commercial Paper Contract

  - script: retry -- npm install
    workingDirectory: commercial-paper/organization/magnetocorp/application
    displayName: Install Magnetocorp Application
  - script: |
      set -ex
      node enrollUser.js
      node issue.js
    workingDirectory: commercial-paper/organization/magnetocorp/application
    displayName: MagnetoCorp Issue Paper

  - script: retry -- npm install
    workingDirectory: commercial-paper/organization/digibank/application
    displayName: Install DigiBank Application
  - script: |
      set -ex
      node enrollUser.js
      node buy.js
      node redeem.js
    workingDirectory: commercial-paper/organization/digibank/application
    displayName: Digibank Issue Paper
