# 基于区块链的应急管理问卷调查系统 - 数据库ER图

```mermaid
erDiagram
    USER ||--o{ SURVEY : 创建
    USER ||--o{ RESPONSE : 提交
    SURVEY ||--o{ QUESTION : 包含
    SURVEY ||--o{ RESPONSE : 收集
    QUESTION ||--o{ OPTION : 包含
    QUESTION ||--o{ ANSWER : 回答
    RESPONSE ||--o{ ANSWER : 包含
    RESPONSE ||--|| TRANSACTION : 记录到区块链
    BLOCK ||--o{ TRANSACTION : 包含

    USER {
        int id PK
        string username UK
        string email UK
        string password_hash
        string role
        boolean is_active
        datetime created_at
        datetime last_login
    }
    
    SURVEY {
        int id PK
        string title
        text description
        datetime created_at
        datetime expiry_date
        boolean is_active
        boolean is_anonymous
        int creator_id FK
    }
    
    QUESTION {
        int id PK
        int survey_id FK
        text content
        string type
        boolean required
        int order
    }
    
    OPTION {
        int id PK
        int question_id FK
        text content
        int order
    }
    
    RESPONSE {
        int id PK
        int survey_id FK
        int user_id FK
        datetime submitted_at
        string ip_address
        string blockchain_hash
    }
    
    ANSWER {
        int id PK
        int response_id FK
        int question_id FK
        text text_answer
        text selected_options
    }
    
    TRANSACTION {
        int id PK
        string hash UK
        int block_id FK
        text data
        datetime timestamp
    }
    
    BLOCK {
        int id PK
        int index UK
        datetime timestamp
        text data
        string previous_hash
        string hash UK
        int nonce
    }
```

## 实体说明

### User（用户）
- 系统用户，分为管理员和普通用户
- 可以创建问卷和回答问卷

### Survey（问卷）
- 由用户创建的调查问卷
- 包含标题、描述、有效期等信息
- 可以设置为匿名或非匿名

### Question（问题）
- 问卷中的问题
- 包含不同类型：单选、多选、文本
- 可设置是否必答

### Option（选项）
- 针对单选和多选问题的选项

### Response（问卷回复）
- 用户对问卷的一次完整回复
- 包含提交时间、IP地址等信息
- 通过区块链哈希与区块链交易关联

### Answer（回答）
- 用户对单个问题的回答
- 根据问题类型存储不同形式的答案

### Block（区块）
- 区块链中的区块
- 包含索引、时间戳、数据、哈希等信息

### Transaction（交易）
- 区块链中的交易记录
- 存储加密的问卷回复数据

## 关系说明

1. 一个用户可以创建多个问卷
2. 一个用户可以提交多个问卷回复
3. 一个问卷包含多个问题
4. 一个问卷收集多个回复
5. 一个问题可以包含多个选项（针对选择题）
6. 一个问题对应多个回答（来自不同用户）
7. 一个问卷回复包含多个回答
8. 一个问卷回复通过区块链哈希关联到一个区块链交易
9. 一个区块包含多个交易 