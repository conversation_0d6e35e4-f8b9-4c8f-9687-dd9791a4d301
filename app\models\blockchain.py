from datetime import datetime
from app import db

class Block(db.Model):
    """区块模型"""
    __tablename__ = 'blocks'

    id = db.Column(db.Integer, primary_key=True)
    index = db.Column(db.Integer, nullable=False, unique=True)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    data = db.Column(db.Text)  # 存储加密后的交易数据
    previous_hash = db.Column(db.String(64), nullable=False)
    hash = db.Column(db.String(64), nullable=False, unique=True)
    nonce = db.Column(db.Integer, nullable=False)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'index': self.index,
            'timestamp': self.timestamp.isoformat(),
            'data': self.data,
            'previous_hash': self.previous_hash,
            'hash': self.hash,
            'nonce': self.nonce
        }
    
    def __repr__(self):
        return f'<Block {self.index}>'

class Transaction(db.Model):
    """交易模型"""
    __tablename__ = 'transactions'

    id = db.Column(db.Integer, primary_key=True)
    hash = db.Column(db.String(64), nullable=False, unique=True)
    block_id = db.Column(db.Integer, db.ForeignKey('blocks.id'))
    data = db.Column(db.Text, nullable=False)  # 加密数据
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关系
    block = db.relationship('Block', backref='transactions')
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'hash': self.hash,
            'block_id': self.block_id,
            'timestamp': self.timestamp.isoformat()
        }
    
    def __repr__(self):
        return f'<Transaction {self.hash[:8]}...>' 