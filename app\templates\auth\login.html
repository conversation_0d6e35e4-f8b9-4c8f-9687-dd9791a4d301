{% extends "base.html" %}

{% block title %}登录 - 基于区块链的应急管理问卷调查系统{% endblock %}

{% block extra_css %}
<style>
    .login-card {
        max-width: 500px;
        margin: 50px auto;
        border-radius: 10px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    .login-header {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 10px 10px 0 0;
        text-align: center;
    }
    .login-body {
        padding: 30px;
    }
    .form-floating {
        margin-bottom: 20px;
    }
    .login-footer {
        text-align: center;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 0 0 10px 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="login-card">
            <div class="login-header">
                <h2><i class="fas fa-sign-in-alt me-2"></i>用户登录</h2>
                <p class="text-muted">请输入您的账号信息登录系统</p>
            </div>
            <div class="login-body">
                <form method="POST" action="{{ url_for('auth.login') }}">
                    <div class="form-floating">
                        <input type="email" class="form-control" id="email" name="email" placeholder="邮箱地址" required>
                        <label for="email">邮箱地址</label>
                    </div>
                    <div class="form-floating">
                        <input type="password" class="form-control" id="password" name="password" placeholder="密码" required>
                        <label for="password">密码</label>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="remember" name="remember">
                        <label class="form-check-label" for="remember">记住我</label>
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">登录</button>
                    </div>
                </form>
            </div>
            <div class="login-footer">
                <p>还没有账号？ <a href="{{ url_for('auth.register') }}">立即注册</a></p>
            </div>
        </div>
    </div>
</div>
{% endblock %} 