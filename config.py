import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """基本配置类"""
    SECRET_KEY = os.environ.get('SECRET_KEY', 'default_secret_key')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    DEBUG = os.environ.get('DEBUG', 'False').lower() == 'true'
    
    # 数据库配置
    DB_USERNAME = os.environ.get('DB_USERNAME', 'root')
    DB_PASSWORD = os.environ.get('DB_PASSWORD', 'password')
    DB_HOST = os.environ.get('DB_HOST', 'localhost')
    DB_PORT = os.environ.get('DB_PORT', '3306')
    DB_NAME = os.environ.get('DB_NAME', 'emergency_survey')
    
    # SQLAlchemy数据库URI
    SQLALCHEMY_DATABASE_URI = f'mysql+pymysql://{DB_USERNAME}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}'
    
    # 区块链配置
    BLOCKCHAIN_DIFFICULTY = int(os.environ.get('BLOCKCHAIN_DIFFICULTY', 4))
    BLOCKCHAIN_MINING_REWARD = int(os.environ.get('BLOCKCHAIN_MINING_REWARD', 10))
    BLOCKCHAIN_SECRET_KEY = os.environ.get('BLOCKCHAIN_SECRET_KEY', 'default_blockchain_key')
    
    # 应用配置
    ADMIN_EMAIL = os.environ.get('ADMIN_EMAIL', '<EMAIL>')
    ADMIN_PASSWORD = os.environ.get('ADMIN_PASSWORD', 'admin_password')

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False

# 配置字典
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
} 