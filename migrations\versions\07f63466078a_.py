"""empty message

Revision ID: 07f63466078a
Revises: 
Create Date: 2025-04-22 01:31:29.776015

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '07f63466078a'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('answers', schema=None) as batch_op:
        batch_op.create_foreign_key(None, 'responses', ['response_id'], ['id'])
        batch_op.create_foreign_key(None, 'questions', ['question_id'], ['id'])

    with op.batch_alter_table('options', schema=None) as batch_op:
        batch_op.create_foreign_key(None, 'questions', ['question_id'], ['id'])

    with op.batch_alter_table('questions', schema=None) as batch_op:
        batch_op.create_foreign_key(None, 'surveys', ['survey_id'], ['id'])

    with op.batch_alter_table('responses', schema=None) as batch_op:
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'])
        batch_op.create_foreign_key(None, 'surveys', ['survey_id'], ['id'])

    with op.batch_alter_table('surveys', schema=None) as batch_op:
        batch_op.create_foreign_key(None, 'users', ['creator_id'], ['id'])

    with op.batch_alter_table('transactions', schema=None) as batch_op:
        batch_op.create_foreign_key(None, 'blocks', ['block_id'], ['id'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('transactions', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')

    with op.batch_alter_table('surveys', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')

    with op.batch_alter_table('responses', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')

    with op.batch_alter_table('questions', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')

    with op.batch_alter_table('options', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')

    with op.batch_alter_table('answers', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')

    # ### end Alembic commands ###
