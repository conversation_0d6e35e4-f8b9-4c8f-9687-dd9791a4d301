from datetime import datetime
from flask_login import UserMixin
from app import db, bcrypt, login_manager

class User(db.Model, UserMixin):
    """用户模型"""
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(128), nullable=False)
    role = db.Column(db.String(20), default='user')  # admin, user
    is_active = db.Column(db.<PERSON><PERSON><PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    
    # 关系
    surveys = db.relationship('Survey', backref='creator', lazy='dynamic')
    responses = db.relationship('Response', backref='respondent', lazy='dynamic')
    
    @property
    def password(self):
        """密码属性不允许读取"""
        raise AttributeError('密码不是可读属性')
        
    @password.setter
    def password(self, password):
        """设置密码时自动加密"""
        self.password_hash = bcrypt.generate_password_hash(password).decode('utf-8')
        
    def verify_password(self, password):
        """验证密码"""
        return bcrypt.check_password_hash(self.password_hash, password)
    
    def is_admin(self):
        """检查用户是否是管理员"""
        return self.role == 'admin'
    
    def update_last_login(self):
        """更新最后登录时间"""
        self.last_login = datetime.utcnow()
        db.session.commit()
    
    def __repr__(self):
        return f'<User {self.username}>'

@login_manager.user_loader
def load_user(user_id):
    """Flask-Login用户加载函数"""
    return User.query.get(int(user_id)) 