#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
区块链数据修复脚本
解决区块链JSON格式错误和ECharts 3D初始化问题
"""

import json
import os
import sys
from flask import Flask
from app import create_app, db
from app.models.blockchain import Block, Transaction
from app.blockchain.crypto import calculate_hash
from app.blockchain.chain import get_blockchain

# 创建应用实例
app = create_app()

def fix_blockchain():
    """重新计算并修复区块链哈希"""
    with app.app_context():
        print("开始修复区块链...")
        blocks = Block.query.order_by(Block.index).all()
        
        if not blocks:
            print("区块链为空，无需修复")
            return False
        
        # 获取区块链实例，读取难度设置
        blockchain = get_blockchain()
        difficulty = blockchain.difficulty
        
        fixed = False
        
        # 检查并修复每个区块
        for i in range(1, len(blocks)):
            current_block = blocks[i]
            previous_block = blocks[i-1]
            
            # 验证前一个哈希是否正确
            if current_block.previous_hash != previous_block.hash:
                print(f"区块 #{current_block.index} 中的前一个哈希不正确，正在修复...")
                current_block.previous_hash = previous_block.hash
                fixed = True
            
            # 验证并修复当前区块的哈希值
            computed_hash = calculate_hash(str(current_block.data) + 
                                          current_block.previous_hash + 
                                          str(current_block.nonce))
            
            if computed_hash != current_block.hash:
                print(f"区块 #{current_block.index} 的哈希值不正确，正在修复...")
                print(f"存储哈希: {current_block.hash}")
                print(f"计算哈希: {computed_hash}")
                
                # 使用工作量证明重新计算正确的哈希和nonce
                nonce = 0
                fixed_hash = calculate_hash(str(current_block.data) + 
                                          current_block.previous_hash + 
                                          str(nonce))
                
                # 执行工作量证明，找到满足难度的nonce
                while not fixed_hash.startswith('0' * difficulty):
                    nonce += 1
                    fixed_hash = calculate_hash(str(current_block.data) + 
                                              current_block.previous_hash + 
                                              str(nonce))
                
                # 更新区块
                current_block.hash = fixed_hash
                current_block.nonce = nonce
                fixed = True
                
                print(f"已修复。新哈希: {fixed_hash}")
                print(f"新Nonce: {nonce}")
        
        if fixed:
            # 提交修改
            db.session.commit()
            print("区块链修复完成！")
            return True
        else:
            print("区块链未发现问题，无需修复")
            return False

def fix_blockchain_data():
    """修复区块链数据中的JSON格式问题"""
    with app.app_context():
        # 获取所有区块
        blocks = Block.query.order_by(Block.index).all()
        
        fixed_count = 0
        for block in blocks:
            # 检查数据是否是有效的JSON
            if block.data:
                try:
                    # 尝试解析数据
                    if isinstance(block.data, str):
                        json.loads(block.data)
                except json.JSONDecodeError:
                    # 如果解析失败，将数据转换为有效的JSON格式
                    print(f"修复区块 #{block.index} 的数据格式")
                    # 尝试修复常见的JSON错误
                    try:
                        # 尝试去除可能导致问题的特殊字符
                        cleaned_data = block.data.replace("'", '"').replace('\n', '').replace('\r', '')
                        json.loads(cleaned_data)  # 验证是否有效
                        block.data = cleaned_data
                        fixed_count += 1
                    except:
                        # 如果仍然无法修复，将其设置为空数组
                        print(f"无法修复区块 #{block.index} 的数据，将其设置为空数组")
                        block.data = "[]"
                        fixed_count += 1
                
        # 保存更改
        if fixed_count > 0:
            db.session.commit()
            print(f"成功修复了 {fixed_count} 个区块的数据格式")
        else:
            print("没有发现需要修复的区块数据")

def create_fix_js():
    """创建一个JavaScript文件，修复ECharts 3D初始化问题"""
    js_content = """
// 区块链信息页面的JSON解析修复
document.addEventListener('DOMContentLoaded', function() {
    // 安全地解析区块链数据
    try {
        // 检查区块链数据是否已正确加载
        if (typeof blockchainData === 'undefined' || !blockchainData) {
            console.warn('区块链数据未定义或为空，尝试从页面获取');
            
            // 尝试从页面中提取数据
            var dataScript = document.querySelector('script[data-blockchain-data]');
            if (dataScript) {
                try {
                    window.blockchainData = JSON.parse(dataScript.getAttribute('data-blockchain-data'));
                    console.log('成功从页面加载区块链数据');
                } catch (e) {
                    console.error('解析页面区块链数据失败:', e);
                    window.blockchainData = [];
                }
            } else {
                window.blockchainData = [];
            }
        }
    } catch (e) {
        console.error('区块链数据初始化错误:', e);
        window.blockchainData = [];
    }
    
    // 修复ECharts 3D初始化
    var originalInit3D = window.init3DBlockchainChart;
    
    if (typeof originalInit3D === 'function') {
        window.init3DBlockchainChart = function() {
            try {
                // 检查基本支持
                var chartDom = document.getElementById('blockchainChart3D');
                if (!chartDom) return;
                
                // 初始化简化的3D图表
                var myChart = echarts.init(chartDom);
                
                // 简单的图表选项
                var option = {
                    series: [{
                        type: 'scatter3D',
                        data: [[0, 0, 0]],
                        symbolSize: 10
                    }]
                };
                
                // 测试图表
                myChart.setOption(option);
                myChart.clear();
                
                // 如果测试成功，调用原始函数
                originalInit3D();
            } catch (e) {
                console.error('3D图表初始化失败:', e);
                
                // 显示错误信息
                if (chartDom) {
                    chartDom.innerHTML = '<div class="alert alert-warning">' +
                        '<i class="fas fa-exclamation-triangle me-2"></i>' +
                        '3D可视化无法加载: ' + e.message +
                        '</div>';
                }
                
                // 显示备用2D可视化
                var fallback = document.getElementById('fallback2DVisualization');
                if (fallback) fallback.style.display = 'block';
            }
        };
        
        console.log('已应用3D图表初始化修复');
    }
});
    """
    
    # 创建静态目录（如果不存在）
    static_dir = os.path.join(app.root_path, 'static', 'js')
    os.makedirs(static_dir, exist_ok=True)
    
    # 写入JavaScript文件
    js_path = os.path.join(static_dir, 'blockchain_fix.js')
    with open(js_path, 'w') as f:
        f.write(js_content)
    
    print(f"已创建修复脚本: {js_path}")
    print("请在 blockchain_info.html 模板中添加以下代码:")
    print('<script src="{{ url_for(\'static\', filename=\'js/blockchain_fix.js\') }}"></script>')

def modify_template():
    """修改区块链信息模板，添加修复脚本和数据属性"""
    template_path = os.path.join(app.root_path, 'templates', 'main', 'blockchain_info.html')
    
    if not os.path.exists(template_path):
        print(f"未找到模板文件: {template_path}")
        return
    
    # 读取模板文件
    with open(template_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查修复脚本是否已添加
    if 'blockchain_fix.js' in content:
        print("修复脚本已添加到模板中")
    else:
        # 查找 extra_js 块的结束位置
        extra_js_end = content.find('{% endblock %}', content.find('{% block extra_js %}'))
        
        if extra_js_end > 0:
            # 在块结束前添加脚本
            new_content = content[:extra_js_end] + '\n<script src="{{ url_for(\'static\', filename=\'js/blockchain_fix.js\') }}"></script>\n' + content[extra_js_end:]
            
            # 添加数据属性
            data_script = '\n<script data-blockchain-data="{{ blocks|tojson|safe }}"></script>\n'
            new_content = new_content.replace('{% block content %}', '{% block content %}' + data_script)
            
            # 写回文件
            with open(template_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("已成功修改模板，添加了修复脚本和数据属性")
        else:
            print("无法找到模板中的 extra_js 块，请手动添加修复脚本")

if __name__ == '__main__':
    print("开始修复区块链数据...")
    fix_blockchain_data()
    
    print("\n创建JavaScript修复脚本...")
    create_fix_js()
    
    print("\n修改区块链信息模板...")
    modify_template()
    
    print("\n修复完成！请重启应用以应用更改。") 