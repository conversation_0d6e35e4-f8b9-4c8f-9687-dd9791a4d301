import hashlib
import json
import base64
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
from Crypto.Random import get_random_bytes

def calculate_hash(data):
    """
    计算数据的SHA-256哈希值
    
    参数:
        data: 要哈希的数据(字符串)
    
    返回:
        哈希值(字符串)
    """
    if isinstance(data, dict):
        # 确保字典以确定的顺序序列化为JSON
        data = json.dumps(data, sort_keys=True)
    elif not isinstance(data, str):
        data = str(data)
    
    # 计算SHA-256哈希值
    return hashlib.sha256(data.encode('utf-8')).hexdigest()

def encrypt_data(data, key):
    """
    使用AES加密数据
    
    参数:
        data: 要加密的数据(字符串)
        key: 加密密钥(字符串)
    
    返回:
        加密后的数据(base64编码的字符串)
    """
    # 生成密钥
    key_bytes = hashlib.sha256(key.encode('utf-8')).digest()
    
    # 生成随机IV
    iv = get_random_bytes(16)
    
    # 创建AES加密器
    cipher = AES.new(key_bytes, AES.MODE_CBC, iv)
    
    # 加密数据
    padded_data = pad(data.encode('utf-8'), AES.block_size)
    encrypted_data = cipher.encrypt(padded_data)
    
    # 将IV和加密数据合并并Base64编码
    result = base64.b64encode(iv + encrypted_data).decode('utf-8')
    
    return result

def decrypt_data(encrypted_data, key):
    """
    使用AES解密数据
    
    参数:
        encrypted_data: 加密的数据(base64编码的字符串)
        key: 解密密钥(字符串)
    
    返回:
        解密后的数据(字符串)
    """
    # 生成密钥
    key_bytes = hashlib.sha256(key.encode('utf-8')).digest()
    
    # Base64解码
    raw_data = base64.b64decode(encrypted_data)
    
    # 分离IV和数据
    iv = raw_data[:16]
    encrypted_data = raw_data[16:]
    
    # 创建AES解密器
    cipher = AES.new(key_bytes, AES.MODE_CBC, iv)
    
    # 解密数据
    padded_data = cipher.decrypt(encrypted_data)
    data = unpad(padded_data, AES.block_size)
    
    return data.decode('utf-8')

def verify_signature(data, signature, public_key):
    """
    验证数字签名
    
    参数:
        data: 原始数据
        signature: 签名
        public_key: 公钥
    
    返回:
        验证结果(布尔值)
    """
    # 这里只是一个简单的实现，实际应用中应使用更复杂的数字签名算法如RSA或ECDSA
    computed_hash = calculate_hash(data + public_key)
    return signature == computed_hash 