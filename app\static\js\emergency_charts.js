/**
 * 应急管理区块链可视化图表
 * 包含应急类型分布、应急响应时间等图表
 */

// 应急管理类型分布图
function createEmergencyTypesChart(containerId) {
    var chartDom = document.getElementById(containerId);
    if (!chartDom) return null;
    
    var myChart = echarts.init(chartDom);
    
    // 请求数据
    fetch('/api/emergency/types_stats')
        .then(response => response.json())
        .then(responseData => {
            if (responseData.success) {
                const data = responseData.data;
                
                // 处理数据
                const typeData = data.map(item => ({
                    name: item.type_name,
                    value: item.count
                }));
                
                // 图表配置
                var option = {
                    title: {
                        text: '应急事件类型统计',
                        left: 'center'
                    },
                    tooltip: {
                        trigger: 'item',
                        formatter: '{a} <br/>{b}: {c} ({d}%)'
                    },
                    legend: {
                        orient: 'horizontal',
                        bottom: 'bottom',
                        data: typeData.map(item => item.name)
                    },
                    // 添加工具箱配置
                    toolbox: {
                        feature: {
                            saveAsImage: { title: '保存为图片' },
                            restore: { title: '还原' },
                            dataView: { title: '数据视图', readOnly: true }
                        }
                    },
                    series: [
                        {
                            name: '应急事件类型',
                            type: 'pie',
                            radius: ['40%', '70%'],
                            avoidLabelOverlap: true,
                            itemStyle: {
                                borderRadius: 10,
                                borderColor: '#fff',
                                borderWidth: 2
                            },
                            label: {
                                show: true,
                                formatter: '{b}: {c} ({d}%)'
                            },
                            emphasis: {
                                label: {
                                    show: true,
                                    fontSize: '18',
                                    fontWeight: 'bold'
                                },
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            },
                            labelLine: {
                                show: true
                            },
                            data: typeData
                        }
                    ]
                };
                
                // 设置配置并渲染图表
                myChart.setOption(option);
                
                // 响应窗口大小变化
                window.addEventListener('resize', function() {
                    myChart.resize();
                });
            }
        })
        .catch(error => {
            console.error("Error fetching emergency types data:", error);
        });
    
    return myChart;
}

// 应急响应时间分布图
function createEmergencyResponseChart(containerId) {
    var chartDom = document.getElementById(containerId);
    if (!chartDom) return null;
    
    var myChart = echarts.init(chartDom);
    
    // 请求数据
    fetch('/api/emergency/response_stats')
        .then(response => response.json())
        .then(responseData => {
            if (responseData.success) {
                const data = responseData.data;
                
                // 处理数据
                const xAxisData = data.map(item => item.category);
                const avgTimeData = data.map(item => parseFloat(item.avg_response_time.toFixed(2)));
                const countData = data.map(item => item.count);
                
                // 图表配置
                var option = {
                    title: {
                        text: '应急响应速度统计',
                        left: 'center'
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow'
                        },
                        formatter: function(params) {
                            const avgTime = params[0].value;
                            const count = params[1].value;
                            return `<strong>${params[0].name}</strong><br/>` +
                                   `平均响应时间: <strong>${avgTime}</strong> 小时<br/>` +
                                   `事件数量: <strong>${count}</strong> 起`;
                        }
                    },
                    legend: {
                        data: ['平均响应时间(小时)', '事件数量'],
                        top: 'bottom'
                    },
                    // 添加工具箱配置
                    toolbox: {
                        feature: {
                            saveAsImage: { title: '保存为图片' },
                            restore: { title: '还原' },
                            dataZoom: { 
                                title: { zoom: '区域缩放', back: '区域缩放还原' },
                                yAxisIndex: 'none'
                            },
                            dataView: { title: '数据视图', readOnly: true }
                        }
                    },
                    // 添加数据缩放组件
                    dataZoom: [
                        {
                            type: 'inside',
                            start: 0,
                            end: 100,
                            xAxisIndex: [0]
                        },
                        {
                            type: 'slider',
                            start: 0,
                            end: 100,
                            xAxisIndex: [0],
                            height: 20
                        }
                    ],
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '15%', // 增加底部空间放置缩放控件
                        containLabel: true
                    },
                    xAxis: [
                        {
                            type: 'category',
                            data: xAxisData,
                            axisLabel: {
                                interval: 0,
                                rotate: 30
                            }
                        }
                    ],
                    yAxis: [
                        {
                            type: 'value',
                            name: '响应时间(小时)',
                            min: 0,
                            axisLabel: {
                                formatter: '{value} h'
                            }
                        },
                        {
                            type: 'value',
                            name: '事件数量',
                            min: 0,
                            axisLabel: {
                                formatter: '{value}'
                            }
                        }
                    ],
                    series: [
                        {
                            name: '平均响应时间(小时)',
                            type: 'bar',
                            data: avgTimeData,
                            itemStyle: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                    {offset: 0, color: '#83bff6'},
                                    {offset: 0.5, color: '#188df0'},
                                    {offset: 1, color: '#0b5ea8'}
                                ])
                            },
                            emphasis: {
                                itemStyle: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {offset: 0, color: '#6aa5e5'},
                                        {offset: 0.7, color: '#1678db'},
                                        {offset: 1, color: '#0b51a8'}
                                    ])
                                }
                            }
                        },
                        {
                            name: '事件数量',
                            type: 'line',
                            yAxisIndex: 1,
                            symbol: 'circle',
                            symbolSize: 8,
                            itemStyle: {
                                color: '#e74c3c'
                            },
                            lineStyle: {
                                width: 3,
                                color: '#e74c3c'
                            },
                            data: countData
                        }
                    ]
                };
                
                // 设置配置并渲染图表
                myChart.setOption(option);
                
                // 响应窗口大小变化
                window.addEventListener('resize', function() {
                    myChart.resize();
                });
            }
        })
        .catch(error => {
            console.error("Error fetching emergency response data:", error);
        });
    
    return myChart;
}

// 问卷响应区域分布 (柱状图)
function createSurveyRegionChart(chartDomId) {
  const chartDom = document.getElementById(chartDomId);
  if (!chartDom) return;
  
  const myChart = echarts.init(chartDom);
  
  const option = {
    title: {
      text: '应急管理问卷区域分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      formatter: '{b}: {c}份'
    },
    xAxis: {
      type: 'category',
      data: ['华北', '华东', '华南', '西南', '西北', '东北', '华中'],
      axisLabel: {
        interval: 0
      }
    },
    yAxis: {
      type: 'value',
      name: '问卷数量'
    },
    series: [
      {
        type: 'bar',
        data: [850, 950, 750, 650, 450, 550, 680],
        itemStyle: {
          color: function(params) {
            // 根据数值设置不同颜色
            const colors = ['#e74c3c', '#f39c12', '#3498db', '#2ecc71', '#9b59b6', '#1abc9c', '#34495e'];
            return colors[params.dataIndex];
          }
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}份'
        }
      }
    ]
  };
  
  myChart.setOption(option);
  window.addEventListener('resize', function() {
    myChart.resize();
  });
  
  return myChart;
}

// 应急问卷调查完成率对比
function createSurveyCompletionChart(chartDomId) {
  const chartDom = document.getElementById(chartDomId);
  if (!chartDom) return;
  
  const myChart = echarts.init(chartDom);
  
  const option = {
    title: {
      text: '问卷完成率对比',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['发布数量', '完成数量', '完成率'],
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: ['自然灾害', '事故灾难', '公共卫生', '社会安全', '其他']
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '数量',
        position: 'left'
      },
      {
        type: 'value',
        name: '完成率',
        min: 0,
        max: 100,
        position: 'right',
        axisLabel: {
          formatter: '{value}%'
        }
      }
    ],
    series: [
      {
        name: '发布数量',
        type: 'bar',
        stack: 'survey',
        emphasis: {
          focus: 'series'
        },
        data: [42, 35, 28, 15, 5]
      },
      {
        name: '完成数量',
        type: 'bar',
        stack: 'survey',
        emphasis: {
          focus: 'series'
        },
        data: [35, 28, 22, 12, 3]
      },
      {
        name: '完成率',
        type: 'line',
        yAxisIndex: 1,
        data: [83, 80, 79, 80, 60],
        label: {
          show: true,
          formatter: '{c}%'
        }
      }
    ]
  };
  
  myChart.setOption(option);
  window.addEventListener('resize', function() {
    myChart.resize();
  });
  
  return myChart;
}

// 初始化所有应急管理相关图表
function initEmergencyCharts() {
  // 初始化基础图表
  createEmergencyTypesChart('emergency-types-chart');
  createEmergencyResponseChart('emergency-response-chart');
  
  // 初始化可选的其他图表（如果存在相应的DOM元素）
  if (document.getElementById('survey-region-chart')) {
    createSurveyRegionChart('survey-region-chart');
  }
  
  if (document.getElementById('survey-completion-chart')) {
    createSurveyCompletionChart('survey-completion-chart');
  }
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
  // 确保在ECharts库加载完成后才初始化图表
  if (typeof echarts !== 'undefined') {
    initEmergencyCharts();
  } else {
    console.error('ECharts库未加载，无法初始化应急管理图表');
  }
}); 